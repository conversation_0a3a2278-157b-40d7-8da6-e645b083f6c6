import * as React from 'react'
import {
  <PERSON>,
  Container,
  Column,
  Head,
  <PERSON>ing,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Text,
} from '@react-email/components'
import { DateTime } from 'luxon'

interface EmailProps {
  otp: string
  expiresAt: DateTime
  type: 'login' | 'registration' | 'update_email'
  duration: number
}

const gomama_logo_path =
  'https://storage.googleapis.com/gomama-prod.appspot.com/1/2021/10/Site-icon.png'
const fb_logo_path = 'https://gajiplus.vercel.app/Facebook.png'
const linkedin_logo_path = 'https://gajiplus.vercel.app/LinkedIn.png'
const instagram_logo_path = 'https://gajiplus.vercel.app/Instagram.png'
const tiktok_logo_path =
  'https://static.wixstatic.com/media/11062b_7fc95bac711041dcb9691b6a09192a84~mv2.png/v1/fill/w_45,h_45,al_c,q_85,usm_0.66_1.00_0.01,enc_auto/11062b_7fc95bac711041dcb9691b6a09192a84~mv2.png'
const img_alt = 'Gomama'

export const Email = ({ otp, expiresAt, type, duration }: EmailProps) => {
  let preview = ''
  let heading = ''
  let content = ''
  let hint = ''
  const formatted_expires_at = expiresAt.setZone('Asia/Singapore').toFormat('dd LLL yyyy hh:mm a')

  switch (type) {
    case 'login':
      preview = 'Login with your email address'
      heading = 'Login with your email address'
      content = `Your confirmation code is below - Copy & Paste it into the login OTP field. This OTP will expire after ${duration} minutes on ${formatted_expires_at}`
      hint = `If you didn't request this email, please be aware that your email address is being used to log in to the Gomama app.`
      break
    case 'registration':
      preview = 'Confirm your email address'
      heading = 'Confirm your email address'
      content = `Your confirmation code is below - Copy & Paste it into the registration OTP field. This OTP will expire after ${duration} minutes on ${formatted_expires_at}`
      hint = `If you didn't request this email, there's nothing to worry about - you can safely ignore it.`
      break
    case 'update_email':
      preview = 'Confirm your email address'
      heading = 'Confirm your email address'
      content = `Your confirmation code is below - Copy & Paste it into the OTP field to update your latest email. This OTP will expire after ${duration} minutes on ${formatted_expires_at}`
      hint = `If you didn't request this email, there's nothing to worry about - you can safely ignore it.`
      break
    default:
      break
  }

  return (
    <Html>
      <Head />
      <Preview>{preview}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img src={gomama_logo_path} width="90" height="90" alt={img_alt} />
          </Section>
          <Heading style={h1}>{heading}</Heading>
          <Text style={heroText}>{content}</Text>

          <Section style={codeBox}>
            <Text style={confirmationCodeText}>{otp}</Text>
          </Section>

          <Text style={text}>{hint}</Text>

          <Section>
            <Row style={footerLogos}>
              <Column style={{ width: '70%' }}>
                <Img src={gomama_logo_path} width="90" height="90" alt={img_alt} />
              </Column>
              <Column>
                <Row>
                  <Column>
                    <Link href="https://www.instagram.com/gomama.official/">
                      <Img
                        src={instagram_logo_path}
                        width="32"
                        height="32"
                        alt={img_alt}
                        style={socialMediaIcon}
                      />
                    </Link>
                  </Column>
                  <Column>
                    <Link href="https://www.facebook.com/gomama.official">
                      <Img
                        src={fb_logo_path}
                        width="32"
                        height="32"
                        alt={img_alt}
                        style={socialMediaIcon}
                      />
                    </Link>
                  </Column>
                  <Column>
                    <Link href="https://www.linkedin.com/company/go-mama/about/">
                      <Img
                        src={linkedin_logo_path}
                        width="32"
                        height="32"
                        alt={img_alt}
                        style={socialMediaIcon}
                      />
                    </Link>
                  </Column>
                  <Column>
                    <Link href="https://www.tiktok.com/@gomama.official">
                      <Img
                        src={tiktok_logo_path}
                        width="32"
                        height="32"
                        alt={img_alt}
                        style={socialMediaIcon}
                      />
                    </Link>
                  </Column>
                </Row>
              </Column>
            </Row>
          </Section>

          <Section>
            <Link
              style={footerLink}
              href="https://www.gomama.com.sg/about"
              target="_blank"
              rel="noopener noreferrer"
            >
              About
            </Link>
            &nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
            <Link
              style={footerLink}
              href="https://www.gomama.com.sg/FAQ"
              target="_blank"
              rel="noopener noreferrer"
            >
              FAQ
            </Link>
            <Text style={footerText}>
              © 2024 Go!mama Pte. Ltd. <br />
              <br />
              All rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  )
}

const footerText = {
  fontSize: '12px',
  color: '#b7b7b7',
  lineHeight: '15px',
  textAlign: 'left' as const,
  marginBottom: '50px',
}

const footerLink = {
  color: '#b7b7b7',
  textDecoration: 'underline',
}

const footerLogos = {
  marginBottom: '32px',
  paddingLeft: '8px',
  paddingRight: '8px',
  width: '100%',
}

const socialMediaIcon = {
  // display: 'inline',
  // marginLeft: '32px',
}

const main = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
}

const container = {
  maxWidth: '600px',
  margin: '0 auto',
}

const logoContainer = {
  marginTop: '32px',
}

const h1 = {
  color: '#1d1c1d',
  fontSize: '36px',
  fontWeight: '700',
  margin: '30px 0',
  padding: '0',
  lineHeight: '42px',
}

const heroText = {
  fontSize: '20px',
  lineHeight: '28px',
  marginBottom: '30px',
}

const codeBox = {
  background: 'rgb(245, 244, 245)',
  borderRadius: '4px',
  marginRight: '50px',
  marginBottom: '30px',
  padding: '43px 23px',
}

const confirmationCodeText = {
  fontSize: '30px',
  textAlign: 'center' as const,
  verticalAlign: 'middle',
}

const text = {
  color: '#000',
  fontSize: '14px',
  lineHeight: '24px',
}
