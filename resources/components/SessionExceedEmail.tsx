import * as React from 'react'
import {
  Body,
  Container,
  Column,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Text,
} from '@react-email/components'
import { DateTime, Interval } from 'luxon'
import Listing from 'App/Models/Listing'
import User from 'App/Models/User'

interface SessionExceedEmailProps {
  exceededSessionsInfo: {
    user: User
    startedAt: DateTime
    expectedEndedAt: DateTime
    sessionId: string
    listing: Listing
  }[]
  timeNow: DateTime
}

const gomama_logo_path =
  'https://storage.googleapis.com/gomama-prod.appspot.com/1/2021/10/Site-icon.png'
const fb_logo_path = 'https://gajiplus.vercel.app/Facebook.png'
const linkedin_logo_path = 'https://gajiplus.vercel.app/LinkedIn.png'
const instagram_logo_path = 'https://gajiplus.vercel.app/Instagram.png'
const tiktok_logo_path =
  'https://static.wixstatic.com/media/11062b_7fc95bac711041dcb9691b6a09192a84~mv2.png/v1/fill/w_45,h_45,al_c,q_85,usm_0.66_1.00_0.01,enc_auto/11062b_7fc95bac711041dcb9691b6a09192a84~mv2.png'
const img_alt = 'Gomama'

export const SessionExceedEmail = ({ exceededSessionsInfo, timeNow }: SessionExceedEmailProps) => {
  const preview = 'Latest User(s) stays in the pod for more than Expected End Time'
  const heading = 'Latest User(s) stays in the pod for more than Expected End Time'
  const content = `There are total of ${
    exceededSessionsInfo.length
  } user(s) in the pod for more than the expected end time right now at ${DateTime.now()
    .setZone('Asia/Singapore')
    .toFormat('dd LLL yyyy hh:mm a')}. The details are below:`
  const hint = `This is a system-generated email.`
  const getSessionTimeDiff = (indexOfSession: number) => {
    return Interval.fromDateTimes(exceededSessionsInfo[indexOfSession].expectedEndedAt, timeNow)
  }

  return (
    <Html>
      <Head />
      <Preview>{preview}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img src={gomama_logo_path} width="90" height="90" alt={img_alt} />
          </Section>
          <Heading style={h1}>{heading}</Heading>
          <Text style={heroText}>{content}</Text>
          <div style={tableContainerStyle}>
            <table style={tableStyle}>
              <thead style={tableHeaderStyle}>
                <tr>
                  <th style={thStyle}>User Info</th>
                  <th style={thStyle}>Listing Info</th>
                  <th style={thStyle}>Session ID</th>
                  <th style={thStyle}>Time Info</th>
                </tr>
              </thead>
              <tbody>
                {exceededSessionsInfo.map((session, index) => (
                  <>
                    <tr key={index} style={index % 2 === 0 ? tableRowEvenStyle : tableRowOddStyle}>
                      <td style={tableCellStyle}>
                        {session.user.fullName}{' '}
                        <>
                          <br />
                          <br />
                        </>
                        {session.user.emailAddress ? session.user.emailAddress : ''}{' '}
                        {session.user.emailAddress && (
                          <>
                            <br />
                            <br />
                          </>
                        )}
                        {session.user.fullMobileNumber ? session.user.fullMobileNumber : ''}{' '}
                        {session.user.fullMobileNumber && (
                          <>
                            <br />
                            <br />
                          </>
                        )}
                        {session.user.id}
                        <br />
                      </td>
                      <td style={tableCellStyle}>
                        {session.listing.name}{' '}
                        <>
                          <br />
                          <br />
                        </>
                        {session.listing.firestoreId}{' '}
                        <>
                          <br />
                          <br />
                        </>
                        {session.listing.addressName}
                        <>
                          <br />
                          <br />
                        </>
                      </td>
                      <td style={tableCellStyle}>{session.sessionId}</td>
                      <td style={tableCellStyle}>
                        Start Time:{' '}
                        {session.startedAt
                          .setZone('Asia/Singapore')
                          .toFormat('dd LLL yyyy hh:mm a')}
                        <>
                          <br />
                          <br />
                        </>
                        Expected End Time:{' '}
                        {session.expectedEndedAt
                          .setZone('Asia/Singapore')
                          .toFormat('dd LLL yyyy hh:mm a')}{' '}
                        <>
                          <br />
                          <br />
                        </>
                        Exceeded Time:{' '}
                        {getSessionTimeDiff(index).length('minutes')
                          ? `${getSessionTimeDiff(index).length('minutes').toFixed(0)} minute(s)`
                          : '-'}{' '}
                        <>
                          <br />
                          <br />
                        </>
                        <br />
                      </td>
                    </tr>
                  </>
                ))}
              </tbody>
            </table>
          </div>
          <Text style={text}>{hint}</Text>
          <Section>
            <Row style={footerLogos}>
              <Column style={{ width: '70%' }}>
                <Img src={gomama_logo_path} width="90" height="90" alt={img_alt} />
              </Column>
              <Column>
                <Row>
                  <Column>
                    <Link href="https://www.instagram.com/gomama.official/">
                      <Img
                        src={instagram_logo_path}
                        width="32"
                        height="32"
                        alt={img_alt}
                        style={socialMediaIcon}
                      />
                    </Link>
                  </Column>
                  <Column>
                    <Link href="https://www.facebook.com/gomama.official">
                      <Img
                        src={fb_logo_path}
                        width="32"
                        height="32"
                        alt={img_alt}
                        style={socialMediaIcon}
                      />
                    </Link>
                  </Column>
                  <Column>
                    <Link href="https://www.linkedin.com/company/go-mama/about/">
                      <Img
                        src={linkedin_logo_path}
                        width="32"
                        height="32"
                        alt={img_alt}
                        style={socialMediaIcon}
                      />
                    </Link>
                  </Column>
                  <Column>
                    <Link href="https://www.tiktok.com/@gomama.official">
                      <Img
                        src={tiktok_logo_path}
                        width="32"
                        height="32"
                        alt={img_alt}
                        style={socialMediaIcon}
                      />
                    </Link>
                  </Column>
                </Row>
              </Column>
            </Row>
          </Section>
          <Section>
            <Link
              style={footerLink}
              href="https://www.gomama.com.sg/about"
              target="_blank"
              rel="noopener noreferrer"
            >
              About
            </Link>
            &nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
            <Link
              style={footerLink}
              href="https://www.gomama.com.sg/FAQ"
              target="_blank"
              rel="noopener noreferrer"
            >
              FAQ
            </Link>
            <Text style={footerText}>
              © 2024 Go!mama Pte. Ltd. <br />
              <br />
              All rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  )
}

// TABLE COMPONENTS
const tableContainerStyle = {
  maxHeight: '400px',
  overflowX: 'hidden' as const,
  overflowY: 'auto' as const,
  border: '1px solid #e5e7eb',
  borderRadius: '4px',
}

const tableStyle = {
  width: '100%',
  borderCollapse: 'collapse' as const,
}

const tableHeaderStyle = {
  position: 'sticky' as const,
  top: 0,
  backgroundColor: '#f3f4f6',
  zIndex: 1,
}

const thStyle = {
  padding: '10px',
  textAlign: 'left' as const,
  borderBottom: '1px solid #e5e7eb',
  fontSize: '14px',
}

const tableRowEvenStyle = {
  backgroundColor: '#ffffff',
}

const tableRowOddStyle = {
  backgroundColor: '#f9fafb',
}

const tableCellStyle = {
  padding: '10px',
  borderBottom: '1px solid #e5e7eb',
  fontSize: '14px',
}

// EMAIL OTHER COMPONENTS
const footerText = {
  fontSize: '12px',
  color: '#b7b7b7',
  lineHeight: '15px',
  textAlign: 'left' as const,
  marginBottom: '50px',
}

const footerLink = {
  color: '#b7b7b7',
  textDecoration: 'underline',
}

const footerLogos = {
  marginBottom: '32px',
  paddingLeft: '8px',
  paddingRight: '8px',
  width: '100%',
}

const socialMediaIcon = {
  // display: 'inline',
  // marginLeft: '32px',
}

const main = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
}

const container = {
  maxWidth: '600px',
  margin: '0 auto',
}

const logoContainer = {
  marginTop: '32px',
}

const h1 = {
  color: '#1d1c1d',
  fontSize: '36px',
  fontWeight: '700',
  margin: '30px 0',
  padding: '0',
  lineHeight: '42px',
}

const heroText = {
  fontSize: '20px',
  lineHeight: '28px',
  marginBottom: '30px',
}

const codeBox = {
  background: 'rgb(245, 244, 245)',
  borderRadius: '4px',
  marginRight: '50px',
  marginBottom: '30px',
  padding: '43px 23px',
}

const confirmationCodeText = {
  fontSize: '30px',
  textAlign: 'center' as const,
  verticalAlign: 'middle',
}

const text = {
  color: '#000',
  fontSize: '14px',
  lineHeight: '24px',
}
