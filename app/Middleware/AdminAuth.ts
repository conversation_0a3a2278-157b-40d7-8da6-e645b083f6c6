import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { AuthenticationException } from '@adonisjs/auth/build/standalone'
// import UserType from 'App/Models/UserType'
import { UserType } from 'Contracts/user_type'

export default class AdminAuthMiddleware {
  protected async authenticateAdmin(auth: HttpContextContract['auth']) {
    const user = await auth.authenticate()
    // const adminUserType = await UserType.findBy('name', 'Admin') // Admin user type

    // if (user.typeId == adminUserType?.id) return true

    if (user.userType == UserType.admin) return true

    /**
     * Unable to authenticate as an Admin
     */
    throw new AuthenticationException(
      'Unauthorized access, route can only be accessed by admin',
      'E_UNAUTHORIZED_ACCESS'
    )
  }

  public async handle({ auth }: HttpContextContract, next: () => Promise<void>) {
    // code for middleware goes here. ABOVE THE NEXT CALL
    await this.authenticateAdmin(auth)
    await next()
  }
}
