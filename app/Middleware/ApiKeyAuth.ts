import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { AuthenticationException } from '@adonisjs/auth/build/standalone'
import Env from '@ioc:Adonis/Core/Env'

export default class ApiKeyAuthMiddleware {
  protected async authenticate(request: HttpContextContract['request']) {
    // Get API key from request header
    const apiKey = request.header('x-api-key')

    // Check if API key is provided
    if (!apiKey) {
      throw new AuthenticationException('API key is missing', 'E_MISSING_API_KEY')
    }

    // Check if API key is valid
    if (apiKey !== Env.get('API_KEY')) {
      throw new AuthenticationException('Invalid API key', 'E_INVALID_API_KEY')
    }

    return true
  }

  public async handle({ request }: HttpContextContract, next: () => Promise<void>) {
    await this.authenticate(request)
    await next()
  }
}
