import Filter from 'bad-words'
import fs from 'fs'
import App from '@ioc:Adonis/Core/Application'

const badwordtxt = process.env.BADWORD_TXT_FILE

export function statementFilter(review: string): string {
  if (!badwordtxt) {
    console.log('Badword txt file dir not found')
    return review
  }

  if (review !== undefined || review !== null || review !== '') {
    const badwords = fs.readFileSync(App.publicPath(badwordtxt)).toString()
    const filterConfig = {
      list: [...badwords.split('\r\n')],
    }
    const filter = new Filter(filterConfig)

    // 3. Check if the review is unclean.
    if (filter.isProfane(review)) {
      // 4. The review is unclean.
      const reviewOriginal = review
      review = filter.clean(review)
      const updateListingRating = {
        review: review,
        review_original: reviewOriginal,
      }
      return updateListingRating.review
    }
  }
  return review
}
