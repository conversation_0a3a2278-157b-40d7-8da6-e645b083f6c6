import Database from '@ioc:Adonis/Lucid/Database'
import Position from 'App/Models/Position'
import { v4 as uuidv4 } from 'uuid'
import * as ngeohash from 'ngeohash'

/**
 * Generate random code
 * @param {number} length of random code
 * @returns {string} Generated random code.
 */
export async function generateRandomPosition(num: number): Promise<Position[]> {
  const positionArr: Position[] = []

  let regenPosition = true
  while (regenPosition) {
    const lon = Math.random() * (180 - -180 + -180)
    const lat = Math.random() * (90 - -90 + -90)
    const geohash = ngeohash.encode(lon, lat, 20)
    const newPositionId = uuidv4()
    const result = await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId, geohash, lon, lat]
    )

    if (result[0].affectedRows > 0) {
      const position = await Position.findOrFail(newPositionId)
      positionArr.push(position)
      if (positionArr.length == num) {
        regenPosition = false
        return positionArr
      }
    }
  }

  return positionArr
}
