import crypto from 'crypto'
import * as jose from 'node-jose'
import jwt from 'jsonwebtoken'
import _ from 'lodash'
import { DateTime } from 'luxon'
import Env from '@ioc:Adonis/Core/Env'
import ListingFile from 'App/Models/ListingFile'
import Drive from '@ioc:Adonis/Core/Drive'
import { TransactionClientContract } from '@ioc:Adonis/Lucid/Database'
import Listing from 'App/Models/Listing'
import User from 'App/Models/User'
import { MultipartFileContract } from '@ioc:Adonis/Core/BodyParser'
import * as querystring from 'querystring'
import { rules } from '@ioc:Adonis/Core/Validator'
import { writeFile, mkdir } from 'fs/promises'
import Application from '@ioc:Adonis/Core/Application'
import { join } from 'path'
import { PhoneNumberUtil } from 'google-libphonenumber'
import * as isoCountry from 'i18n-iso-countries'
import MeilisearchService from 'App/Services/meilisearch'

export const baseUrl = Env.get('BASE_URL')
export const baseUrlAdmin = Env.get('BASE_URL_ADMIN')
export const imageFileSpec = {
  size: '10mb',
  extnames: ['jpg', 'png', 'jpeg', 'JPG', 'PNG', 'JPEG'],
}
export const s3Endpoint = Env.get('S3_ENDPOINT')
export const s3PublicEndpoint = Env.get('S3_PUBLIC_ENDPOINT')
export const s3PrivateEndpoint = Env.get('S3_PRIVATE_ENDPOINT')
export const remotePathListing = Env.get('LISTING_IMAGE_URL')
export const remotePathActivity = Env.get('ACTIVITY_IMAGE_URL')
export const remotePathRegion = Env.get('REGION_IMAGE_URL')
export const remotePathUserProfile = Env.get('USER_PROFILE_IMAGE_URL')
export const remotePathUserVerify = Env.get('USER_VERIFY_IMAGE_URL')
export const remotePathListingFlag = Env.get('LISTING_FLAG_IMAGE_URL')
export const remotePathNotificationMessage = Env.get('NOTIFICATION_MESSAGE_IMAGE_URL')
export const disk_name = Env.get('PUBLIC_DRIVE_DISK')
export const protocols = ['https', 'http', 'data', '/']
export const protocolRegex = new RegExp(`(${protocols.join('|')})`, 'i')
export const varcharRule = [rules.minLength(1), rules.maxLength(255)]
/**
 * Generate random code
 * @param {number} length of random code
 * @returns {string} Generated random code.
 */
export function generateRandomCode(length: number): string {
  let result = ''
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const charactersLength = characters.length
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result
}

/**
 * Generate random code
 * @param {number} length of random code
 * @returns {string} Generated random code.
 */
export function generateOtp(length: number): string {
  let result = ''
  const characters = '0123456789'
  const charactersLength = characters.length
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result
}

/**
 * Generate random number
 * @param {number} length of random number.
 * @return {number} Generated random number.
 */
export function generateRandomNumber(length: number): number {
  let last: null | number = null
  let repeat = 0

  if (typeof length == 'undefined') length = 15

  const now = Math.pow(10, 2) * +new Date()

  if (now == last) {
    repeat++
  } else {
    repeat = 0
    last = now
  }

  const s = (now + repeat).toString()
  return +s.substring(s.length - length)
}

/**
 *
 * @param {string} apiUrl - URL without query string.
 * @example```https://www.google.com```
 *
 * @param {{ [key: string]: string }} queryParams - An object of the query parameters.
 * @example```{ id: 1, firstName: "First", lastName: "Last" }```
 *
 * @returns {string} A complete URL with appended query parameters.
 * @example```https://www.google.com?id=1&first_name=First&last_name=Last```
 */
export function serializeEncodeURI(apiUrl: string, queryParams: { [key: string]: string }): string {
  const keyParams = Object.keys(queryParams)
  if (keyParams.length == 0) {
    return apiUrl
  }
  const queryParamsArr: Array<string> = []
  for (let key of keyParams) {
    const snakeCaseKey = _.snakeCase(key)
    queryParamsArr.push(`${snakeCaseKey}=${queryParams[key]}`)
  }

  const appendedQueryString = queryParamsArr.join('&')
  const encodeFullApiUrl = encodeURI(`${apiUrl}?${appendedQueryString}`)

  return encodeFullApiUrl
}

/**
 *
 * @returns Generate an array that can be used for Authorization Code Flow with PKCE.
 * @example const [codeVerifier, codeChallenge] = generatePkcePairValue()
 */
export const generatePkcePairValue = () => {
  const codeVerifier = generateRandomCode(255)
  const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64')
  const codeChallengeMethod = 'S256'

  return [codeVerifier, codeChallenge, codeChallengeMethod]
}

/**
 *
 * @returns Generate an array that can be used for cipher encryption and decryption.
 * @example const [secretKey, iv] = generateCipherKey()
 */
export const generateCipherKey = () => {
  const secret = crypto.randomBytes(32).toString('hex')
  const iv = crypto.randomBytes(16).toString('hex')

  return [secret, iv]
}

type ephemeralKeyPair = {
  publicKey: string
  privateKey: string
}

/**
 * Generate key pair
 *
 * This method will generate a keypair which consists of an elliptic curve public key and private key in PEM format.
 *
 * @returns {Object} - Returns an object which consists of a public key and private key
 */
export const generateEphemeralKeyPair = async (): Promise<ephemeralKeyPair> => {
  let options: crypto.ECKeyPairOptions<'pem', 'pem'> = {
    namedCurve: 'P-384',
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem',
    },
    privateKeyEncoding: {
      type: 'sec1',
      format: 'pem',
    },
  }
  let sessionKeyPair = crypto.generateKeyPairSync('ec', options)
  return sessionKeyPair
}

export type GenerateClientAssertionOptions = {
  alg: jwt.Algorithm
  clientId: string
  aud: string
  exp?: number
  privateSigningKey: string
  jktThumbprint: string
}

/**
 *
 * @param {GenerateClientAssertionOptions} options
 * @returns {string}
 */
export const generateClientAssertion = async ({
  alg,
  clientId,
  aud,
  privateSigningKey,
  jktThumbprint,
}: GenerateClientAssertionOptions): Promise<string> => {
  const now = DateTime.now()
  const jwtClaims: jwt.JwtPayload = {
    sub: clientId,
    jti: generateRandomCode(40),
    aud: aud,
    iss: clientId,
    iat: now.toUnixInteger(),
    exp: now.plus({ minutes: 2 }).toUnixInteger(),
    cnf: {
      jkt: jktThumbprint,
    },
  }

  const privateKey = await jose.importSPKI(privateSigningKey, 'ES256')
  const signJwt = new jose.SignJWT(jwtClaims)
  signJwt.setProtectedHeader({ typ: 'JWT', alg: alg })

  const jwtAssertion = await signJwt.sign(privateKey)
  return jwtAssertion
}

/**
 *
 * @param url - The URL of the audience for /token or /person API.
 * @param method - POST for /token, GET for /person
 * @param ephemeralKeyPair - Session ephemeral key pair used to sign DPoP.
 * @param ath [ath] - Access token hash. Get from /token API. (Required for /person)
 * @returns - Returns the DPoP token.
 */
export const generateDPoP = async (
  url: string,
  method: 'GET' | 'POST',
  ephemeralKeyPair: ephemeralKeyPair,
  ath?: string
): Promise<string> => {
  const now = DateTime.now()
  const jwsPayload = {
    htu: url,
    htm: method,
    jti: generateRandomCode(40),
    iat: now.toUnixInteger(),
    exp: now.plus({ minutes: 2 }).toUnixInteger(),
  }

  if (ath) {
    jwsPayload['ath'] = ath
  }

  const privateKey = await jose.importSPKI(ephemeralKeyPair.privateKey, 'ES256')
  const jwk = await jose.exportJWK(await jose.importSPKI(ephemeralKeyPair.publicKey, 'ES256'))
  jwk.use = 'sig'
  jwk.alg = 'ES256'

  const signJws = new jose.CompactSign(Buffer.from(JSON.stringify(jwsPayload), 'utf-8'))
  signJws.setProtectedHeader({ typ: 'dpop+jwt', jwk: jwk, alg: 'ES256' })
  const dpop = await signJws.sign(privateKey)
  return dpop
}

/**
 *
 * @param {string} message - Message to encrypt.
 * @param {string} secretHex - Secret key in hexadecimal for encryption.
 * @param {string} ivHex - Initialization vector in hexadecimal for encryption.
 * @returns {string} Encrypted message in hexadecimal.
 */
export const cipherEncryption = (message: string, secretHex: string, ivHex: string) => {
  // 1. retrieve secret and iv from env
  const secret = Buffer.from(secretHex, 'hex')
  const iv = Buffer.from(ivHex, 'hex')

  // 2. encrypt text
  const cipher = crypto.createCipheriv('aes-256-cbc', secret, iv)
  cipher.update(message, 'utf-8', 'hex')
  let encryptedText = cipher.final('hex')
  return encryptedText
}

/**
 *
 * @param {string} encryptedText - Encrypted message to decrypt.
 * @param {string} secretHex - Secret key in hexadecimal for encryption.
 * @param {string} ivHex - Initialization vector in hexadecimal for encryption.
 * @returns {string} Decrypted message in UTF-8
 */
export const cipherDecryption = (encryptedText, secretHex: string, ivHex: string) => {
  // 1. retrieve secret and iv from env
  const secret = Buffer.from(secretHex, 'hex')
  const iv = Buffer.from(ivHex, 'hex')

  // 2. decrypt text
  const decipher = crypto.createDecipheriv('aes-256-cbc', secret, iv)
  decipher.update(encryptedText, 'hex', 'utf-8')
  let decryptedText = decipher.final('utf-8')
  return decryptedText
}

/**
 *
 * @returns Singpass configuration
 */
export const getSingpassConfig = () => {
  return {
    clientId: process.env.SINGPASS_CLIENT_ID as string,
    clientSecret: process.env.SINGPASS_CLIENT_SECRET as string,
    pkiCompanyPrivateKey: process.env.SINGPASS_PKI_COMPANY_PRIVATE_KEY as string,
    pkiMyInfoPublicCert: process.env.SINGPASS_PKI_MYINFO_PUBLIC_CERT as string,
    scope: process.env.SINGPASS_SCOPE as string,
    grantType: process.env.SINGPASS_GRANT_TYPE as string,
    myinfoUrl: process.env.SINGPASS_MYINFO_URL as string,
    purpose: process.env.SINGPASS_PURPOSE as string,
    redirectUri: process.env.SINGPASS_REDIRECT_URI as string,
  }
}

/**
 *
 * @param {string} spki - Key to generate thumbprint in PEM format.
 * @returns {string} - Returns JWK thumbprint.
 */
export const generateJWKThumbprint = async (spki: string): Promise<string> => {
  const keyLike = await jose.importSPKI(spki, 'ES256')
  const jwk = await jose.exportJWK(keyLike)
  const jwkThumbprint = await jose.calculateJwkThumbprint(jwk, 'sha256')

  return jwkThumbprint
}

// Used by create new listing (all image are new)
/**
 *
 * @param {User} user - Encrypted message to decrypt.
 * @param {Listing} listing - Secret key in hexadecimal for encryption.
 * @param {TransactionClientContract} trx - Database Transaction Client.
 * @param {boolean} is_admin_route - Determine if this is called from an admin route.
 * @param {MultipartFileContract | undefined} main_image_file - The main image for the listing in file format.
 * @param {string | undefined} main_image_url - The main image for the listing in URL string format.
 * @param {MultipartFileContract | undefined} sub_image_files - An array for all sub images for the listing in file format.
 * @param {string[] | undefined} sub_image_urls - An array for all sub images for the listing in URL string format.
 * @returns {void}
 */
export async function newImageHandler(
  user: User,
  listing: Listing,
  trx: TransactionClientContract,
  is_admin_route: boolean,
  main_image_file: MultipartFileContract | undefined,
  main_image_url: string | undefined,
  sub_image_files: MultipartFileContract[] | undefined,
  sub_image_urls: string[] | undefined
): Promise<void> {
  // Main Image
  if (main_image_file || main_image_url) {
    // Image file is prioritize if both file and url present
    // Schema validation ensure either new_image_file or new_image_url value is presented
    const new_main_image = await ListingFile.create(
      {
        listingId: listing.id,
        uploadedBy: user.id,
        isApproved: is_admin_route,
        isHidden: !is_admin_route,
        isMain: true,
      },
      { client: trx }
    )

    if (main_image_file) {
      // Upload image file
      const remoteName = `${listing.id}_${generateRandomCode(10)}.${main_image_file.extname}`
      await main_image_file.moveToDisk(remotePathListing, { name: remoteName }, disk_name)
      new_main_image.imageUrl = `${remoteName}`
    } else if (main_image_url) {
      // Upload image's url endpoint
      new_main_image.imageUrl = main_image_url
    }

    await new_main_image.save()
  }

  // Sub Images
  if (sub_image_files || sub_image_urls) {
    // Upload image file
    // No prioritize for sub images, all urls and file provided will create a new listing file
    if (sub_image_files) {
      let filesToCreate: Partial<ListingFile>[] = []

      for await (const [, sub_image_file] of sub_image_files.entries()) {
        const remoteName = `${listing.id}_${generateRandomCode(10)}.${sub_image_file.extname}`
        await sub_image_file.moveToDisk(remotePathListing, { name: remoteName }, disk_name)
        filesToCreate.push({
          listingId: listing.id,
          uploadedBy: user.id,
          isApproved: is_admin_route,
          isHidden: !is_admin_route,
          imageUrl: remoteName,
        })
      }

      await ListingFile.createMany(filesToCreate, { client: trx })
    }

    // Upload image's url endpoint
    if (sub_image_urls) {
      await ListingFile.createMany(
        sub_image_urls.map((sub_image_url) => {
          return {
            listingId: listing.id,
            uploadedBy: user.id,
            isApproved: is_admin_route,
            isHidden: !is_admin_route,
            imageUrl: sub_image_url,
          }
        }),
        { client: trx }
      )
    }
  }
}

// Used by update existing listing (image can be updating old one or create new)
/**
 *
 * @param {User} user - Encrypted message to decrypt.
 * @param {Listing} listing - Secret key in hexadecimal for encryption.
 * @param {TransactionClientContract} trx - Database Transaction Client.
 * @param {MultipartFileContract | undefined} main_image_id - The existing main image's id for the listing in file format.
 * @param {MultipartFileContract | undefined} main_image_file - The main image for the listing in file format.
 * @param {string | undefined} main_image_url - The main image for the listing in URL string format.
 * @param {string[] | undefined} sub_image_ids - An array for all sub images' id for the listing (When only provides 2 id, which means only updates 2 existing images, the extra image source provided under sub_image_files or sub_image_urls will be treated as add new images).
 * @param {MultipartFileContract | undefined} sub_image_files - An array for all sub images for the listing in file format.
 * @param {string[] | undefined} sub_image_urls - An array for all sub images for the listing in URL string format.
 * @returns {void}
 */
export async function updateImageHandler(
  listing: Listing,
  user: User,
  trx: TransactionClientContract,
  main_image_id: string | undefined,
  main_image_file: MultipartFileContract | undefined,
  main_image_url: string | undefined,
  sub_image_ids: string[] | undefined,
  sub_image_files: MultipartFileContract[] | undefined,
  sub_image_urls: string[] | undefined
): Promise<void | { status: string; msg: string }> {
  // 1. Validate all potential conflicts before update

  // Main Image
  if (main_image_id) {
    // If main image id is provided, means its to update the main image
    const isAssociatedMainImage = await ListingFile.query()
      .where('listing_id', listing.id)
      .andWhere('id', main_image_id)
      .andWhere('is_main', true)
      .first()

    // Validate if the main image associated with the mentioned listing.
    if (!isAssociatedMainImage) {
      return {
        status: 'conflict',
        msg: 'Please provide the main image file id that are associated with the mentioned listing only. If the mentioned listing does not have any main image yet, this field is not needed',
      }
    }

    // Validate if at least file or url is provided for the main image update
    if (!main_image_file && !main_image_url) {
      return {
        status: 'unprocessableEntity',
        msg: 'Please provide a main image file or main image url for updates',
      }
    }

    // Validation done, valid main image, proceed update main image
    await Drive.use(disk_name).delete(
      remotePathListing + isAssociatedMainImage.imageUrl.split(remotePathListing)[1]
    ) // Delete old image file, regardless previously have image file or not (no error prompt if file is not found using the url)
    if (main_image_file) {
      const remoteName = `${listing.id}_${generateRandomCode(10)}.${main_image_file.extname}`
      await main_image_file.moveToDisk(remotePathListing, { name: remoteName }, disk_name)
      isAssociatedMainImage.imageUrl = `${remoteName}`
    } else if (main_image_url) {
      isAssociatedMainImage.imageUrl = main_image_url
    }

    await isAssociatedMainImage.useTransaction(trx).save()
  } else if (main_image_file || main_image_url) {
    // If main image id is not provided AND main image file/main image url is provided, means its to create a new main image
    const mainIsExist = await ListingFile.query()
      .where('listing_id', listing.id)
      .andWhere('is_main', true)
      .first()
    // Validate if there is already an existing main image for mentioned listing (Each listing can only have 1 main image)
    if (mainIsExist) {
      return {
        status: 'conflict',
        msg: 'The mentioned listing already has a main image, please provide the main image id for updates',
      }
    }

    const newMainImage = await ListingFile.create(
      {
        listingId: listing.id,
        isApproved: true,
        isHidden: false,
        isMain: true,
        reviewedBy: user.id,
        uploadedBy: user.id,
      },
      { client: trx }
    )

    // Validation done, no previous main image, proceed create new main image
    if (main_image_file) {
      const remoteName = `${listing.id}_${generateRandomCode(10)}.${main_image_file.extname}`
      await main_image_file.moveToDisk(remotePathListing, { name: remoteName }, disk_name)
      newMainImage.imageUrl = `${remoteName}`
    } else if (main_image_url) {
      newMainImage.imageUrl = main_image_url
    }

    await newMainImage.useTransaction(trx).save()
  }

  // Sub Image
  if (sub_image_ids) {
    // If sub image ids is provided, means update the existing sub image

    // Validate if sub image files or sub image urls is provided for update
    if (sub_image_files || sub_image_urls) {
      let totalImageSource: number = 0

      if (sub_image_files) {
        totalImageSource += sub_image_files.length
      }

      if (sub_image_urls) {
        totalImageSource += sub_image_urls.length
      }

      // Validate if total number of image source provided is enough to update each sub image id provided
      if (totalImageSource < sub_image_ids.length) {
        return {
          status: 'unprocessableEntity',
          msg: 'The total number of image from both sub_image_urls and sub_image_files are lesser than the sub_image_ids to be updated, please provide the sufficient image source to update each sub image, extra image source will be treated as new image to be created',
        }
      }
    } else {
      return {
        status: 'unprocessableEntity',
        msg: 'Please provide either sub_image_files or sub_image_urls to update sub image',
      }
    }

    let subImageUrlIndexCount: number = 0
    for await (const [index, sub_image_id] of sub_image_ids.entries()) {
      const isAssociatedSubImage = await ListingFile.query()
        .where('listing_id', listing.id)
        .andWhere('id', sub_image_id)
        .andWhere('is_main', false)
        .first()

      // Validate if the main image associated with the mentioned listing.
      if (!isAssociatedSubImage) {
        return {
          status: 'conflict',
          msg:
            'Please provide the sub image file id that are associated with the mentioned listing only at index: ' +
            index,
        }
      }

      // Validation done, valid sub image, proceed update sub image
      await Drive.use(disk_name).delete(
        remotePathListing + isAssociatedSubImage.imageUrl.split(remotePathListing)[1]
      ) // Delete old image file, regardless previously have image file or not (no error prompt if file is not found using the url)
      if (sub_image_files && sub_image_files[index]) {
        // after split get ['','path...','file extension...']
        const remoteName = `${listing.id}_${generateRandomCode(10)}.${
          sub_image_files[index].extname
        }`
        await sub_image_files[index].moveToDisk(remotePathListing, { name: remoteName }, disk_name)
        isAssociatedSubImage.imageUrl = `${remoteName}`
      } else if (sub_image_urls && sub_image_urls[subImageUrlIndexCount]) {
        // When all sub_image_files provided has been used to update and there are still sub_image_id needed to be updated, then only proceed with using sub_image_url
        isAssociatedSubImage.imageUrl = sub_image_urls[subImageUrlIndexCount] //subImageUrlIndexCount starts from 0 initially
        subImageUrlIndexCount += 1
      }

      await isAssociatedSubImage.useTransaction(trx).save()
    }

    // Create new sub image if there are still remaining extra image, splice all image that has been used to update the existing sub image
    sub_image_files && sub_image_files.splice(0, sub_image_ids.length) // splice all image file that has been used to update existing sub image
    sub_image_urls?.splice(0, subImageUrlIndexCount) // splice all image url that has been used to update existing sub image

    if (sub_image_files) {
      // Create new sub images using remaining urls/files
      let filesToCreate: Partial<ListingFile>[] = []

      for await (const [, sub_image_file] of sub_image_files?.entries()) {
        const remoteName = `${listing.id}_${generateRandomCode(10)}.${sub_image_file.extname}`
        await sub_image_file.moveToDisk(remotePathListing, { name: remoteName }, disk_name)
        filesToCreate.push({
          listingId: listing.id,
          isApproved: true,
          isHidden: false,
          isMain: false,
          reviewedBy: user.id,
          uploadedBy: user.id,
          imageUrl: `${remoteName}`,
        })
      }

      await ListingFile.createMany(filesToCreate, { client: trx })
    }

    if (sub_image_urls) {
      // Create new sub images using remaining urls/files
      await ListingFile.createMany(
        sub_image_urls.map((sub_image_url) => {
          return {
            listingId: listing.id,
            isApproved: true,
            isHidden: false,
            isMain: false,
            reviewedBy: user.id,
            uploadedBy: user.id,
            imageUrl: sub_image_url,
          }
        }),
        { client: trx }
      )
    }
  } else if (sub_image_files || sub_image_urls) {
    if (sub_image_files) {
      // Create new sub images using URL
      let filesToCreate: Partial<ListingFile>[] = []

      for await (const [, sub_image_file] of sub_image_files?.entries()) {
        const remoteName = `${listing.id}_${generateRandomCode(10)}.${sub_image_file.extname}`
        await sub_image_file.moveToDisk(remotePathListing, { name: remoteName }, disk_name)
        filesToCreate.push({
          listingId: listing.id,
          isApproved: true,
          isHidden: false,
          isMain: false,
          reviewedBy: user.id,
          uploadedBy: user.id,
          imageUrl: `${remoteName}`,
        })
      }

      await ListingFile.createMany(filesToCreate, { client: trx })
    }

    if (sub_image_urls) {
      // Create new sub images using File
      await ListingFile.createMany(
        sub_image_urls.map((sub_image_url) => {
          return {
            listingId: listing.id,
            isApproved: true,
            isHidden: false,
            isMain: false,
            reviewedBy: user.id,
            uploadedBy: user.id,
            imageUrl: sub_image_url,
          }
        }),
        { client: trx }
      )
    }
  }
}

/**
 * Generate SHA256 with RSA Header
 * @param {string} url Full token API URL
 * @param {Record<string, string>} params JSON object of params sent, key/value pair.
 * @param {string} method HTTP request method
 * @param {string} contentType HTTP request body content type
 * @param {string} clientId Client ID
 * @param {string} privateCertKey Private key certificate content
 * @param {string} privateCertKeySecret Private key certificate secret
 * @param {string} nonce Secure random string
 * @return {string} SHA256 with RSA Header
 */
export function generateSHA256withRSAHeader(
  url: string,
  params: Record<string, string>,
  method: string,
  contentType: string,
  clientId: string,
  privateCertKey: string,
  privateCertKeySecret: string,
  nonce: string
): string {
  const timestamp = new Date().getTime()

  // Construct the authorization token.
  const defaultApexHeaders = {
    app_id: clientId, // App ID assigned to your application
    nonce: nonce, // secure random number
    signature_method: 'RS256',
    timestamp: timestamp, // Unix epoch time
  }

  if (method == 'POST' && contentType != 'application/x-www-form-urlencoded') {
    params = {}
  }

  // Forming the signature base string.
  const baseParams = sortJSON(_.merge(defaultApexHeaders, params))
  let baseParamsStr = querystring.stringify(baseParams)
  baseParamsStr = querystring.unescape(baseParamsStr)
  const baseString = method.toUpperCase() + '&' + url + '&' + baseParamsStr

  // Signing Base String to get Digital Signature.
  const signWith = {
    key: privateCertKey,
  }

  if (!_.isUndefined(privateCertKeySecret) && !_.isEmpty(privateCertKeySecret)) {
    _.set(signWith, 'passphrase', privateCertKeySecret)
  }

  // Load pem file containing the x509 cert & private key & sign the base string with it.
  const signature = crypto.createSign('RSA-SHA256').update(baseString).sign(signWith, 'base64')

  // Assembling the Header
  const strApexHeader =
    'PKI_SIGN timestamp="' +
    timestamp +
    '",nonce="' +
    nonce +
    '",app_id="' +
    clientId +
    '",signature_method="RS256"' +
    ',signature="' +
    signature +
    '"'

  return strApexHeader
}

/**
 * Sorts a JSON object based on the key value in alphabetical order.
 * @param {Record<string, string>} json raw.
 * @return {Record<string, string>} sorted json.
 */
export function sortJSON(json: Record<string, string>): Record<string, string> {
  if (_.isNil(json)) {
    return json
  }

  const newJSON: { [key: string]: string } = {}
  const keys = Object.keys(json)
  keys.sort()

  for (const key in keys) {
    if (Object.prototype.hasOwnProperty.call(keys, key)) {
      newJSON[keys[key]] = json[keys[key]]
    }
  }

  return newJSON
}

/**
 * Verify & Decode JWS or JWT
 * @param {string} jws JSON web token
 * @param {string} publicCert Public x509 certificate
 * @return {string | jwt.JwtPayload} JSON web token payload
 */
export function verifyJWS(jws: string, publicCert: string): string | jwt.JwtPayload {
  try {
    const decoded = jwt.verify(jws, publicCert, {
      algorithms: ['RS256'],
      ignoreNotBefore: true,
    })

    return decoded
  } catch (error) {
    return ''
  }
}

/**
 * Decrypt JWE using private key
 * @param {string} header JWE header
 * @param {string} encryptedKey JWE encrypted key
 * @param {string} iv JWE iv
 * @param {string} cipherText JWE ciphertext
 * @param {string} tag JWE tag
 * @param {string} privateKey Private key certificate content
 * @return {Record<string, string>} Decrypted payload
 */
export async function decryptJWE(
  header: string,
  encryptedKey: string,
  iv: string,
  cipherText: string,
  tag: string,
  privateKey: string
): Promise<Record<string, string>> {
  const keystore = jose.JWK.createKeyStore()
  const data = {
    type: 'compact',
    ciphertext: cipherText,
    protected: header,
    encrypted_key: encryptedKey,
    tag: tag,
    iv: iv,
    header: JSON.parse(jose.util.base64url.decode(header).toString()),
  }

  const jweKey = await keystore.add(privateKey, 'pem')
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const result = await jose.JWE.createDecrypt(jweKey).decrypt(data)

  // {result} is a jose.JWK.Key
  return JSON.parse(result.payload.toString())
}

/**
 * Generate JSON file
 * @param {string} fileName location to create file.
 * @param {string} jsonString json in string.
 * @returns {void}
 */
export async function writeJsonFile(fileName: string, jsonString: string): Promise<void> {
  try {
    // Ensure the JSON string is valid
    JSON.parse(jsonString)

    // Resolve the file path
    const fullPath = join(Application.appRoot, 'data/' + fileName)

    // Create directory if it doesn't exist
    const dir = join(Application.appRoot, 'data')
    await mkdir(dir, { recursive: true }).catch(() => {}) // Ignore if directory exists

    // Write the file
    await writeFile(fullPath, jsonString, 'utf8')

    console.log(`JSON file written successfully: ${fullPath}`)
  } catch (error) {
    if (error instanceof SyntaxError) {
      console.error('Invalid JSON string:', error.message)
    } else {
      console.error('Error writing JSON file:', error)
    }
    throw error
  }
}

// Update the geojson file with latest listing info
export async function updateGeojsonFile(): Promise<void> {
  console.log('updateGeojsonFile')

  const listings = await Listing.query()
    .select('listings.*') // need this since we inner join positions table
    .where('listings.is_hidden', false)
    .andWhere('is_verified', true)

  const geoJsonFeatures = listings.map((listing) => {
    return {
      type: 'Feature',
      properties: {
        id: listing.id,
        type: listing.listingType,
        name: listing.name,
        firestore_id: listing.firestoreId,
      },
      geometry: {
        type: 'Point',
        coordinates: [listing.position.coordinate.x, listing.position.coordinate.y],
      },
    }
  })

  const geoJsonResponse = {
    type: 'FeatureCollection',
    crs: {
      type: 'name',
      properties: {
        name: 'urn:ogc:def:crs:OGC:1.3:CRS84',
      },
    },
    features: geoJsonFeatures,
  }

  await writeJsonFile('geojson.json', JSON.stringify(geoJsonResponse))
}

export function validatePhoneNumber(phoneNumber: string): boolean {
  const phoneUtil = PhoneNumberUtil.getInstance()
  let fullPhoneNumber = phoneNumber

  try {
    // if + code is not provided, add it in
    if (!fullPhoneNumber.startsWith('+')) {
      fullPhoneNumber = '+' + fullPhoneNumber
    }

    const number = phoneUtil.parseAndKeepRawInput(fullPhoneNumber)

    return phoneUtil.isPossibleNumber(number)
  } catch (err) {
    return false
  }
}

export function dialCodeToCountryCodeAndName(
  fullPhoneNumber: string
): { countryCode: string; countryName: string } | null {
  const phoneUtil = PhoneNumberUtil.getInstance()
  const number = phoneUtil.parseAndKeepRawInput(fullPhoneNumber)

  const countryCode = phoneUtil.getRegionCodeForNumber(number)
  if (!countryCode) return null // No country code return null

  const countryName = isoCountry.getName(countryCode, 'en')
  if (!countryName) return null // No country name return null

  const countryObj = {
    countryCode: countryCode,
    countryName: countryName,
  }

  return countryObj
}

function deg2rad(deg) {
  return deg * (Math.PI / 180)
}

export function getDistanceFromLatLonInKm(lat1, lon1, lat2, lon2) {
  const R = 6371 // Radius of the Earth in kilometers
  const dLat = deg2rad(lat2 - lat1) // Difference in latitude
  const dLon = deg2rad(lon2 - lon1) // Difference in longitude
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  const distance = R * c // Distance in kilometers
  return distance
}

export async function syncMeilisearch(type: 'delete' | 'update', listingId: string) {
  switch (type) {
    case 'update':
      const updatedListing = await Listing.query()
        .where('id', listingId)
        .preload('listingFiles')
        .preload('activities')
        .preload('amenities')
        .preload('position')
        .preload('listingRatings')
        .withAggregate('listingRatings', (query) => {
          query.avg('experience_rating').as('average_experience_ratings')
        })
        .first()

      //----------------------------------Sync Meili with latest update----------------------------------
      const searchDocuments = [
        {
          id: updatedListing!.id,
          listing_name: updatedListing!.name,
          listing_created_at: updatedListing!.createdAt,
          listing_type: updatedListing!.listingType,
          listing_keywords: updatedListing!.keywords,
          listing_is_verified: updatedListing!.isVerified,
          listing_is_hidden: updatedListing!.isHidden,
          listing_status: updatedListing!.status ?? null,
          amenities: updatedListing!.amenities.map((amenity) => amenity.id),
          average_experience_ratings: updatedListing!.$extras.average_experience_ratings,
          // _geo is a reserved field. If you include it in your documents, Meilisearch expects its value to conform to a specific format.
          _geo: {
            lat: updatedListing!.position.coordinate.y,
            lng: updatedListing!.position.coordinate.x,
          },
        },
      ]
      await MeilisearchService.admin.addOrUpdateDocument(`listings`, searchDocuments)
      //----------------------------------Sync Meili with latest update----------------------------------
      break
    case 'delete':
      await MeilisearchService.admin.deleteDocument(`listings`, listingId)
      break
    default:
      break
  }
}
