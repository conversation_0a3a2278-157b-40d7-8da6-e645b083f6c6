import { BaseTask, CronTimeV2 } from 'adonis5-scheduler/build/src/Scheduler/Task'
import Session from 'App/Models/Session'
import { ListingStatus } from 'Contracts/listing_type'
import { DateTime } from 'luxon'

// NOTE: This task is to set the actual_ended_at of a session from null to DateTime.now() if the session is ended (user already leave the pod) but actual_ended_at is still null due server/db crash bug.
// When session ended, the actual_ended_at should be set to null, but apparently there is an unknown reason that the actual_ended_at is still null.
// This task will check if the actual_ended_at is still null and if so, it will set it to DateTime.now().

export default class CheckEndedSessionWithNullActualEndedAt extends BaseTask {
  public static get schedule() {
    // Use CronTimeV2 generator:
    return CronTimeV2.everyFiveMinutes()
    // or just use return cron-style string (simple cron editor: crontab.guru)
  }
  /**
   * Set enable use .lock file for block run retry task
   * Lock file save to `build/tmp/adonis5-scheduler/locks/your-class-name`
   */
  public static get useLock() {
    return false
  }

  public async handle() {
    try {
      const onGoingSessions = await Session.query()
        .whereNull('actual_ended_at') // Only get active sessions
        .preload('user')
        .preload('listing')

      // NOTE: The onGoingSessionsWithListingStatusBug is to handle the scenario where user already exit(listing status is idle or disinfecting),
      // but actual_ended_at is still null (onGoingSessions pull only session with null actual_ended_at).
      const onGoingSessionsWithListingStatusBug = onGoingSessions.filter((session) => {
        if (
          session.listing.status === ListingStatus.disinfecting ||
          session.listing.status === ListingStatus.idle
        ) {
          return session
        }
      })

      if (onGoingSessionsWithListingStatusBug.length > 0) {
        for (const session of onGoingSessionsWithListingStatusBug) {
          // help user encounter bug to update with now (DateTime.now()) to their actual_ended_at column value.
          session.actualEndedAt = DateTime.now()
          await session.save()
        }
      }

      this.logger.info(
        `Done update total ${onGoingSessionsWithListingStatusBug.length} sessions that has null actual_ended_at but user already leave the pod.`
      )
    } catch (error) {
      this.logger.error(
        `Error update sessions that has null actual_ended_at but user already leave the pod: ${error.message}`
      )
    }
  }
}
