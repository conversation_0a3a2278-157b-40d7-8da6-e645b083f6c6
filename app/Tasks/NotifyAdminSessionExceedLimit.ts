import { BaseTask, CronTimeV2 } from 'adonis5-scheduler/build/src/Scheduler/Task'
import Session from 'App/Models/Session'
import { DateTime } from 'luxon'
import Event from '@ioc:Adonis/Core/Event'
import { render } from '@react-email/render'
import { SessionExceedEmail } from '../../resources/components/SessionExceedEmail'
import SessionExceedNotificationEmail from 'App/Models/SessionExceedNotificationEmail'
import User from 'App/Models/User'
import { ListingStatus } from 'Contracts/listing_type'
import { UserType } from 'Contracts/user_type'

export default class NotifyAdminSessionExceedLimit extends BaseTask {
  public static get schedule() {
    // Use CronTimeV2 generator:
    return CronTimeV2.everyFiveMinutes()
    // or just use return cron-style string (simple cron editor: crontab.guru)
  }
  /**
   * Set enable use .lock file for block run retry task
   * Lock file save to `build/tmp/adonis5-scheduler/locks/your-class-name`
   */
  public static get useLock() {
    return false
  }

  public async handle() {
    try {
      const now = DateTime.now()

      const sessionExceedNotificationEmailsWithinLastSpecifiedMinutes =
        await SessionExceedNotificationEmail.query().whereRaw(
          `TIMESTAMPDIFF(MINUTE, created_at, '${now.toSQL()}') < 6`
        )
      // NOTE: The 6 minutes timestampdiff are assigned depending on the cron time set in schedule() above.
      // This is to ensure same user(s) won't causes spam and ensure the same user(s) (who is still in the pod) will be resend again after the next cycle.
      // Eg. Every 5 minutes will have 1 cycle, sessionExceedNotificationEmailsWithinLast6Minutes will extract all user id in email sent last 6 minutes from table SessionExceedNotificationEmail.
      // Each cycle if there is any notification email sent, a new row will be created in table SessionExceedNotificationEmail.
      const usersIncludedInNotifyEmailWithinLastSpecifiedMinutes: string[] = []
      sessionExceedNotificationEmailsWithinLastSpecifiedMinutes.forEach((row) => {
        usersIncludedInNotifyEmailWithinLastSpecifiedMinutes.push(...row.userIdsWithinEmail)
      })
      const uniqueUserIds = [...new Set(usersIncludedInNotifyEmailWithinLastSpecifiedMinutes)]

      const exceedExpectedEndedAtSessions = await Session.query()
        .where('expected_ended_at', '<', now.toSQL())
        .andWhereNull('actual_ended_at') // Only get active sessions
        .andWhereNotIn('user_id', uniqueUserIds)
        .preload('user')
        .preload('listing')

      // NOTE: The exceedExpectedEndedAtSessionsWithoutListingStatusBug is for overcoming the uncontrollable server crash bug, where session actual_ended_at is not updated
      // but in reality the user has already leave the pod and the session should be ended.
      const exceedExpectedEndedAtSessionsWithoutListingStatusBug =
        exceedExpectedEndedAtSessions.filter((session) => {
          if (session.listing.status === ListingStatus.occupied) {
            return session
          }
        })

      if (exceedExpectedEndedAtSessionsWithoutListingStatusBug.length > 0) {
        await SessionExceedNotificationEmail.create({
          userIdsWithinEmail: exceedExpectedEndedAtSessionsWithoutListingStatusBug.map(
            (session) => session.user.id
          ),
          createdAt: now,
          updatedAt: now,
        })

        const htmlRendered = render(
          SessionExceedEmail({
            exceededSessionsInfo: exceedExpectedEndedAtSessionsWithoutListingStatusBug.map(
              (session) => {
                return {
                  user: session.user,
                  listing: session.listing,
                  startedAt: session.startedAt,
                  expectedEndedAt: session.expectedEndedAt,
                  sessionId: session.id,
                }
              }
            ),
            timeNow: now,
          })
        )
        const plainText = render(
          SessionExceedEmail({
            exceededSessionsInfo: exceedExpectedEndedAtSessionsWithoutListingStatusBug.map(
              (session) => {
                return {
                  user: session.user,
                  listing: session.listing,
                  startedAt: session.startedAt,
                  expectedEndedAt: session.expectedEndedAt,
                  sessionId: session.id,
                }
              }
            ),
            timeNow: now,
          }),
          {
            plainText: true,
          }
        )

        const adminUsers = await User.query().where('user_type', UserType.admin)
        for await (const adminUser of adminUsers) {
          if (adminUser.emailAddress) {
            await Event.emit('new:session-exceed-limit', {
              from: process.env.SES_SENDER!,
              to: adminUser.emailAddress,
              subject: 'Sessions Exceeded',
              html: htmlRendered,
              text: plainText,
            })
          }
        }

        // TODO: Change to send email to all admin when publish in production.
        // const adminUsers = await User.query().where('user_type', UserType.admin)
        // for await (const adminUser of adminUsers) {
        //   if (adminUser.emailAddress) {
        //     await Event.emit('new:session-exceed-limit', {
        //       from: process.env.SES_SENDER!,
        //       to: adminUser.emailAddress,
        //       subject: 'Sessions Exceeded',
        //       html: htmlRendered,
        //       text: plainText,
        //     })
        //   }
        // }
      }

      this.logger.info(
        `Done notify admin total ${exceedExpectedEndedAtSessionsWithoutListingStatusBug.length} sessions exceed the expected_ended_at time.`
      )
    } catch (error) {
      this.logger.error(
        `Error notify admin sessions exceed the expected_ended_at time: ${error.message}`
      )
    }
  }
}
