import Database from '@ioc:Adonis/Lucid/Database'
import { BaseTask, CronTimeV2 } from 'adonis5-scheduler/build/src/Scheduler/Task'
import ListingRating from 'App/Models/ListingRating'
import { DateTime } from 'luxon'

export default class AutoReviewSessionsPastTwoDays extends BaseTask {
  public static get schedule() {
    // Use CronTimeV2 generator:
    return CronTimeV2.everyDayAt(0, 0)
    // or just use return cron-style string (simple cron editor: crontab.guru)
  }
  /**
   * Set enable use .lock file for block run retry task
   * Lock file save to `build/tmp/adonis5-scheduler/locks/your-class-name`
   */
  public static get useLock() {
    return false
  }

  public async handle() {
    try {
      const now = DateTime.now()
      const twoDaysAgoToISODate = now.minus({ days: 2 }).toISODate()

      const ratingsLast2Days = await Database.query()
        .from('listing_ratings')
        .whereRaw('DATE(created_at) >= ?', [twoDaysAgoToISODate])
        .andWhereNot('created_at', 'actual_ended_at')

      // Note: started_at != actual_ended_at is to exclude no entry session (user created but never enter).
      // Redis service will automatically set actual_ended_at to value of started_at once determine user did not enter a session.
      const enteredButUnreviewedSessionsLast2Days = await Database.rawQuery(
        `select * from sessions where DATE(started_at) = DATE_SUB(DATE(now()), INTERVAL 2 day) and started_at != actual_ended_at ${
          ratingsLast2Days.length > 0 ? `and id not in (${ratingsLast2Days.map(() => '?')})` : ''
        }`,
        ratingsLast2Days.length > 0
          ? ratingsLast2Days.map((rating) => rating.session_id)
          : undefined
      )

      enteredButUnreviewedSessionsLast2Days[0].forEach(async (unreviewedSession) => {
        await ListingRating.create({
          listingId: unreviewedSession.listing_id,
          sessionId: unreviewedSession.id,
          appRating: 5,
          experienceRating: 5,
          listingRating: 5,
          review: '-',
        })
      })

      this.logger.info('Completed review past 2 days unreviewed sessions')
    } catch (error) {
      this.logger.error(`Error auto review unreviewed sessions: ${error.message}`)
    }
  }
}
