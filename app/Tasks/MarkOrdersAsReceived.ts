import { BaseTask, CronTimeV2 } from 'adonis5-scheduler/build/src/Scheduler/Task'
import { DateTime } from 'luxon'
import ShopifyService from '../Services/ShopifyService'

export default class MarkOrdersAsReceived extends BaseTask {
  public static commandName = 'orders:mark-as-received'
  public static description =
    'Mark orders as received if they are older than 30 days and not marked yet'

  private readonly METAFIELD_NAMESPACE = 'mark_as_received'
  private readonly METAFIELD_KEY = 'status'

  public static get schedule() {
    // Use CronTimeV2 generator:
    return CronTimeV2.everyDayAt(0, 0)
    // or just use return cron-style string (simple cron editor: crontab.guru)
  }

  /**
   * Set enable use .lock file for block run retry task
   * Lock file save to `build/tmp/adonis5-scheduler/locks/your-class-name`
   */
  public static get useLock() {
    return false
  }

  public async handle() {
    const shopifyService = ShopifyService.getInstance()

    // Calculate the date 30 days ago
    const thirtyDaysAgo = DateTime.now().minus({ days: 30 }).toISO()

    // Query to get orders that are older than 30 days
    const query = `
      query {
        orders(
          first: 50,
          sortKey: PROCESSED_AT,
          reverse: true,
          query: "processed_at:<='${thirtyDaysAgo}'"
        ) {
          edges {
            node {
              id
              processedAt
              metafield(namespace: "${this.METAFIELD_NAMESPACE}", key: "${this.METAFIELD_KEY}") {
                value
              }
            }
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    `

    try {
      const result = await shopifyService.graphqlRequest(query)
      const orders = result.data.orders.edges

      // Filter orders that haven't been marked as received
      const unmarkedOrders = orders.filter(
        (edge) => !edge.node.metafield || edge.node.metafield.value !== 'true'
      )

      this.logger.info(`Found ${unmarkedOrders.length} orders to mark as received`)

      // Mark each order as received
      for (const order of unmarkedOrders) {
        const orderId = order.node.id.split('/').pop()
        const mutation = `
          mutation OrderUpdateMarkAsReceivedMetafield($input: MetafieldsSetInput!) {
            metafieldsSet(metafields: [$input]) {
              metafields {
                id
                namespace
                key
                value
              }
              userErrors {
                field
                message
              }
            }
          }
        `

        const variables = {
          input: {
            ownerId: order.node.id,
            namespace: this.METAFIELD_NAMESPACE,
            key: this.METAFIELD_KEY,
            type: 'boolean',
            value: 'true',
          },
        }

        try {
          await shopifyService.graphqlRequest(mutation, variables)
          this.logger.info(`Successfully marked order ${orderId} as received`)
        } catch (error) {
          this.logger.error(`Failed to mark order ${orderId} as received: ${error.message}`)
        }
      }

      this.logger.info('Completed marking orders as received')
    } catch (error) {
      this.logger.error(`Error processing orders: ${error.message}`)
    }
  }
}
