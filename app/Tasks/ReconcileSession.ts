import { BaseTask, CronTimeV2 } from 'adonis5-scheduler/build/src/Scheduler/Task'
import Session from 'App/Models/Session'
import { DateTime } from 'luxon'
import { Queue } from 'bullmq'
import Env from '@ioc:Adonis/Core/Env'

export default class ReconcileSession extends BaseTask {
  private redisConnection = {
    host: Env.get('REDIS_HOST', '127.0.0.1'),
    port: Env.get('REDIS_PORT', 6379),
    password: Env.get('REDIS_PASSWORD', undefined),
  }

  public static get schedule() {
    // Use CronTimeV2 generator:
    return CronTimeV2.everyFiveMinutes()
    // or just use return cron-style string (simple cron editor: crontab.guru)
  }

  /**
   * Set enable use .lock file for block run retry task
   * Lock file save to `build/tmp/adonis5-scheduler/locks/your-class-name`
   */
  public static get useLock() {
    return false
  }

  public async handle() {
    this.logger.info('Starting session reconciliation...')

    const sessionQueue = new Queue('session-cleanup', {
      connection: this.redisConnection,
    })

    try {
      const now = DateTime.now()

      // Find sessions that should have ended but are still active
      const overdueSessions = await Session.query()
        .whereNull('actual_ended_at')
        .where('expected_ended_at', '<=', now.toSQL())

      if (overdueSessions.length === 0) {
        this.logger.info('No overdue sessions found.')
        return
      }

      this.logger.info(
        `Found ${overdueSessions.length} overdue sessions. Re-queuing for cleanup...`
      )

      for (const session of overdueSessions) {
        await sessionQueue.add(
          'cleanup',
          { sessionId: session.id },
          { jobId: `reconcile-${session.id}` }
        )
        this.logger.info(`Re-queued session ${session.id} for cleanup.`)
      }

      this.logger.info('Session reconciliation completed.')
    } catch (error) {
      this.logger.error('Error during session reconciliation:', error)
    } finally {
      await sessionQueue.close()
    }
  }
}
