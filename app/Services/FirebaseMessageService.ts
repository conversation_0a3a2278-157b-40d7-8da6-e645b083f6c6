import { initializeApp, cert } from 'firebase-admin/app'
import { getMessaging, MulticastMessage } from 'firebase-admin/messaging'

export function setupFirebase() {
  try {
    // Get service account path - default to project root but can be overridden by env var
    const serviceAccountPath =
      process.env.FIREBASE_SERVICE_ACCOUNT_PATH || './serviceAccountKey.json'

    initializeApp({
      credential: cert(serviceAccountPath),
      databaseURL: process.env.FIREBASE_URL,
    })
  } catch (e) {
    console.log('INITIALIZE FIREBASE ERROR', e)
  }
}

export async function sendNotification(
  custom_message_title: string,
  custom_message: string,
  device_tokens: string[],
  imageUrl?: string
) {
  const message: MulticastMessage = {
    notification: {
      title: custom_message_title,
      body: custom_message,
      imageUrl: imageUrl ? imageUrl : undefined,
    },
    android: imageUrl
      ? {
          notification: {
            imageUrl: imageUrl,
          },
        }
      : undefined,
    apns: imageUrl
      ? {
          payload: {
            aps: {
              'mutable-content': 1,
            },
          },
          fcmOptions: {
            imageUrl: imageUrl,
          },
        }
      : undefined,
    tokens: device_tokens,
  }
  const response = await getMessaging().sendEachForMulticast(message)
  console.log('Successfully sent message:', response)
  return response
}
