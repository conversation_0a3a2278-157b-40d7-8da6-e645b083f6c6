import { ModelObject } from '@ioc:Adonis/Lucid/Orm'
import Logger from '@ioc:Adonis/Core/Logger'
import Redis from '@ioc:Adonis/Addons/Redis'
import Env from '@ioc:Adonis/Core/Env'
import { REDIS_KEYS } from './RedisService'
import MqttService from './MqttService'

export type SessionEvent = {
  type:
    | 'session_created'
    | 'session_extended'
    | 'session_ended'
    | 'active_session'
    | 'no_active_session'
    | 'session_cleanup'
    | 'user_ongoing_session' // user has another ongoing session
    | 'user_ongoing_session_with_restart' // user has another ongoing session but we allow them to restart the session
    | 'listing_ongoing_session' // listing has another ongoing session
    | 'child_exceed_age' // user's youngest child > 3 years old
    | 'no_child' // user doesn't have any children
    | 'exceed_distance' // user are not within range to unlock the listing
    | 'entry_expired' // user did not join within entry time
    | 'female_only' // user is not female
} & ModelObject

const ENTRY_TIMEOUT = Env.get('IGLOOHOME_ENTRY_TIMEOUT') * 60 * 1000

export default class SessionService {
  private static mqttService: MqttService | null = null

  private static getMqttService(): MqttService {
    if (!this.mqttService) {
      this.mqttService = new MqttService()
    }
    return this.mqttService
  }

  public static async emitUpdate(userId: string, data: SessionEvent) {
    Logger.info('session emit user %s', userId)
    Logger.info('session emit data %o', data)

    if (data.type == 'session_created') {
      console.log('session_created')
      const entryDeadline = Date.now() + ENTRY_TIMEOUT
      const endDeadline = new Date(data.expected_ended_at).getTime()

      // Keep existing Redis operations for parallel operation
      await Redis.pipeline()
        .hset(`${REDIS_KEYS.SESSION_HASH}:${userId}`, { ...data, entry_deadline: entryDeadline })
        .hset(REDIS_KEYS.SESSION_IDS, {
          [data.id]: userId,
        })
        .zadd(REDIS_KEYS.SESSION_ENTRY_CHECK, entryDeadline, data.id) // Add to entry check set
        .zadd(REDIS_KEYS.SESSION_EXPIRY_SET, endDeadline, data.id)
        .exec()

      // Add MQTT publishing
      try {
        const mqttService = this.getMqttService()
        await mqttService.publishSessionCreated(data.id, {
          id: data.id,
          userId: userId,
          listingId: data.listing_id || '',
          startedAt: data.started_at,
          expectedEndedAt: data.expected_ended_at,
          ...data
        })

        // Also publish to user sessions topic
        await mqttService.publishUserSession(userId, {
          id: data.id,
          userId: userId,
          listingId: data.listing_id || '',
          startedAt: data.started_at,
          expectedEndedAt: data.expected_ended_at,
          ...data
        })
      } catch (error) {
        Logger.error('Failed to publish session_created to MQTT:', error)
      }

      return
    } else if (data.type == 'session_extended') {
      console.log('session_extended', data)

      const sessionUserId = await Redis.hget(REDIS_KEYS.SESSION_IDS, data.id)
      if (!sessionUserId) return false

      const expiryTimestamp = new Date(data.expected_ended_at).getTime()

      // Keep existing Redis operations
      await Redis.pipeline()
        .hset(`${REDIS_KEYS.SESSION_HASH}:${sessionUserId}`, { expected_ended_at: data.expected_ended_at })
        .zadd('sessions:expiry', expiryTimestamp, data.id)
        .exec()

      // Add MQTT publishing
      try {
        const mqttService = this.getMqttService()
        await mqttService.publishSessionUpdated(data.id, {
          id: data.id,
          userId: sessionUserId,
          listingId: data.listing_id || '',
          startedAt: data.started_at,
          expectedEndedAt: data.expected_ended_at,
          ...data
        })

        // Also publish to user sessions topic
        await mqttService.publishUserSession(sessionUserId, {
          id: data.id,
          userId: sessionUserId,
          listingId: data.listing_id || '',
          startedAt: data.started_at,
          expectedEndedAt: data.expected_ended_at,
          ...data
        })
      } catch (error) {
        Logger.error('Failed to publish session_extended to MQTT:', error)
      }

      return
    } else if (data.type == 'session_ended') {
      console.log('session_ended', data)

      // Keep existing Redis operations
      await Redis.pipeline()
        .del(`${REDIS_KEYS.SESSION_HASH}:${userId}`) // Remove session data
        .hdel(REDIS_KEYS.SESSION_IDS, data.id) // Remove mapping
        .zrem(REDIS_KEYS.SESSION_EXPIRY_SET, data.id) // Remove from expiry set
        .exec()

      // Add MQTT publishing
      try {
        const mqttService = this.getMqttService()
        await mqttService.publishSessionEnded(data.id, {
          id: data.id,
          userId: userId,
          listingId: data.listing_id || '',
          startedAt: data.started_at,
          expectedEndedAt: data.expected_ended_at,
          actualEndedAt: data.actual_ended_at,
          ...data
        })
      } catch (error) {
        Logger.error('Failed to publish session_ended to MQTT:', error)
      }

      return
    }

    // Keep existing Redis operations
    await Redis.pipeline()
      /// NOTE: set value to redis for storage
      .hset(`${REDIS_KEYS.SESSION_HASH}:${userId}`, data)
      /// NOTE: publish to trigger pub/sub in gomama-realtime
      .publish(`${REDIS_KEYS.SESSION_HASH}:${userId}`, JSON.stringify(data))
      .exec()

    // Add MQTT publishing for other session events
    try {
      const mqttService = this.getMqttService()

      // Determine the appropriate MQTT topic based on event type
      if (data.type === 'session_cleanup') {
        await mqttService.publishSessionCleanup(data.id, {
          id: data.id,
          userId: userId,
          listingId: data.listing_id || '',
          startedAt: data.started_at,
          expectedEndedAt: data.expected_ended_at,
          ...data
        })
      } else {
        // For other session events, publish as user notifications
        await mqttService.publishUserNotification(userId, {
          userId: userId,
          title: `Session ${data.type}`,
          message: `Session event: ${data.type}`,
          sessionId: data.id,
          eventType: data.type,
          ...data
        })
      }
    } catch (error) {
      Logger.error(`Failed to publish ${data.type} to MQTT:`, error)
    }
  }
}
