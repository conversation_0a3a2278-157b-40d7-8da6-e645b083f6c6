import Redis from '@ioc:Adonis/Addons/Redis'
import Logger from '@ioc:Adonis/Core/Logger'
import Listing from 'App/Models/Listing'
import Session from 'App/Models/Session'
import { ListingStatus } from 'Contracts/listing_type'
import MqttService, { MqttMessage } from './MqttService'

export const REDIS_KEYS = {
  ACTIVE_LISTINGS: 'active-listings',
  SESSION_HASH: 'active-sessions', // Hash storing all sessions
  SESSION_EXPIRY_SET: 'session-expiry', // Sorted set for session expiry times
  SESSION_IDS: 'session-ids', // Set of active session IDs
  DEVICE_IDS: 'device-ids', // Set of user id mapping to device id
  SESSION_CLEANUP: 'session-cleanup',
  SESSION_ENTRY_CHECK: 'session-entry-check', // Track sessions to join in 5 minutes
  SESSION_ENTRY_EXPIRED: 'session-entry-expired',
} as const

export default class RedisService {
  private mqttService: MqttService

  constructor() {
    this.mqttService = new MqttService()
    this.setupRedisSubscriptions()
    this.setupMqttSubscriptions()
  }

  private setupRedisSubscriptions() {
    Logger.info(`redis subscribe to ${REDIS_KEYS.ACTIVE_LISTINGS}:*`)
    Redis.psubscribe(`${REDIS_KEYS.ACTIVE_LISTINGS}:*`, async (channel, data) => {
      const id = channel.split(':')[1]
      const listing = await Listing.findBy('firestore_id', id)
      if (!listing) {
        return
      }

      const currentStatus =
        data == 'occupied'
          ? ListingStatus.occupied
          : data == 'disinfecting'
            ? ListingStatus.disinfecting
            : ListingStatus.idle

      listing.status = currentStatus
      await listing.save()
    })

    // idle 5 minutes terminate the sessions
    Logger.info(`redis subscribe to ${REDIS_KEYS.SESSION_ENTRY_EXPIRED}`)
    Redis.psubscribe(`${REDIS_KEYS.SESSION_ENTRY_EXPIRED}`, async (channel, data) => {
      // data is expected to be session id
      try {
        const lastSessionByUser = await Session.query()
          .where('id', data)
          .whereNull('actual_ended_at')
          .first()

        if (!lastSessionByUser) {
          return
        }

        lastSessionByUser.actualEndedAt = lastSessionByUser.startedAt
        await lastSessionByUser.save()

        // we remove the session from redis
        await Redis.pipeline()
          .del(`${REDIS_KEYS.SESSION_HASH}:${lastSessionByUser.userId}`) // Remove session data
          .hdel(REDIS_KEYS.SESSION_IDS, lastSessionByUser.id) // Remove mapping
          .zrem(REDIS_KEYS.SESSION_EXPIRY_SET, lastSessionByUser.id) // Remove from expiry set
          .zrem(REDIS_KEYS.SESSION_ENTRY_CHECK, lastSessionByUser.id) // Remove from entry set
          .del(`${REDIS_KEYS.DEVICE_IDS}:${lastSessionByUser.userId}`) // No entry after 5 minutes, always remove the device id map
          .publish(
            `${REDIS_KEYS.SESSION_HASH}:${lastSessionByUser.userId}`,
            JSON.stringify({ type: 'entry_expired' })
          ) // notify frontend
          .exec()

        // notify frontend
        // SessionService.emitUpdate(lastSessionByUser.userId, { type: 'entry_expired' })
      } catch (error) {
        Logger.error(error, channel)
      }
    })
  }

  private async setupMqttSubscriptions() {
    try {
      // Subscribe to listing status updates
      await this.mqttService.subscribe('gomama/listings/status/+', async (topic, message) => {
        await this.handleListingStatusUpdate(topic, message)
      })

      // Subscribe to session cleanup events
      await this.mqttService.subscribe('gomama/sessions/cleanup/+', async (topic, message) => {
        await this.handleSessionCleanup(topic, message)
      })

      Logger.info('MQTT subscriptions set up successfully')
    } catch (error) {
      Logger.error('Failed to set up MQTT subscriptions:', error)
    }
  }

  private async handleListingStatusUpdate(topic: string, message: MqttMessage) {
    try {
      const topicParts = topic.split('/')
      const listingId = topicParts[topicParts.length - 1]

      if (message.type === 'status_update' && message.data?.status) {
        const listing = await Listing.findBy('firestore_id', listingId)
        if (!listing) {
          Logger.warn(`Listing not found for MQTT status update: ${listingId}`)
          return
        }

        const currentStatus =
          message.data.status === 'occupied'
            ? ListingStatus.occupied
            : message.data.status === 'disinfecting'
              ? ListingStatus.disinfecting
              : ListingStatus.idle

        listing.status = currentStatus
        await listing.save()

        Logger.info(`Updated listing ${listingId} status to ${currentStatus} via MQTT`)
      }
    } catch (error) {
      Logger.error('Error handling MQTT listing status update:', error)
    }
  }

  private async handleSessionCleanup(topic: string, message: MqttMessage) {
    try {
      const topicParts = topic.split('/')
      const sessionId = topicParts[topicParts.length - 1]

      if (message.type === 'session_cleanup') {
        const session = await Session.query()
          .where('id', sessionId)
          .whereNull('actual_ended_at')
          .first()

        if (!session) {
          Logger.warn(`Session not found for MQTT cleanup: ${sessionId}`)
          return
        }

        session.actualEndedAt = session.startedAt
        await session.save()

        // Clean up Redis data
        await Redis.pipeline()
          .del(`${REDIS_KEYS.SESSION_HASH}:${session.userId}`)
          .hdel(REDIS_KEYS.SESSION_IDS, session.id)
          .zrem(REDIS_KEYS.SESSION_EXPIRY_SET, session.id)
          .zrem(REDIS_KEYS.SESSION_ENTRY_CHECK, session.id)
          .del(`${REDIS_KEYS.DEVICE_IDS}:${session.userId}`)
          .exec()

        Logger.info(`Cleaned up session ${sessionId} via MQTT`)
      }
    } catch (error) {
      Logger.error('Error handling MQTT session cleanup:', error)
    }
  }
}
