import mqtt from 'mqtt'
import Env from '@ioc:Adonis/Core/Env'
import Logger from '@ioc:Adonis/Core/Logger'

export interface MqttMessage {
  timestamp: number
  source: string
  type: string
  data: any
}

export interface SessionData {
  id: string
  userId: string
  listingId: string
  startedAt: string
  expectedEndedAt: string
  [key: string]: any
}

export interface ListingStatusData {
  listingId: string
  status: string
  [key: string]: any
}

export interface UserNotificationData {
  userId: string
  title: string
  message: string
  [key: string]: any
}

export default class MqttService {
  private client: mqtt.MqttClient | null = null
  private isConnected: boolean = false
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 10
  private reconnectInterval: number = 5000 // 5 seconds

  constructor() {
    this.connect()
  }

  private connect(): void {
    const brokerUrl = `mqtt://${Env.get('MQTT_BROKER_HOST')}:${Env.get('MQTT_BROKER_PORT')}`
    
    Logger.info(`Connecting to MQTT broker at ${brokerUrl}`)

    this.client = mqtt.connect(brokerUrl, {
      clientId: Env.get('MQTT_CLIENT_ID'),
      username: Env.get('MQTT_USERNAME'),
      password: Env.get('MQTT_PASSWORD'),
      keepalive: 60,
      reconnectPeriod: this.reconnectInterval,
      connectTimeout: 30000,
      clean: true,
    })

    this.client.on('connect', () => {
      Logger.info('Connected to MQTT broker')
      this.isConnected = true
      this.reconnectAttempts = 0
    })

    this.client.on('error', (error) => {
      Logger.error('MQTT connection error:', error)
      this.isConnected = false
    })

    this.client.on('close', () => {
      Logger.warn('MQTT connection closed')
      this.isConnected = false
    })

    this.client.on('reconnect', () => {
      this.reconnectAttempts++
      Logger.info(`MQTT reconnecting... Attempt ${this.reconnectAttempts}`)
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        Logger.error('Max MQTT reconnection attempts reached')
        this.client?.end()
      }
    })

    this.client.on('offline', () => {
      Logger.warn('MQTT client is offline')
      this.isConnected = false
    })
  }

  private createMessage(type: string, data: any): MqttMessage {
    return {
      timestamp: Date.now(),
      source: 'adonisjs',
      type,
      data,
    }
  }

  private async publish(topic: string, message: MqttMessage, options: mqtt.IClientPublishOptions = {}): Promise<void> {
    if (!this.client || !this.isConnected) {
      Logger.error('MQTT client is not connected. Cannot publish message.')
      return
    }

    try {
      const messageString = JSON.stringify(message)
      Logger.info(`Publishing to MQTT topic: ${topic}`)
      Logger.debug(`MQTT message: ${messageString}`)

      await new Promise<void>((resolve, reject) => {
        this.client!.publish(topic, messageString, options, (error) => {
          if (error) {
            Logger.error('Failed to publish MQTT message:', error)
            reject(error)
          } else {
            resolve()
          }
        })
      })
    } catch (error) {
      Logger.error('Error publishing MQTT message:', error)
      throw error
    }
  }

  // Session-related methods
  async publishSessionCreated(sessionId: string, sessionData: SessionData): Promise<void> {
    const topic = `gomama/sessions/created/${sessionId}`
    const message = this.createMessage('session_created', sessionData)
    await this.publish(topic, message, { qos: 1 })
  }

  async publishSessionUpdated(sessionId: string, sessionData: SessionData): Promise<void> {
    const topic = `gomama/sessions/updated/${sessionId}`
    const message = this.createMessage('session_updated', sessionData)
    await this.publish(topic, message, { qos: 1 })
  }

  async publishSessionEnded(sessionId: string, sessionData: SessionData): Promise<void> {
    const topic = `gomama/sessions/ended/${sessionId}`
    const message = this.createMessage('session_ended', sessionData)
    await this.publish(topic, message, { qos: 1 })
  }

  async publishSessionCleanup(sessionId: string, sessionData: SessionData): Promise<void> {
    const topic = `gomama/sessions/cleanup/${sessionId}`
    const message = this.createMessage('session_cleanup', sessionData)
    await this.publish(topic, message, { qos: 1 })
  }

  // User-related methods
  async publishUserNotification(userId: string, notificationData: UserNotificationData): Promise<void> {
    const topic = `gomama/users/notifications/${userId}`
    const message = this.createMessage('user_notification', notificationData)
    await this.publish(topic, message, { qos: 1 })
  }

  async publishUserSession(userId: string, sessionData: SessionData): Promise<void> {
    const topic = `gomama/users/sessions/${userId}`
    const message = this.createMessage('user_session', sessionData)
    await this.publish(topic, message, { qos: 1, retain: true })
  }

  // Listing-related methods
  async publishListingStatus(listingId: string, status: string, additionalData: any = {}): Promise<void> {
    const topic = `gomama/listings/status/${listingId}`
    const message = this.createMessage('status_update', { listingId, status, ...additionalData })
    await this.publish(topic, message, { qos: 1, retain: true })
  }

  async publishListingAvailability(listingId: string, availability: any): Promise<void> {
    const topic = `gomama/listings/availability/${listingId}`
    const message = this.createMessage('availability_update', { listingId, availability })
    await this.publish(topic, message, { qos: 1, retain: true })
  }

  // System-related methods
  async publishSystemHealth(healthData: any): Promise<void> {
    const topic = 'gomama/system/health'
    const message = this.createMessage('system_health', healthData)
    await this.publish(topic, message, { qos: 0 })
  }

  async publishSystemMetrics(metricsData: any): Promise<void> {
    const topic = 'gomama/system/metrics'
    const message = this.createMessage('system_metrics', metricsData)
    await this.publish(topic, message, { qos: 0 })
  }

  // Subscription methods for receiving messages
  async subscribe(topic: string, callback: (topic: string, message: MqttMessage) => void): Promise<void> {
    if (!this.client || !this.isConnected) {
      Logger.error('MQTT client is not connected. Cannot subscribe to topic.')
      return
    }

    this.client.subscribe(topic, { qos: 1 }, (error) => {
      if (error) {
        Logger.error(`Failed to subscribe to MQTT topic ${topic}:`, error)
      } else {
        Logger.info(`Subscribed to MQTT topic: ${topic}`)
      }
    })

    this.client.on('message', (receivedTopic, payload) => {
      if (receivedTopic === topic) {
        try {
          const message: MqttMessage = JSON.parse(payload.toString())
          callback(receivedTopic, message)
        } catch (error) {
          Logger.error('Failed to parse MQTT message:', error)
        }
      }
    })
  }

  // Utility methods
  isClientConnected(): boolean {
    return this.isConnected
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      Logger.info('Disconnecting from MQTT broker')
      await new Promise<void>((resolve) => {
        this.client!.end(false, {}, () => {
          this.isConnected = false
          resolve()
        })
      })
    }
  }
}
