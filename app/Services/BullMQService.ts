import Logger from '@ioc:Adonis/Core/Logger'
import SessionCleanupWorker from 'App/Jobs/SessionCleanup'

export default async function startBullMQWorkers() {
  Logger.info('Starting BullMQ Workers...')
  // You can add more workers here as needed
  SessionCleanupWorker.on('completed', (job) => {
    Logger.info(`Job ${job.id} completed`)
  })

  SessionCleanupWorker.on('failed', (job, err) => {
    if (!job) {
      Logger.error(`Job failed with error ${err.message}`)
    }

    Logger.error(`Job ${job!.id} failed with error ${err.message}`)
  })

  // The worker is automatically started when instantiated, but we can add explicit start/stop if needed
  // For now, just importing it is enough to get it running.
}
