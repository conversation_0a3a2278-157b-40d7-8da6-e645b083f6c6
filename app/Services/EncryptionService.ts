import { createCipheriv, createDecipheriv, randomBytes } from 'crypto'

export default class EncryptionService {
  private static instance: EncryptionService
  private algorithm = 'aes-256-cbc'
  private key: Buffer

  private constructor() {
    // Use a 32-byte (256-bit) key derived from APP_KEY
    const appKey = process.env.APP_KEY!
    this.key = Buffer.from(appKey.padEnd(32, '0').slice(0, 32))
  }

  public static getInstance(): EncryptionService {
    if (!EncryptionService.instance) {
      EncryptionService.instance = new EncryptionService()
    }
    return EncryptionService.instance
  }

  public encrypt(text: string): { encryptedData: string; iv: string } {
    const iv = randomBytes(16)
    const cipher = createCipheriv(this.algorithm, this.key, iv)
    let encrypted = cipher.update(text, 'utf8', 'base64')
    encrypted += cipher.final('base64')
    return {
      encryptedData: encrypted,
      iv: iv.toString('hex'),
    }
  }

  public decrypt(encryptedData: string, iv: string): string {
    const decipher = createDecipheriv(this.algorithm, this.key, Buffer.from(iv, 'hex'))
    let decrypted = decipher.update(encryptedData, 'base64', 'utf8')
    decrypted += decipher.final('utf8')
    return decrypted
  }
}
