import axios from 'axios'

export default class ShopifyService {
  private static instance: ShopifyService
  private shopifyUrl: string
  private accessToken: string
  private storefrontToken: string

  private constructor() {
    this.shopifyUrl = process.env.SHOPIFY_SHOP_URL!
    this.accessToken = process.env.SHOPIFY_ACCESS_TOKEN!
    this.storefrontToken = process.env.SHOPIFY_STOREFRONT_TOKEN!
  }

  public static getInstance(): ShopifyService {
    if (!ShopifyService.instance) {
      ShopifyService.instance = new ShopifyService()
    }
    return ShopifyService.instance
  }

  public async graphqlRequest(query: string, variables?: any) {
    try {
      const { data } = await axios.post(
        `${this.shopifyUrl}/admin/api/2024-10/graphql.json`,
        {
          query,
          variables,
        },
        {
          headers: {
            'X-Shopify-Access-Token': this.accessToken,
            'Content-Type': 'application/json',
          },
        }
      )

      if (data.errors) {
        throw new Error(data.errors[0].message)
      }

      return data
    } catch (error) {
      console.error('GraphQL request error:', error)
      throw error
    }
  }

  public async createCustomer(customerData: {
    email?: string
    phone?: string
    firstName?: string
    lastName?: string
    password: string
  }) {
    console.log(customerData)

    try {
      const registerQuery = {
        query: `mutation customerCreate($input: CustomerCreateInput!) {
          customerCreate(input: $input) {
            customer {
              id
              email
            }
            customerUserErrors {
              code
              field
              message
            }
          }
        }`,
        variables: {
          input: {
            email: customerData.email,
            phone: customerData.phone,
            firstName: customerData.firstName,
            lastName: customerData.lastName,
            password: customerData.password,
            acceptsMarketing: false,
          },
        },
      }

      const { data: shopifyResponse } = await axios.post(
        `${this.shopifyUrl}/api/2024-10/graphql.json`,
        registerQuery,
        {
          headers: {
            'X-Shopify-Storefront-Access-Token': this.storefrontToken,
            'Content-Type': 'application/json',
          },
        }
      )

      console.log(shopifyResponse)

      if (shopifyResponse.errors) {
        throw Error(shopifyResponse.errors[0].message)
      }

      // Check for customer-specific errors
      if (shopifyResponse?.data?.customerCreate?.customerUserErrors?.length > 0) {
        const error = shopifyResponse.data.customerCreate.customerUserErrors[0]
        if (error.code === 'TAKEN') {
          throw Error('Email is already taken')
        }
        if (error.code === 'CUSTOMER_DISABLED') {
          throw Error('Customer already registered but not enabled')
        }
        throw Error(error.message)
      }

      return shopifyResponse.data?.customerCreate?.customer
    } catch (error) {
      console.log(error)
      throw error
    }
  }

  public async loginCustomer(email: string, password: string) {
    try {
      const loginQuery = {
        query: `mutation customerAccessTokenCreate($input: CustomerAccessTokenCreateInput!) {
          customerAccessTokenCreate(input: $input) {
            customerAccessToken {
              accessToken
              expiresAt
            }
            customerUserErrors {
              code
              field
              message
            }
          }
        }`,
        variables: {
          input: {
            email,
            password,
          },
        },
      }

      const { data: shopifyResponse } = await axios.post(
        `${this.shopifyUrl}/api/2024-10/graphql.json`,
        loginQuery,
        {
          headers: {
            'X-Shopify-Storefront-Access-Token': this.storefrontToken,
            'Content-Type': 'application/json',
          },
        }
      )

      if (shopifyResponse.errors) {
        throw Error(shopifyResponse.errors[0].message)
      }

      // Check for customer-specific errors
      if (shopifyResponse?.data?.customerAccessTokenCreate?.customerUserErrors?.length > 0) {
        const error = shopifyResponse.data.customerAccessTokenCreate.customerUserErrors[0]
        throw Error(error.message)
      }

      return shopifyResponse.data?.customerAccessTokenCreate?.customerAccessToken
    } catch (error) {
      console.log(error)
      throw error
    }
  }

  public async getCart(customerAccessToken: string, cartId: string) {
    try {
      const { data: shopifyResponse } = await axios.get(
        `${this.shopifyUrl}/api/2024-10/graphql.json`,
        {
          headers: {
            'X-Shopify-Storefront-Access-Token': this.storefrontToken,
            'Content-Type': 'application/json',
            'X-Shopify-Customer-Access-Token': customerAccessToken,
          },
          params: {
            query: `query cart($cartId: ID!) {
              cart(id: $cartId) {
                id
                cost {
                  subtotalAmount {
                    amount
                    currencyCode
                  }
                }
              }
            }`,
            variables: {
              cartId,
            },
          },
        }
      )

      if (shopifyResponse.errors) {
        throw Error(shopifyResponse.errors[0].message)
      }

      return shopifyResponse.data?.cart
    } catch (error) {
      console.log(error)
      throw error
    }
  }
}
