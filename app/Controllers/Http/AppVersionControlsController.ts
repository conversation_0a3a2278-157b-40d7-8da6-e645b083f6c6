import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import Logger from '@ioc:Adonis/Core/Logger'
import AppVersionControl from 'App/Models/AppVersionControl'
import { DateTime } from 'luxon'
import { varcharRule } from 'App/utils'

export default class AppVersionControlsController {
  // R
  public async retrieveLatestPublishedVersion({ response }: HttpContextContract) {
    try {
      const latestPublishVersion = await AppVersionControl.query()
        .whereNotNull('published_at')
        .orderBy('published_at', 'desc')
        .first()

      if (!latestPublishVersion)
        return response.notFound({
          success: false,
          message: 'Currently there is no published version',
        })

      return response.ok({ data: latestPublishVersion })
    } catch (error) {
      Logger.error(error, 'retrieveLatestPublishedVersion')
      return response.badRequest(error)
    }
  }

  // Admin Routes
  // C
  public async createVersion({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'createVersion')

    const user = await auth.authenticate()
    const validationSchema = schema.create({
      version: schema.string(
        varcharRule.concat([
          rules.unique({ table: 'app_version_controls', column: 'version' }),
          rules.regex(/^(\d+\.){2}\d+$/),
        ])
      ),
      description: schema.string(varcharRule),
      publish_now: schema.boolean(),
      force_update: schema.boolean(),
    })

    const { publish_now, ...payload } = await request.validate({ schema: validationSchema })

    try {
      const newAppVersion = await AppVersionControl.create({
        ...payload,
        addedBy: user.id,
        publishedBy: publish_now ? user.id : undefined,
        publishedAt: publish_now ? DateTime.now() : undefined,
      })
      return response.ok({
        success: true,
        message: 'Successfully created new version',
        data: newAppVersion,
      })
    } catch (error) {
      Logger.error(error, 'createVersion')
      return response.status(400).send(error)
    }
  }

  // R
  public async retrieveVersion({ params: { id }, response }: HttpContextContract) {
    try {
      const version = await AppVersionControl.find(id)

      if (!version) return response.notFound({ success: false, message: 'App Version not found' })
      return response.ok({ data: version })
    } catch (error) {
      Logger.error(error, 'retrieveVersion')
      return response.badRequest(error)
    }
  }

  public async retrieveVersions({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)
      // const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      const appVersions = await AppVersionControl.query()
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(appVersions)
    } catch (error) {
      Logger.error(error, 'retrieveVersions')
      return response.badRequest(error)
    }
  }

  // U
  public async updateVersion({ params: { id }, request, response }: HttpContextContract) {
    Logger.info(request.all(), 'updateVersion')

    const validationSchema = schema.create({
      version: schema.string.optional(),
      description: schema.string.optional(varcharRule),
      force_update: schema.boolean.optional(),
    })

    const payload = await request.validate({ schema: validationSchema })

    try {
      const versionToUpdate = await AppVersionControl.findOrFail(id)

      const updatedVersion = await versionToUpdate.merge(payload).save()

      return response.ok({
        success: true,
        message: 'Successfully updated version details',
        data: updatedVersion,
      })
    } catch (error) {
      Logger.error(error, 'updateVersion')
      return response.status(400).send(error)
    }
  }

  public async publishVersion({ params: { id }, response, auth }: HttpContextContract) {
    Logger.info(id, 'publishVersion')

    const user = await auth.authenticate()
    const versionToPublish = await AppVersionControl.findOrFail(id)

    if (versionToPublish.publishedAt)
      return response.forbidden({
        success: false,
        message: 'The version has already been published',
      })

    try {
      versionToPublish.publishedAt = DateTime.now()
      versionToPublish.publishedBy = user.id
      await versionToPublish.save()

      return response.ok({ success: true, message: 'Successfully published the version' })
    } catch (error) {
      Logger.error(error, 'publishVersion')
      return response.badRequest(error)
    }
  }

  public async unpublishVersion({ params: { id }, response }: HttpContextContract) {
    Logger.info(id, 'unpublishVersion')

    const versionToUnpublish = await AppVersionControl.findOrFail(id)

    if (!versionToUnpublish.publishedAt)
      return response.forbidden({ success: false, message: 'The version has not been published' })

    try {
      versionToUnpublish.publishedAt = null
      versionToUnpublish.publishedBy = null
      await versionToUnpublish.save()

      return response.ok({ success: true, message: 'Successfully unpublished the version' })
    } catch (error) {
      Logger.error(error, 'unpublishVersion')
      return response.badRequest(error)
    }
  }

  // D
  public async deleteVersion({ params: { id }, response }: HttpContextContract) {
    const versionToDelete = await AppVersionControl.findOrFail(id)

    try {
      await versionToDelete.delete()
      return response.ok({ success: true, message: 'Successfully deleted the version' })
    } catch (error) {
      Logger.error(error, 'deleteVersion')
      return response.badRequest(error)
    }
  }
}
