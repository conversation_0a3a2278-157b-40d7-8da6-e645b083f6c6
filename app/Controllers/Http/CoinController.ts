import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Env from '@ioc:Adonis/Core/Env'
import { schema } from '@ioc:Adonis/Core/Validator'
import Profile, { SocialType } from 'App/Models/Profile'
import zestLoyaltyPublicAdmin from 'App/Network/zestLoyaltyAdmin'
import ShopifyService from 'App/Services/ShopifyService'

const findCartTotal = async ({
  email,
  password,
  cart_id,
}: {
  email: string
  password: string
  cart_id: string
}) => {
  const shopifyService = ShopifyService.getInstance()
  const shopifyCustomer = await shopifyService.loginCustomer(email, password!)

  const cart = await shopifyService.getCart(shopifyCustomer.accessToken, cart_id)

  return cart.cost.subtotalAmount.amount
}

export default class CoinController {
  public async dailyCheckIn({ response, auth }: HttpContextContract) {
    if (!auth.user) {
      return response.status(401).json({ error: 'User not authenticated' })
    }

    const shopifyProfile = await Profile.query()
      .where('user_id', auth.user.id)
      .where('social_type', SocialType.SHOPIFY)
      .first()

    if (!shopifyProfile) {
      return response.status(400).json({ error: 'User must have a Shopify profile' })
    }

    const shopifyId = shopifyProfile.socialId.split('/').pop()

    try {
      const zestResponse = await zestLoyaltyPublicAdmin.post(
        `/public/shopify/customer/${Env.get('ZEST_LOYALTY_APP_CLIENT_ID')}/login-points`,
        {
          customer_id: shopifyId,
        }
      )

      return response.status(200).json({ data: zestResponse.data })
    } catch (error) {
      console.log(error)

      if (error.message === 'login.bonus.claimed') {
        return response.status(400).json({
          error: 'Daily check-in bonus already claimed today',
          code: 'login.bonus.claimed',
        })
      }

      return response.status(500).json({ error: 'Failed to process daily check-in' })
    }
  }

  public async getBalance({ response, auth }: HttpContextContract) {
    if (!auth.user) {
      return response.status(401).json({ error: 'User not authenticated' })
    }

    const shopifyProfile = await Profile.query()
      .where('user_id', auth.user.id)
      .where('social_type', SocialType.SHOPIFY)
      .first()

    if (!shopifyProfile) {
      return response.status(400).json({ error: 'User must have a Shopify profile' })
    }

    const shopifyId = shopifyProfile.socialId.split('/').pop()

    try {
      const zestResponse = await zestLoyaltyPublicAdmin.get(
        `/public/shopify/customer/${Env.get(
          'ZEST_LOYALTY_APP_CLIENT_ID'
        )}/me?customer_id=${shopifyId}`
      )

      return response.status(200).json({ data: zestResponse.data })
    } catch (error) {
      console.error('Error processing daily check-in:', error)
      return response.status(500).json({ error: 'Failed to retrieve balance' })
    }
  }

  public async getMaxRedeemable({ response, request, auth }: HttpContextContract) {
    const validationSchema = schema.create({
      cart_id: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    if (!auth.user) {
      return response.status(401).json({ error: 'User not authenticated' })
    }

    const shopifyProfile = await Profile.query()
      .where('user_id', auth.user.id)
      .where('social_type', SocialType.SHOPIFY)
      .first()

    if (!shopifyProfile) {
      return response.status(400).json({ error: 'User must have a Shopify profile' })
    }

    const shopifyId = shopifyProfile.socialId.split('/').pop()

    const orderTotal = await findCartTotal({
      email: shopifyProfile.email,
      password: shopifyProfile.getDecryptedPassword()!,
      cart_id: validationData.cart_id,
    })

    try {
      const zestResponse = await zestLoyaltyPublicAdmin.post(
        `/public/shopify/customer/${Env.get('ZEST_LOYALTY_APP_CLIENT_ID')}/max-redeemable`,
        {
          customer_id: shopifyId,
          order_total: orderTotal,
        }
      )

      return response.status(200).json({ data: zestResponse.data })
    } catch (error) {
      console.error('Error getting max redeemable coins:', error)
      return response.status(500).json({ error: 'Failed to get max redeemable coins' })
    }
  }

  public async redeemCoins({ response, auth, request }: HttpContextContract) {
    const validationSchema = schema.create({
      points: schema.number(),
      cart_id: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    if (!auth.user) {
      return response.status(401).json({ error: 'User not authenticated' })
    }

    const shopifyProfile = await Profile.query()
      .where('user_id', auth.user.id)
      .where('social_type', SocialType.SHOPIFY)
      .first()

    if (!shopifyProfile) {
      return response.status(400).json({ error: 'User must have a Shopify profile' })
    }

    const shopifyId = shopifyProfile.socialId.split('/').pop()

    const orderTotal = await findCartTotal({
      email: shopifyProfile.email,
      password: shopifyProfile.getDecryptedPassword()!,
      cart_id: validationData.cart_id,
    })

    try {
      const zestResponse = await zestLoyaltyPublicAdmin.post(
        `/public/shopify/customer/${Env.get('ZEST_LOYALTY_APP_CLIENT_ID')}/redeem`,
        {
          customer_id: shopifyId,
          point: validationData.points,
          order_total: orderTotal,
        }
      )

      return response.status(200).json({ data: zestResponse.data })
    } catch (error) {
      console.log(error)

      if (
        error.message ===
        'redemption in process, to create another redemption please undo the previous redemption or apply the previously created discount code'
      ) {
        return response.status(400).json({
          error:
            'Redemption in process, to create another redemption please undo the previous redemption or apply the previously created discount code',
          code: 'redemption.in.process',
        })
      }

      if (error.message[0].field === 'point' && error.message[0].rule === 'range') {
        return response.status(400).json({
          error: error.message[0].message,
          code: 'points.insufficient',
        })
      }

      return response.status(500).json({ error: 'Failed to redeem coins' })
    }
  }

  public async cancelRedemption({ response, auth }: HttpContextContract) {
    if (!auth.user) {
      return response.status(401).json({ error: 'User not authenticated' })
    }

    const shopifyProfile = await Profile.query()
      .where('user_id', auth.user.id)
      .where('social_type', SocialType.SHOPIFY)
      .first()

    if (!shopifyProfile) {
      return response.status(400).json({ error: 'User must have a Shopify profile' })
    }

    const shopifyId = shopifyProfile.socialId.split('/').pop()

    try {
      const zestResponse = await zestLoyaltyPublicAdmin.post(
        `/public/shopify/customer/${Env.get('ZEST_LOYALTY_APP_CLIENT_ID')}/undo-redeem`,
        {
          customer_id: shopifyId,
        }
      )

      if (zestResponse.data.success) {
        return response.status(200).json(zestResponse.data)
      }

      return response.status(400).json({ error: 'Pending redemption could not be found' })
    } catch (error) {
      console.error(error)

      if (error.message === 'pending redemption could not be found') {
        return response.status(400).json({
          error: 'Pending redemption could not be found',
          code: 'redemption.not.found',
        })
      }

      return response.status(500).json({ error: 'Failed to cancel redemption' })
    }
  }
}
