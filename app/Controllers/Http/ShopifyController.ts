import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
// import Env from '@ioc:Adonis/Core/Env'
import ShopifyService from 'App/Services/ShopifyService'
import Profile from 'App/Models/Profile'
import { SocialType } from 'App/Models/Profile'
import zestReviewAdmin from 'App/Network/zestReviewAdmin'
// import Redis from '@ioc:Adonis/Addons/Redis'
import { rules, schema } from '@ioc:Adonis/Core/Validator'

// const zestReviewAuth = async () => {
//     let headers = {}

//     const zestRedis = await Redis.get('zestReviewAccessToken')

//     if (zestRedis) {
//         headers = {
//             'Authorization': 'Bearer ' + zestRedis
//         }
//     } else {
//         const zestAuth = await zestReviewAdmin.post(`/license/login`, {
//             email: Env.get('ZEST_REVIEW_USERNAME'),
//             password: Env.get('ZEST_REVIEW_PASSWORD')
//         })

//         const accessToken = zestAuth.data.access_token
//         await Redis.set('zestReviewAccessToken', accessToken)

//         headers = {
//             'Authorization': 'Bearer ' + accessToken
//         }
//     }

//     return headers
// }

const getCustomerName = (shopifyProfile, user) => {
  // Check shopifyProfile fields first
  if (shopifyProfile?.username?.trim()) {
    return shopifyProfile.username
  }

  if (shopifyProfile?.firstName?.trim() || shopifyProfile?.lastName?.trim()) {
    const firstName = shopifyProfile.firstName?.trim() || ''
    const lastName = shopifyProfile.lastName?.trim() || ''
    return `${firstName} ${lastName}`.trim()
  }

  // Then check user fields
  if (user?.username?.trim()) {
    return user.username
  }

  if (user?.firstName?.trim() || user?.lastName?.trim()) {
    const firstName = user.firstName?.trim() || ''
    const lastName = user.lastName?.trim() || ''
    return `${firstName} ${lastName}`.trim()
  }

  // Return empty string if no valid names found
  return ''
}

export default class ShopifyController {
  public async getOrders({ request, response, auth }: HttpContextContract) {
    try {
      const {
        cursor,
        limit = 10,
        customer_email,
        financial_status,
        fulfillment_status,
      } = request.qs()

      // If customer_email is not provided, try to get it from the authenticated user
      let customerEmail = customer_email
      if (!customerEmail && auth.user) {
        const shopifyProfile = await Profile.query()
          .where('user_id', auth.user.id)
          .where('social_type', SocialType.SHOPIFY)
          .first()

        if (shopifyProfile) {
          customerEmail = shopifyProfile.email
        }
      }

      const shopifyService = ShopifyService.getInstance()
      const query = `
        query($cursor: String, $customerEmail: String) {
            orders(
                first: ${limit},
                after: $cursor,
                sortKey: PROCESSED_AT
                reverse: true
                query: $customerEmail
            ) {
                edges {
                    node {
                        id
                        email
                        currencyCode
                        lineItems(first: 50) {
                            edges {
                                node {
                                    title
                                    quantity
                                    originalTotalSet {
                                        presentmentMoney {
                                            amount
                                            currencyCode
                                        }
                                    }
                                    discountedTotalSet {
                                        presentmentMoney {
                                            amount
                                            currencyCode
                                        }
                                    }
                                    image {
                                        url
                                    }
                                    variant {
                                        title
                                        product {
                                            id
                                            vendor
                                            title
                                            featuredImage {
                                                url
                                            }
                                        }
                                        image {
                                            url
                                        }
                                    }
                                }
                            }
                        }
                        name
                        processedAt
                        shippingAddress {
                            address1
                            city
                            country
                            zip
                        }
                        billingAddress {
                            address1
                            city
                            country
                            zip
                        }
                        statusPageUrl
                        subtotalPriceSet {
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                        }
                        totalPriceSet {
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                        }
                        totalShippingPriceSet {
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                        }
                        totalTaxSet {
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                        }
                        displayFinancialStatus
                        displayFulfillmentStatus
                        phone
                        metafield(namespace: "mark_as_received", key: "status") {
                            namespace
                            key
                            value
                        }
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                }
            }
            ordersCount(
                query: $customerEmail
            ) {
                count
            }
        }
      `

      // Build the query filters
      let queryFilters: string[] = ['archived:false']
      if (customerEmail) queryFilters.push(`email:${customerEmail}`)
      if (financial_status) queryFilters.push(`financial_status:${financial_status}`)
      if (fulfillment_status) queryFilters.push(`fulfillment_status:${fulfillment_status}`)

      const variables = {
        cursor: cursor || null,
        customerEmail: queryFilters.join(' AND '),
      }

      const result = await shopifyService.graphqlRequest(query, variables)

      return response.ok({ data: result.data })
    } catch (error) {
      console.error('Error fetching orders:', error)
      return response.status(500).json({ error: 'Failed to fetch orders' })
    }
  }

  private readonly METAFIELD_NAMESPACE = 'mark_as_received'
  private readonly METAFIELD_KEY = 'status'

  public async updateOrderReceived({ request, response }: HttpContextContract) {
    try {
      const { id } = request.params()

      const shopifyService = ShopifyService.getInstance()
      const mutation = `#graphql
                mutation OrderUpdateMarkAsReceivedMetafield($input: MetafieldsSetInput!) {
                    metafieldsSet(metafields: [$input]) {
                        metafields {
                            id
                            namespace
                            key
                            value
                        }
                        userErrors {
                            field
                            message
                        }
                    }
                }
            `

      const variables = {
        input: {
          ownerId: `gid://shopify/Order/${id}`,
          namespace: this.METAFIELD_NAMESPACE,
          key: this.METAFIELD_KEY,
          type: 'boolean',
          value: 'true',
        },
      }

      const result = await shopifyService.graphqlRequest(mutation, variables)

      if (result.data.metafieldsSet.userErrors.length > 0) {
        return response.status(400).json({
          error: result.data.metafieldsSet.userErrors[0].message,
        })
      }

      return response.ok({ success: true, data: result.data.metafieldsSet.metafields[0] })
    } catch (error) {
      console.error('Error updating order metafield:', error)
      return response.status(500).json({ error: 'Failed to update order status' })
    }
  }

  public async getOrdersToReview({ response, request, auth }: HttpContextContract) {
    if (!auth.user) {
      return response.status(401).json({ error: 'User not authenticated' })
    }

    const shopifyProfile = await Profile.query()
      .where('user_id', auth.user.id)
      .where('social_type', SocialType.SHOPIFY)
      .first()

    if (!shopifyProfile) {
      return response.status(400).json({ error: 'User must have a Shopify profile' })
    }
    const shopifyId = shopifyProfile.socialId.split('/').pop()

    if (!shopifyId) {
      return response.status(400).json({ error: 'User must have a shopify Id' })
    }

    const page = request.input('page', 1)
    const sort = request.input('sort', 'id:asc')
    const limit = request.input('limit', 10)

    try {
      const zestResponse = await zestReviewAdmin.get(
        `/reviews/public/customer/review-requests?customer_id=${shopifyId}&email=${shopifyProfile.email}&page=${page}&sort=${sort}&limit=${limit}&status=scheduled,sent`
      )

      //         let orderIds: string[] = zestResponse.data.data.map((reviewRequest: { external_order_id: string }) => {
      //             return reviewRequest.external_order_id
      //         })

      //         // need to remove duplicates
      //         orderIds = Array.from(new Set(orderIds))

      //         const shopifyService = ShopifyService.getInstance()
      //         const query = `
      //     query($query: String) {
      //         orders(
      //             first: ${orderIds.length},
      //             sortKey: PROCESSED_AT
      //             reverse: true
      //             query: $query
      //         ) {
      //             edges {
      //                 node {
      //                     id
      //                     email
      //                     currencyCode
      //                     lineItems(first: 50) {
      //                         edges {
      //                             node {
      //                                 title
      //                                 quantity
      //                                 originalTotalSet {
      //                                     presentmentMoney {
      //                                         amount
      //                                         currencyCode
      //                                     }
      //                                 }
      //                                 discountedTotalSet {
      //                                     presentmentMoney {
      //                                         amount
      //                                         currencyCode
      //                                     }
      //                                 }
      //                                 variant {
      //                                     title
      //                                     product {
      //                                         id
      //                                         vendor
      //                                         title
      //                                     }
      //                                     image {
      //                                         url
      //                                     }
      //                                 }
      //                             }
      //                         }
      //                     }
      //                     name
      //                     processedAt
      //                     shippingAddress {
      //                         address1
      //                         city
      //                         country
      //                         zip
      //                     }
      //                     billingAddress {
      //                         address1
      //                         city
      //                         country
      //                         zip
      //                     }
      //                     statusPageUrl
      //                     subtotalPriceSet {
      //                         presentmentMoney {
      //                             amount
      //                             currencyCode
      //                         }
      //                     }
      //                     totalPriceSet {
      //                         presentmentMoney {
      //                             amount
      //                             currencyCode
      //                         }
      //                     }
      //                     totalShippingPriceSet {
      //                         presentmentMoney {
      //                             amount
      //                             currencyCode
      //                         }
      //                     }
      //                     totalTaxSet {
      //                         presentmentMoney {
      //                             amount
      //                             currencyCode
      //                         }
      //                     }
      //                     displayFinancialStatus
      //                     displayFulfillmentStatus
      //                     phone
      //                     metafield(namespace: "mark_as_received", key: "status") {
      //                         namespace
      //                         key
      //                         value
      //                     }
      //                 }
      //                 cursor
      //             }
      //             pageInfo {
      //                 hasNextPage
      //                 endCursor
      //             }
      //         }
      //         ordersCount(
      //             query: $query
      //         ) {
      //             count
      //         }
      //     }
      //   `

      //         // Build the query filters
      //         let queryFilters = orderIds.map((orderId) => {
      //             return `id:${orderId}`
      //         })

      //         const variables = {
      //             query: queryFilters.length > 0 ? queryFilters.join(' OR ') : null,
      //         }

      //         const result = await shopifyService.graphqlRequest(query, variables)

      return response.status(200).json(zestResponse.data)
    } catch (error) {
      console.error('Error fetching review requests:', error)
      return response.status(500).json({ error: 'Failed to fetch review requests' })
    }
  }

  public async submitReview({ request, response, auth }: HttpContextContract) {
    if (!auth.user) {
      return response.status(401).json({ error: 'User not authenticated' })
    }

    const shopifyProfile = await Profile.query()
      .where('user_id', auth.user.id)
      .where('social_type', SocialType.SHOPIFY)
      .first()

    if (!shopifyProfile) {
      return response.status(400).json({ error: 'User must have a Shopify profile' })
    }

    const shopifyId = shopifyProfile.socialId.split('/').pop()

    if (!shopifyId) {
      return response.status(400).json({ error: 'User must have a shopify Id' })
    }

    const customerEmail = shopifyProfile.email

    if (!customerEmail) {
      return response.status(400).json({ error: 'User must have an email' })
    }

    const customerName = getCustomerName(shopifyProfile, auth.user)

    const validationSchema = schema.create({
      title: schema.string.optional(),
      content: schema.string(),
      score: schema.number([rules.range(1, 5)]),
      product_id: schema.string(),
      shopify_order_id: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      const zestResponse = await zestReviewAdmin.post(`/reviews/public/customer/reviews`, {
        customer_id: shopifyId,
        email: customerEmail,
        name: customerName,
        title: validationData.title,
        content: validationData.content,
        score: validationData.score,
        product_id: validationData.product_id,
        shopify_order_id: validationData.shopify_order_id,
      })

      console.log(zestResponse)
      return response.status(200).json({ message: 'Review submitted successfully' })
    } catch (error) {
      console.error('Error submitting review:', error)
      return response.status(500).json({ error: 'Failed to submit review' })
    }
  }

  public async toggleFavourite({ request, response, auth }: HttpContextContract) {
    if (!auth.user) {
      return response.status(401).json({ error: 'User not authenticated' })
    }

    const shopifyProfile = await Profile.query()
      .where('user_id', auth.user.id)
      .where('social_type', SocialType.SHOPIFY)
      .first()

    if (!shopifyProfile) {
      return response.status(400).json({ error: 'User must have a Shopify profile' })
    }

    const validationSchema = schema.create({
      product_id: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      const shopifyService = ShopifyService.getInstance()

      // First get current favorites
      const getFavoritesQuery = `
                query {
                    customer(id: "${shopifyProfile.socialId}") {
                        id
                        metafield(namespace: "custom", key: "favourites") {
                            value
                        }
                    }
                }
            `

      const { data: favoritesData } = await shopifyService.graphqlRequest(getFavoritesQuery)

      // Parse current favorites or initialize empty array
      let favorites: string[] = []
      if (favoritesData?.customer?.metafield?.value) {
        try {
          favorites = JSON.parse(favoritesData.customer.metafield.value)
        } catch (e) {
          console.error('Error parsing favorites:', e)
        }
      }

      // Toggle the product in favorites
      const productId = validationData.product_id
      const productIndex = favorites.indexOf(productId)
      if (productIndex > -1) {
        // Remove if exists
        favorites.splice(productIndex, 1)
      } else {
        // Add if doesn't exist
        favorites.push(productId)
      }

      // Update favorites metafield
      const updateFavoritesMutation = `
                mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
                    metafieldsSet(metafields: $metafields) {
                        metafields {
                            key
                            namespace
                            value
                        }
                        userErrors {
                            field
                            message
                            code
                        }
                    }
                }
            `

      const variables = {
        metafields: [
          {
            key: 'favourites',
            namespace: 'custom',
            ownerId: shopifyProfile.socialId,
            value: JSON.stringify(favorites),
          },
        ],
      }

      await shopifyService.graphqlRequest(updateFavoritesMutation, variables)

      return response.ok({
        success: true,
      })
    } catch (error) {
      console.error('Error toggling favorite:', error)
      return response.status(500).json({
        error: 'Failed to toggle favorite',
        details: error.message,
      })
    }
  }

  public async getFavorites({ response, auth }: HttpContextContract) {
    if (!auth.user) {
      return response.status(401).json({ error: 'User not authenticated' })
    }

    const shopifyProfile = await Profile.query()
      // .where('email', '<EMAIL>')
      .where('user_id', auth.user.id)
      .where('social_type', SocialType.SHOPIFY)
      .first()

    if (!shopifyProfile) {
      return response.status(400).json({ error: 'User must have a Shopify profile' })
    }

    try {
      const shopifyService = ShopifyService.getInstance()

      const getFavoritesQuery = `
                query {
                    customer(id: "${shopifyProfile.socialId}") {
                        id
                        metafield(namespace: "custom", key: "favourites") {
                            value
                        }
                    }
                }
            `

      const { data: favoritesData } = await shopifyService.graphqlRequest(getFavoritesQuery)

      // Parse favorites or return empty array
      let favorites: string[] = []
      if (favoritesData?.customer?.metafield?.value) {
        try {
          favorites = JSON.parse(favoritesData.customer.metafield.value)
        } catch (e) {
          console.error('Error parsing favorites:', e)
        }
      }

      return response.ok({ data: favorites })
    } catch (error) {
      console.error('Error fetching favorites:', error)
      return response.status(500).json({
        error: 'Failed to fetch favorites',
        details: error.message,
      })
    }
  }

  public async setFavourites({ request, response, auth }: HttpContextContract) {
    if (!auth.user) {
      return response.status(401).json({ error: 'User not authenticated' })
    }

    const shopifyProfile = await Profile.query()
      // .where('email', '<EMAIL>')
      .where('user_id', auth.user.id)
      .where('social_type', SocialType.SHOPIFY)
      .first()

    if (!shopifyProfile) {
      return response.status(400).json({ error: 'User must have a Shopify profile' })
    }

    const validationSchema = schema.create({
      product_ids: schema.array().members(schema.string()),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      const shopifyService = ShopifyService.getInstance()

      // Update favorites metafield
      const updateFavoritesMutation = `
                mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
                    metafieldsSet(metafields: $metafields) {
                        metafields {
                            key
                            namespace
                            value
                        }
                        userErrors {
                            field
                            message
                            code
                        }
                    }
                }
            `

      const variables = {
        metafields: [
          {
            key: 'favourites',
            namespace: 'custom',
            ownerId: shopifyProfile.socialId,
            value: JSON.stringify(validationData.product_ids),
          },
        ],
      }

      await shopifyService.graphqlRequest(updateFavoritesMutation, variables)

      return response.ok({
        success: true,
      })
    } catch (error) {
      console.error('Error toggling favorite:', error)
      return response.status(500).json({
        error: 'Failed to toggle favorite',
        details: error.message,
      })
    }
  }
}
