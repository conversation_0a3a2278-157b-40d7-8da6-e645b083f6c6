// import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
// import { rules, schema } from '@ioc:Adonis/Core/Validator'
// import Database from '@ioc:Adonis/Lucid/Database'
// import ListingType from 'App/Models/ListingType'
// import _ from 'lodash'
// import Logger from '@ioc:Adonis/Core/Logger'

// export default class ListingTypesController {
//   // C
//   public async createListingType({ request, response }: HttpContextContract) {
//     try {
//       const validationSchema = schema.create({
//         name: schema.string([rules.maxLength(255)]),
//         description: schema.string([rules.maxLength(255)]),
//         index: schema.number([rules.unique({ table: 'listing_types', column: 'index' })]),
//       })

//       const validatedData = await request.validate({ schema: validationSchema })

//       const isExistListingType = await ListingType.findBy('name', validatedData.name)

//       if (isExistListingType) {
//         return response.conflict({
//           success: false,
//           message: `Listing type with name: ${isExistListingType.name} already exist`,
//         })
//       }

//       const listingType = await Database.transaction(async (trx) => {
//         const newListingType = new ListingType()
//         newListingType.name = validatedData.name
//         newListingType.description = validatedData.description
//         newListingType.index = validatedData.index
//         newListingType.useTransaction(trx)
//         await newListingType.save()

//         return newListingType
//       })

//       return response.ok({
//         success: listingType.id ? true : false,
//         data: listingType,
//         message: 'Successfully created a listing type',
//       })
//     } catch (error) {
//       Logger.error(error, 'createListingType')
//       return response.badRequest(error)
//     }
//   }

//   // R
//   public async findListingTypes({ request, response }: HttpContextContract) {
//     try {
//       const page = request.input('page', 1)
//       const sort = request.input('sort', 'created_at:desc').split(':')
//       const limit = request.input('limit', 20)

//       const listingTypes = await ListingType.query().orderBy(sort[0], sort[1]).paginate(page, limit)

//       return response.ok(listingTypes)
//     } catch (error) {
//       Logger.error(error, 'findListingTypes')
//       return response.badRequest(error)
//     }
//   }

//   public async findListingType({ params: { id }, response }: HttpContextContract) {
//     try {
//       const listingType = await ListingType.query().andWhere('id', id).first()

//       if (!listingType) {
//         return response.notFound({ success: false, message: 'Listing type not found' })
//       }

//       return response.ok({ data: listingType })
//     } catch (error) {
//       Logger.error(error, 'findListingType')
//       return response.badRequest(error)
//     }
//   }

//   // U
//   public async updateListingType({ params: { id }, request, response }: HttpContextContract) {
//     try {
//       const findListingType = await ListingType.find(id)

//       if (!findListingType) {
//         return response.notFound({ success: false, message: 'Listing type not found' })
//       }

//       const validationSchema = schema.create({
//         name: schema.string.optional([rules.maxLength(255)]),
//         description: schema.string.optional([rules.maxLength(255)]),
//         is_hidden: schema.boolean.optional(),
//         index: schema.number.optional([rules.unique({ table: 'listing_types', column: 'index' })]),
//       })

//       const validatedData = await request.validate({ schema: validationSchema })

//       const isExistListingType = await ListingType.findBy('name', validatedData.name)

//       if (isExistListingType) {
//         return response.conflict({
//           success: false,
//           message: `Listing type with name: ${isExistListingType.name} already exist`,
//         })
//       }

//       const result = await Database.transaction(async (trx) => {
//         findListingType.merge({ ...validatedData })
//         findListingType.useTransaction(trx)
//         await findListingType.save()

//         return findListingType
//       })

//       return response.ok({
//         success: true,
//         data: result,
//         message: 'Successfully updated a listing type',
//       })
//     } catch (error) {
//       Logger.error(error, 'updateListingType')
//       return response.badRequest(error)
//     }
//   }

//   // D
//   public async deleteListingType({ params: { id }, response }: HttpContextContract) {
//     try {
//       const findListingType = await ListingType.find(id)

//       if (!findListingType) {
//         return response.notFound({ success: false, message: 'Listing type not found' })
//       }

//       const result = await Database.transaction(async (trx) => {
//         findListingType.useTransaction(trx)
//         await findListingType.delete()

//         return {
//           success: true,
//         }
//       })

//       return response.ok({
//         success: result.success,
//         message: 'Successfully deleted a listing type',
//       })
//     } catch (error) {
//       Logger.error(error, 'deleteListingType')
//       return response.badRequest(error)
//     }
//   }
// }
