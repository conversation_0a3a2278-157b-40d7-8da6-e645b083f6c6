import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Region from 'App/Models/Region'
import _ from 'lodash'
import Logger from '@ioc:Adonis/Core/Logger'
import Drive from '@ioc:Adonis/Core/Drive'
import { disk_name, remotePathRegion } from 'App/utils'

const imageFileSpec = {
  size: '10mb',
  extnames: ['jpg', 'png', 'jpeg', 'JPG', 'PNG', 'JPEG'],
}

export default class RegionsController {
  // R
  public async findRegions({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)

      const regions = await Region.query()
        .where('is_hidden', false)
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(regions)
    } catch (error) {
      Logger.error(error, 'findRegions')
      return response.badRequest(error)
    }
  }

  public async findRegion({ params: { id }, response }: HttpContextContract) {
    try {
      const region = await Region.query().where('id', id).andWhere('is_hidden', false).first()

      if (!region) {
        return response.notFound({ success: false, message: 'Region not found' })
      }

      return response.ok({ data: region })
    } catch (error) {
      Logger.error(error, 'findRegion')
      return response.badRequest(error)
    }
  }

  public async findRegionWithSlug({ params: { slug }, response }: HttpContextContract) {
    try {
      const region = await Region.query().where('slug', slug).andWhere('is_hidden', false).first()

      if (!region) {
        return response.notFound({ success: false, message: 'Region not found' })
      }

      return response.ok({ data: region })
    } catch (error) {
      Logger.error(error, 'findRegionWithSlug')
      return response.badRequest(error)
    }
  }

  // Admin Routes

  // C
  public async createRegion({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        name: schema.string([rules.minLength(1), rules.maxLength(255)]),
        description: schema.string([rules.minLength(1), rules.maxLength(255)]),
        image_file: schema.file.optional(imageFileSpec, [rules.requiredIfNotExists('image_url')]),
        image_url: schema.string.optional([
          rules.requiredIfNotExists('image_file'),
          rules.minLength(1),
          rules.maxLength(255),
        ]),
      })

      const { image_file, image_url, ...validatedData } = await request.validate({
        schema: validationSchema,
      })

      const isExistRegion = await Region.findBy('name', validatedData.name)

      if (isExistRegion) {
        return response.conflict({
          success: false,
          message: `The region: ${isExistRegion.name} already exist`,
        })
      }

      const region = await Database.transaction(async (trx) => {
        const newRegion = await Region.create({ ...validatedData }, { client: trx })

        if (image_file) {
          const remoteName = `${newRegion.name.toLowerCase().replace(/\s/g, '_')}.${
            image_file.extname
          }`
          await image_file.moveToDisk(remotePathRegion, { name: remoteName }, disk_name)
          newRegion.imageUrl = `${remoteName}`
        } else if (image_url) {
          newRegion.imageUrl = image_url
        }
        await newRegion.useTransaction(trx).save()

        return newRegion
      })

      return response.ok({
        success: region.id ? true : false,
        message: 'Successfully created a region',
        data: region,
      })
    } catch (error) {
      Logger.error(error, 'createRegion')
      return response.badRequest(error)
    }
  }

  // R
  public async findRegionsAdmin({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)

      const regions = await Region.query().orderBy(sort[0], sort[1]).paginate(page, limit)

      return response.ok(regions)
    } catch (error) {
      Logger.error(error, 'findRegionsAdmin')
      return response.badRequest(error)
    }
  }

  public async findRegionAdmin({ params: { id }, response }: HttpContextContract) {
    try {
      const region = await Region.find(id)

      if (!region) {
        return response.notFound({ success: false, message: 'Region not found' })
      }

      return response.ok({ data: region })
    } catch (error) {
      Logger.error(error, 'findRegionAdmin')
      return response.badRequest(error)
    }
  }

  public async findRegionWithSlugAdmin({ params: { slug }, response }: HttpContextContract) {
    try {
      const region = await Region.findBy('slug', slug)

      if (!region) {
        return response.notFound({ success: false, message: 'Region not found' })
      }

      return response.ok({ data: region })
    } catch (error) {
      Logger.error(error, 'findRegionWithSlugAdmin')
      return response.badRequest(error)
    }
  }

  // U
  public async updateRegion({ params: { id }, request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        name: schema.string.optional([rules.maxLength(255)]),
        description: schema.string.optional([rules.maxLength(255)]),
        image_file: schema.file.optional(imageFileSpec, [rules.requiredIfNotExists('image_url')]),
        image_url: schema.string.optional([
          rules.requiredIfNotExists('image_file'),
          rules.maxLength(255),
        ]),
        is_hidden: schema.boolean.optional(),
      })

      const findRegion = await Region.find(id)

      if (!findRegion) {
        return response.notFound({ success: false, message: 'Region not found' })
      }

      const { image_file, image_url, ...validatedData } = await request.validate({
        schema: validationSchema,
      })

      const isExistRegion = validatedData.name
        ? await Region.findBy('name', validatedData.name)
        : null

      if (isExistRegion) {
        return response.conflict({
          success: false,
          message: `The region: ${isExistRegion.name} already exist`,
        })
      }

      const result = await Database.transaction(async (trx) => {
        findRegion.merge({ ...validatedData })

        await Drive.use(disk_name).delete(
          remotePathRegion + findRegion.imageUrl.split(remotePathRegion)[1]
        )

        if (image_file) {
          const remoteName = `${findRegion.name.toLowerCase().replace(/\s/g, '_')}.${
            image_file.extname
          }`
          await image_file.moveToDisk(remotePathRegion, { name: remoteName }, disk_name)
          findRegion.imageUrl = `${remoteName}`
        } else if (image_url) {
          findRegion.imageUrl = image_url
        }

        await findRegion.useTransaction(trx).save()
        return findRegion
      })

      return response.ok({
        success: true,
        message: 'Successfully updated a region',
        data: result,
      })
    } catch (error) {
      Logger.error(error, 'updateRegion')
      return response.badRequest(error)
    }
  }

  public async updateRegionWithSlug({ params: { slug }, request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        name: schema.string.optional([rules.maxLength(255)]),
        description: schema.string.optional([rules.maxLength(255)]),
        image_file: schema.file.optional(imageFileSpec, [rules.requiredIfNotExists('image_url')]),
        image_url: schema.string.optional([
          rules.requiredIfNotExists('image_file'),
          rules.maxLength(255),
        ]),
        is_hidden: schema.boolean.optional(),
      })

      const findRegion = await Region.findBy('slug', slug)

      if (!findRegion) {
        return response.notFound({ success: false, message: 'Region not found' })
      }

      const { image_file, image_url, ...validatedData } = await request.validate({
        schema: validationSchema,
      })

      const isExistRegion = validatedData.name
        ? await Region.findBy('name', validatedData.name)
        : null

      if (isExistRegion) {
        return response.conflict({
          success: false,
          message: `The region: ${isExistRegion.name} already exist`,
        })
      }

      const result = await Database.transaction(async (trx) => {
        findRegion.merge({ ...validatedData })

        await Drive.use(disk_name).delete(
          remotePathRegion + findRegion.imageUrl.split(remotePathRegion)[1]
        )

        if (image_file) {
          const remoteName = `${findRegion.name.toLowerCase().replace(/\s/g, '_')}.${
            image_file.extname
          }`
          await image_file.moveToDisk(remotePathRegion, { name: remoteName }, disk_name)
          findRegion.imageUrl = `${remoteName}`
        } else if (image_url) {
          findRegion.imageUrl = image_url
        }

        await findRegion.useTransaction(trx).save()
        return findRegion
      })

      return response.ok({
        success: true,
        message: 'Successfully updated a region',
        data: result,
      })
    } catch (error) {
      Logger.error(error, 'updateRegionWithSlug')
      return response.badRequest(error)
    }
  }

  // D
  public async deleteRegion({ params: { id }, response }: HttpContextContract) {
    try {
      const findRegion = await Region.find(id)

      if (!findRegion) {
        return response.notFound({ success: false, message: 'Region not found' })
      }

      const toBeDeletedUrl = remotePathRegion + findRegion.imageUrl.split(remotePathRegion)[1]

      const result = await Database.transaction(async (trx) => {
        findRegion.useTransaction(trx)
        await findRegion.delete()

        return {
          success: true,
        }
      })

      if (result.success) {
        await Drive.use(disk_name).delete(toBeDeletedUrl)
      }

      return response.ok({
        success: result.success,
        message: 'Successfully deleted a region',
      })
    } catch (error) {
      Logger.error(error, 'deleteRegion')
      return response.badRequest(error)
    }
  }

  public async deleteRegionWithSlug({ params: { slug }, response }: HttpContextContract) {
    try {
      const findRegion = await Region.findBy('slug', slug)

      if (!findRegion) {
        return response.notFound({ success: false, message: 'Region not found' })
      }

      const toBeDeletedUrl = remotePathRegion + findRegion.imageUrl.split(remotePathRegion)[1]

      const result = await Database.transaction(async (trx) => {
        findRegion.useTransaction(trx)
        await findRegion.delete()

        return {
          success: true,
        }
      })

      if (result.success) {
        await Drive.use(disk_name).delete(toBeDeletedUrl)
      }

      return response.ok({
        success: result.success,
        message: 'Successfully deleted a region',
      })
    } catch (error) {
      Logger.error(error, 'deleteRegionWithSlug')
      return response.badRequest(error)
    }
  }
}
