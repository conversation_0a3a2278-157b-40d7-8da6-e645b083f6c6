import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Logger from '@ioc:Adonis/Core/Logger'
import Env from '@ioc:Adonis/Core/Env'
import {
  remotePathActivity,
  remotePathListing,
  remotePathRegion,
  remotePathUserProfile,
  remotePathUserVerify,
  generateRandomCode,
} from 'App/utils'

export const PUBLIC_DISKNAME = Env.get('PUBLIC_DRIVE_DISK')
export const PRIVATE_DISKNAME = Env.get('PRIVATE_DRIVE_DISK')

export default class FilesController {
  // TODO: need to authenticate as admin
  public async upload({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      name: schema.string.optional(),
      file: schema.file({
        size: '20mb',
        extnames: ['pdf', 'png', 'jpg', 'jpeg', 'txt', 'mp4', '3gp', 'heic'],
      }),
      type: schema.enum(['listing', 'activity', 'region', 'user_profile', 'user_verify']),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    let remotePath = remotePathListing
    let disk = PUBLIC_DISKNAME // most images can be public
    switch (validationData.type) {
      case 'activity': {
        remotePath = remotePathActivity
        break
      }
      case 'region': {
        remotePath = remotePathRegion
        break
      }
      case 'user_profile': {
        remotePath = remotePathUserProfile
        break
      }
      case 'user_verify': {
        remotePath = remotePathUserVerify
        disk = PRIVATE_DISKNAME // user verification should be private
        break
      }
    }

    try {
      if (validationData.file) {
        const remoteName = `${validationData.name ? validationData.name : ''}_${generateRandomCode(
          10
        )}.${validationData.file.extname}`

        await validationData.file.moveToDisk(remotePath, { name: remoteName }, disk)

        return response.ok({ success: true, data: { url: remoteName } })
      }
    } catch (error) {
      Logger.error(error, 'upload')
      return response.status(400).send({
        success: false,
      })
    }
  }
}
