import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Activity from 'App/Models/Activity'
import _ from 'lodash'
import Logger from '@ioc:Adonis/Core/Logger'
import Drive from '@ioc:Adonis/Core/Drive'
import { disk_name, remotePathActivity } from 'App/utils'

const imageFileSpec = {
  size: '10mb',
  extnames: ['jpg', 'png', 'jpeg', 'JPG', 'PNG', 'JPEG'],
}

export default class ActivitiesController {
  // R
  public async findActivities({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)

      const activities = await Activity.query()
        .where('is_hidden', false)
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(activities)
    } catch (error) {
      Logger.error(error, 'findActivities')
      return response.badRequest(error)
    }
  }

  public async findActivity({ params: { id }, response }: HttpContextContract) {
    try {
      const activity = await Activity.query().where('id', id).andWhere('is_hidden', false).first()

      if (!activity) {
        return response.notFound({ success: false, message: 'Activity not found' })
      }

      return response.ok({ data: activity })
    } catch (error) {
      Logger.error(error, 'findActivity')
      return response.badRequest(error)
    }
  }

  public async findActivityWithSlug({ params: { slug }, response }: HttpContextContract) {
    try {
      const activity = await Activity.query()
        .where('slug', slug)
        .andWhere('is_hidden', false)
        .first()

      if (!activity) {
        return response.notFound({ success: false, message: 'Activity not found' })
      }

      return response.ok({ data: activity })
    } catch (error) {
      Logger.error(error, 'findActivityWithSlug')
      return response.badRequest(error)
    }
  }

  // Admin Routes

  // C
  public async createActivity({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        name: schema.string([rules.minLength(1), rules.maxLength(255)]),
        description: schema.string([rules.minLength(1), rules.maxLength(255)]),
        image_file: schema.file.optional(imageFileSpec, [rules.requiredIfNotExists('image_url')]),
        image_url: schema.string.optional([
          rules.requiredIfNotExists('image_file'),
          rules.minLength(1),
          rules.maxLength(255),
        ]),
      })

      const { image_file, image_url, ...validatedData } = await request.validate({
        schema: validationSchema,
      })

      const activity = await Database.transaction(async (trx) => {
        const newActivity = await Activity.create({ ...validatedData }, { client: trx })

        if (image_file) {
          const remoteName = `${newActivity.name.toLowerCase().replace(/\s/g, '_')}.${
            image_file.extname
          }`
          await image_file.moveToDisk(remotePathActivity, { name: remoteName }, disk_name)
          newActivity.imageUrl = `${remoteName}`
        } else if (image_url) {
          newActivity.imageUrl = image_url
        }

        await newActivity.useTransaction(trx).save()

        return newActivity
      })

      return response.ok({
        success: activity.id ? true : false,
        message: 'Successfully created an activity',
        data: activity,
      })
    } catch (error) {
      Logger.error(error, 'createActivity')
      return response.badRequest(error)
    }
  }

  // R
  public async findActivitiesAdmin({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)
      const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      const activities = await Activity.filter(filters)
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(activities)
    } catch (error) {
      Logger.error(error, 'findActivitiesAdmin')
      return response.badRequest(error)
    }
  }

  public async findActivityAdmin({ params: { id }, response }: HttpContextContract) {
    try {
      const activity = await Activity.find(id)

      if (!activity) {
        return response.notFound({ success: false, message: 'Activity not found' })
      }

      return response.ok({ data: activity })
    } catch (error) {
      Logger.error(error, 'findActivityAdmin')
      return response.badRequest(error)
    }
  }

  public async findActivityWithSlugAdmin({ params: { slug }, response }: HttpContextContract) {
    try {
      const activity = await Activity.query().where('slug', slug).first()

      if (!activity) {
        return response.notFound({ success: false, message: 'Activity not found' })
      }

      return response.ok({ data: activity })
    } catch (error) {
      Logger.error(error, 'findActivityWithSlugAdmin')
      return response.badRequest(error)
    }
  }

  // U
  public async updateActivity({ params: { id }, request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        name: schema.string.optional([rules.maxLength(255)]),
        description: schema.string.optional([rules.maxLength(255)]),
        image_file: schema.file.optional(imageFileSpec, [rules.requiredIfNotExists('image_url')]),
        image_url: schema.string.optional([
          rules.requiredIfNotExists('image_file'),
          rules.maxLength(255),
        ]),
        is_hidden: schema.boolean.optional(),
      })

      const findActivity = await Activity.find(id)

      if (!findActivity) {
        return response.notFound({ success: false, message: 'Activity not found' })
      }

      const { image_file, image_url, ...validatedData } = await request.validate({
        schema: validationSchema,
      })

      const result = await Database.transaction(async (trx) => {
        findActivity.merge({ ...validatedData })

        await Drive.use(disk_name).delete(
          remotePathActivity + findActivity.imageUrl.split(remotePathActivity)[1]
        )

        if (image_file) {
          const remoteName = `${findActivity.name.toLowerCase().replace(/\s/g, '_')}.${
            image_file.extname
          }`
          await image_file.moveToDisk(remotePathActivity, { name: remoteName }, disk_name)
          findActivity.imageUrl = `${remoteName}`
        } else if (image_url) {
          findActivity.imageUrl = image_url
        }

        await findActivity.useTransaction(trx).save()

        return findActivity
      })

      return response.ok({
        success: true,
        message: 'Successfully updated an activity',
        data: result,
      })
    } catch (error) {
      Logger.error(error, 'updateActivity')
      return response.badRequest(error)
    }
  }

  public async updateActivityWithSlug({
    params: { slug },
    request,
    response,
  }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        name: schema.string.optional([rules.maxLength(255)]),
        description: schema.string.optional([rules.maxLength(255)]),
        image_file: schema.file.optional(imageFileSpec, [rules.requiredIfNotExists('image_url')]),
        image_url: schema.string.optional([
          rules.requiredIfNotExists('image_file'),
          rules.maxLength(255),
        ]),
        is_hidden: schema.boolean.optional(),
      })

      const findActivity = await Activity.findBy('slug', slug)

      if (!findActivity) {
        return response.notFound({ success: false, message: 'Activity not found' })
      }

      const { image_file, image_url, ...validatedData } = await request.validate({
        schema: validationSchema,
      })

      const result = await Database.transaction(async (trx) => {
        findActivity.merge({ ...validatedData })

        await Drive.use(disk_name).delete(
          remotePathActivity + findActivity.imageUrl.split(remotePathActivity)[1]
        )

        if (image_file) {
          const remoteName = `${findActivity.name.toLowerCase().replace(/\s/g, '_')}.${
            image_file.extname
          }`
          await image_file.moveToDisk(remotePathActivity, { name: remoteName }, disk_name)
          findActivity.imageUrl = `${remoteName}`
        } else if (image_url) {
          findActivity.imageUrl = image_url
        }

        await findActivity.useTransaction(trx).save()

        return findActivity
      })

      return response.ok({
        success: true,
        message: 'Successfully updated an activity',
        data: result,
      })
    } catch (error) {
      Logger.error(error, 'updateActivityWithSlug')
      return response.badRequest(error)
    }
  }

  // D
  public async deleteActivity({ params: { id }, response }: HttpContextContract) {
    try {
      const findActivity = await Activity.find(id)

      if (!findActivity) {
        return response.notFound({ success: false, message: 'Activity not found' })
      }

      const toBeDeletedUrl = remotePathActivity + findActivity.imageUrl.split(remotePathActivity)[1]

      const result = await Database.transaction(async (trx) => {
        findActivity.useTransaction(trx)
        await findActivity.delete()

        return {
          success: true,
        }
      })

      if (result.success) {
        await Drive.use(disk_name).delete(toBeDeletedUrl)
      }

      return response.ok({
        success: result.success,
        message: 'Successfully deleted an activity',
      })
    } catch (error) {
      Logger.error(error, 'deleteActivity')
      return response.badRequest(error)
    }
  }

  public async deleteActivityWithSlug({ params: { slug }, response }: HttpContextContract) {
    try {
      const findActivity = await Activity.findBy('slug', slug)

      if (!findActivity) {
        return response.notFound({ success: false, message: 'Activity not found' })
      }

      const toBeDeletedUrl = remotePathActivity + findActivity.imageUrl.split(remotePathActivity)[1]

      const result = await Database.transaction(async (trx) => {
        findActivity.useTransaction(trx)
        await findActivity.delete()

        return {
          success: true,
        }
      })

      if (result.success) {
        await Drive.use(disk_name).delete(toBeDeletedUrl)
      }

      return response.ok({
        success: result.success,
        message: 'Successfully deleted an activity',
      })
    } catch (error) {
      Logger.error(error, 'deleteActivityWithSlug')
      return response.badRequest(error)
    }
  }
}
