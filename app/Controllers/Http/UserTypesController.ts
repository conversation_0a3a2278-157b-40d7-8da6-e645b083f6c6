// import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
// import { rules, schema } from '@ioc:Adonis/Core/Validator'
// import Database from '@ioc:Adonis/Lucid/Database'
// import UserType from 'App/Models/UserType'
// import _ from 'lodash'
// import Logger from '@ioc:Adonis/Core/Logger'

// export default class UserTypesController {
//   // C
//   public async createUserType({ request, response }: HttpContextContract) {
//     try {
//       const validationSchema = schema.create({
//         name: schema.string([rules.maxLength(255)]),
//         description: schema.string([rules.maxLength(255)]),
//       })

//       const validatedData = await request.validate({ schema: validationSchema })

//       const isExistUserType = await UserType.findBy('name', validatedData.name)

//       if (isExistUserType) {
//         return response.conflict({
//           success: false,
//           message: `User type with name: ${isExistUserType.name} already exist`,
//         })
//       }

//       const userType = await Database.transaction(async (trx) => {
//         const newUserType = await UserType.create({ ...validatedData }, { client: trx })

//         return newUserType
//       })

//       return response.ok({
//         success: userType.id ? true : false,
//         data: userType,
//         message: 'Successfully created a user type',
//       })
//     } catch (error) {
//       Logger.error(error, 'createUserType')
//       return response.badRequest(error)
//     }
//   }

//   // R
//   public async findUserTypes({ request, response }: HttpContextContract) {
//     try {
//       const page = request.input('page', 1)
//       const sort = request.input('sort', 'created_at:desc').split(':')
//       const limit = request.input('limit', 20)

//       const userTypes = await UserType.query().orderBy(sort[0], sort[1]).paginate(page, limit)

//       return response.ok(userTypes)
//     } catch (error) {
//       Logger.error(error, 'findUserTypes')
//       return response.badRequest(error)
//     }
//   }

//   public async findUserType({ params: { id }, response }: HttpContextContract) {
//     try {
//       const userType = await UserType.find(id)

//       if (!userType) {
//         return response.notFound({ success: false, message: 'User type not found' })
//       }

//       return response.ok({ data: userType })
//     } catch (error) {
//       Logger.error(error, 'findLisitngType')
//       return response.badRequest(error)
//     }
//   }

//   // U
//   public async updateUserType({ params: { id }, request, response }: HttpContextContract) {
//     try {
//       const findUserType = await UserType.find(id)

//       if (!findUserType) {
//         return response.notFound({ success: false, message: 'User type not found' })
//       }

//       const validationSchema = schema.create({
//         name: schema.string.optional([rules.maxLength(255)]),
//         description: schema.string.optional([rules.maxLength(255)]),
//         is_hidden: schema.boolean.optional(),
//       })

//       const validatedData = await request.validate({ schema: validationSchema })

//       const isExistUserType = await UserType.findBy('name', validatedData.name)

//       if (isExistUserType) {
//         return response.conflict({
//           success: false,
//           message: `User type with name: ${isExistUserType.name} already exist`,
//         })
//       }

//       const result = await Database.transaction(async (trx) => {
//         findUserType.merge({ ...validatedData })
//         findUserType.useTransaction(trx)
//         await findUserType.save()

//         return findUserType
//       })

//       return response.ok({
//         success: true,
//         data: result,
//         message: 'Successfully updated a user type',
//       })
//     } catch (error) {
//       Logger.error(error, 'updateUserType')
//       return response.badRequest(error)
//     }
//   }

//   // D
//   public async deleteUserType({ params: { id }, response }: HttpContextContract) {
//     try {
//       const findUserType = await UserType.find(id)

//       if (!findUserType) {
//         return response.notFound({ success: false, message: 'User type not found' })
//       }

//       const result = await Database.transaction(async (trx) => {
//         findUserType.useTransaction(trx)
//         await findUserType.delete()

//         return {
//           success: true,
//         }
//       })

//       return response.ok({
//         success: result.success,
//         message: 'Successfully deleted a user type',
//       })
//     } catch (error) {
//       Logger.error(error, 'deleteUserType')
//       return response.badRequest(error)
//     }
//   }
// }
