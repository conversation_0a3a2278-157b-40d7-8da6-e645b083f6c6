import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import { generateRandomCode, getSingpassConfig } from 'App/utils'
import Env from '@ioc:Adonis/Core/Env'
import { schema } from '@ioc:Adonis/Core/Validator'
import axios from 'axios'
import _ from 'lodash'
import * as utils from '../../utils'
import User from 'App/Models/User'
import { DateTime } from 'luxon'
import UserVerificationRequest from 'App/Models/UserVerificationRequest'
import {
  UserVerificationStatus,
  UserVerificationType,
} from 'Contracts/user_verification_request_type'

export default class SingpassV3AuthsController {
  public async initializeSingpassOAuth({ response, auth }: HttpContextContract) {
    try {
      const user = await auth.authenticate()

      console.log('👶 1 of 2: Generate random nonce, state and secret strings.')
      const nonceString = generateRandomCode(255)
      const stateString = generateRandomCode(255)
      const secretString = generateRandomCode(255)

      console.log('👶 2 of 3: Update random nonce, state and secret strings for the user.')

      await user
        .merge({
          singpassNonce: nonceString,
          singpassState: stateString,
          singpassSecret: secretString,
        })
        .save()

      console.log('👶 3 of 3: Generate full Singpass Myinfo authorise url.')
      const fullMyinfoAuthoriseUrl =
        Env.get('SINGPASS_MYINFO_URL') +
        '/authorise' +
        '?client_id=' +
        encodeURIComponent(Env.get('SINGPASS_CLIENT_ID')) +
        '&attributes=' +
        encodeURIComponent(Env.get('SINGPASS_SCOPE')) +
        '&purpose=' +
        encodeURIComponent(Env.get('SINGPASS_PURPOSE')) +
        '&state=' +
        encodeURIComponent(stateString) +
        '&redirect_uri=' +
        encodeURIComponent(Env.get('SINGPASS_REDIRECT_URI'))

      return response.ok({
        success: true,
        message: 'initializeSingpassOAuth Success',
        data: fullMyinfoAuthoriseUrl,
      })
    } catch (error) {
      console.log(error)
      return response.badRequest({ success: false, message: 'initializeSingpassOAuth Failed' })
    }
  }

  public async singpassMyInfoProcess({ request, response }: HttpContextContract) {
    try {
      let isSuccess = false

      console.log(
        '👶 1 of 12: Retrieve Singpass login pki authorization code and authorization state from client mobile app.'
      )
      const validationSchema = schema.create({
        authorization_code: schema.string(),
        authorization_state: schema.string(),
      })

      const { authorization_code: authorizationCode, authorization_state: authorizationState } =
        await request.validate({
          schema: validationSchema,
        })

      console.log(
        '👶 2 of 12: Retrieve a user document with Singpass nonce and Singpass state from Users table.'
      )
      const user = await User.findByOrFail('singpass_state', authorizationState)

      const userNonce = user.singpassNonce
      const userState = user.singpassState

      if (authorizationState !== userState) {
        console.log(
          '❌ 2 of 12: Retrieve a user document with Singpass nonce and Singpass state from Users database.'
        )
        return {
          is_success: isSuccess,
        }
      }

      console.log(
        '👶 3 of 12: Retrieve PKI openid config for Singpass myinfo from Environment Variable.'
      )
      const {
        scope,
        clientId,
        clientSecret,
        grantType,
        myinfoUrl,
        redirectUri,
        pkiCompanyPrivateKey,
        pkiMyInfoPublicCert,
      } = getSingpassConfig()
      const myinfoTokenUrl = `${myinfoUrl}/token`

      console.log(
        '👶 4 of 12: Prepare headers and params for Singpass Myinfo authorization token request.'
      )
      const tokenParams = {
        client_id: clientId,
        client_secret: clientSecret,
        code: authorizationCode,
        grant_type: grantType,
        redirect_uri: redirectUri,
        state: userState,
      }
      const cacheCtl = 'no-cache'
      const contentType = 'application/x-www-form-urlencoded'
      const tokenMethod = 'POST'
      const tokenHeaders = {
        'Content-Type': contentType,
        'Cache-Control': cacheCtl,
      }
      const axiosTokenParams = new URLSearchParams()
      axiosTokenParams.append('client_id', clientId)
      axiosTokenParams.append('client_secret', clientSecret)
      axiosTokenParams.append('code', authorizationCode)
      axiosTokenParams.append('grant_type', grantType)
      axiosTokenParams.append('redirect_uri', redirectUri)
      axiosTokenParams.append('state', userState)

      const tokenAuthHeaders = utils.generateSHA256withRSAHeader(
        myinfoTokenUrl,
        tokenParams,
        tokenMethod,
        contentType,
        clientId,
        pkiCompanyPrivateKey,
        clientSecret,
        userNonce
      )

      if (!_.isEmpty(tokenAuthHeaders)) {
        _.set(tokenHeaders, 'Authorization', tokenAuthHeaders)
      }

      console.log(
        '👶 5 of 12: Call Singpass Myinfo token endpoint to retrieve authorization tokens.'
      )
      let tokenResponse

      console.log('myinfoTokenUrl ==============')
      console.log(myinfoTokenUrl)
      console.log('axiosTokenParams ==============')
      console.log(axiosTokenParams)
      console.log('tokenHeaders ==============')
      console.log(tokenHeaders)

      try {
        tokenResponse = await axios.post(myinfoTokenUrl, axiosTokenParams, {
          headers: tokenHeaders,
        })
      } catch (e) {
        console.log(
          '❌ 5 of 12: Call Singpass Myinfo token endpoint to retrieve authorization tokens.',
          e
        )

        // TODO: THIS IS TEMPORARY FIX FOR EXPO, REMOVE THIS WHEN EXPO IS OVER --START.
        const verificationRequest = await UserVerificationRequest.query()
          .where('user_id', user.id)
          .orderBy('created_at', 'desc')
          .firstOrFail()

        const returnData = {
          nric: verificationRequest.singpassNric,
          name: verificationRequest.singpassFirstName + ' ' + verificationRequest.singpassLastName,
          gender: user.gender,
          birthday: verificationRequest.singpassBirthday.toString(),
          mobile_number: verificationRequest.singpassMobileNumber,
          children_birth_records: verificationRequest.singpassChildrenBirthday.map((birthday) =>
            birthday.toString()
          ),
        }

        return response.ok({
          success: isSuccess,
          data: returnData,
        })
        // TODO: THIS IS TEMPORARY FIX FOR EXPO, REMOVE THIS WHEN EXPO IS OVER --END.
      }

      console.log('👶 6 of 12: Retrieve PKI access token variable for PKI decryption.')
      const accessToken = tokenResponse?.data['access_token'] ?? ''

      console.log('👶 7 of 12: Verify JWS access token with Singpass Myinfo public certificate.')
      const decoded = utils.verifyJWS(accessToken, pkiMyInfoPublicCert)

      console.log('👶 8 of 12: Prepare headers and params for Singpass myinfo person request.')

      let sub = ''
      if (typeof decoded.sub === 'function') {
        console.log('👶 8.1 of 12: decoded.sub is a function')
        sub = decoded.sub()
      } else {
        console.log('👶 8.1 of 12: decoded.sub is a string')
        sub = decoded.sub ?? ''
      }

      console.log('👶 8.2 of 12: Sub is', sub)

      // const sub = decoded.sub
      const myinfoPersonUrl = `${myinfoUrl}/person/${sub}`
      const personMethod = 'GET'
      const personParams = {
        attributes: scope,
        client_id: clientId,
      }
      const personHeaders = {
        'Cache-Control': cacheCtl,
      }
      const nonce = utils.generateRandomNumber(15)

      const personAuthHeaders = utils.generateSHA256withRSAHeader(
        myinfoPersonUrl,
        personParams,
        personMethod,
        '',
        clientId,
        pkiCompanyPrivateKey,
        clientSecret,
        nonce.toString()
      )

      if (!_.isEmpty(personAuthHeaders)) {
        _.set(personHeaders, 'Authorization', personAuthHeaders + ',Bearer ' + accessToken)
      } else {
        _.set(personHeaders, 'Authorization', 'Bearer ' + accessToken)
      }

      console.log('👶 9 of 12: Call Singpass Myinfo person endpoint to retrieve person data.')
      let personResponse
      try {
        personResponse = await axios.get(myinfoPersonUrl, {
          headers: personHeaders,
          params: personParams,
        })
      } catch (e) {
        console.log('❌ 9 of 12: Call Singpass Myinfo person endpoint to retrieve person data.', e)
      }

      console.log('👶 10 of 12: Process and decrypt person encrypted payload.')
      const personData = `${personResponse?.data}`
      const jweParts = personData.split('.')
      const personDataJWS = await utils.decryptJWE(
        jweParts[0],
        jweParts[1],
        jweParts[2],
        jweParts[3],
        jweParts[4],
        pkiCompanyPrivateKey
      )

      console.log(
        '👶 11 of 12: Verify and decode person data payload with Singpass Myinfo public certificate.'
      )
      const decodedPersonData: any = utils.verifyJWS(`${personDataJWS}`, pkiMyInfoPublicCert)
      isSuccess = decodedPersonData !== null

      console.log(`👶 12 of 12: Return the json response with person data:`)
      console.log(decodedPersonData)

      const person = {
        nric: decodedPersonData.partialuinfin.value,
        name: decodedPersonData.name.value,
        gender: decodedPersonData.sex.desc,
        birthday: decodedPersonData.dob.value,
        mobile_number: `${decodedPersonData.mobileno.prefix.value}${decodedPersonData.mobileno.areacode.value}${decodedPersonData.mobileno.nbr.value}`,
        children_birth_records: decodedPersonData.childrenbirthrecords.map(
          (child) => child.dob.value
        ),
      }

      return response.ok({
        success: isSuccess,
        data: person,
      })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }
}
