import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
// import Position from 'App/Models/Position'
import User, { userFindFields } from 'App/Models/User'
import _ from 'lodash'
import Drive from '@ioc:Adonis/Core/Drive'
import Logger from '@ioc:Adonis/Core/Logger'
import {
  dialCodeToCountryCodeAndName,
  disk_name,
  generateRandomCode,
  imageFileSpec,
  remotePathUserProfile,
  remotePathUserVerify,
  validatePhoneNumber,
  varcharRule,
} from 'App/utils'
import { v4 as uuidv4 } from 'uuid'
import { UserGender } from 'Contracts/users'
import UserVerificationRequest from 'App/Models/UserVerificationRequest'
import { PUBLIC_DISKNAME, PRIVATE_DISKNAME } from './FilesController'
import {
  UserVerificationStatus,
  UserVerificationType,
} from 'Contracts/user_verification_request_type'
import Position from 'App/Models/Position'
import ngeohash from 'ngeohash'
import { UserType } from 'Contracts/user_type'
import { DateTime } from 'luxon'
import UserFavoriteListing from 'App/Models/UserFavoriteListing'
import { ListingType } from 'Contracts/listing_type'
import BigNumber from 'bignumber.js'
import Listing from 'App/Models/Listing'
import Profile, { SocialType } from 'App/Models/Profile'
import Env from '@ioc:Adonis/Core/Env'
import { createShopifyCustomerIfNotExists, loginProcess } from './AuthController'

const fetchFields = {
  fields: {
    pick: [
      'id',
      'name',
      'company_name',
      'contact_number',
      'address_name',
      'full_address',
      'description',
      'number_of_private_feeding_rooms',
      'opening_hours',
      'listing_type',
      'status',
      'created_at',
      'updated_at',
      'position',
      'firestore_id',
    ],
  },
  relations: {
    position: {
      fields: {
        pick: ['coordinate'],
      },
    },
    listing_files: {
      fields: {
        pick: ['id', 'image_url'],
      },
    },
  },
}

export default class UsersController {
  // C
  public async createUser({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'createUser')

    try {
      const validationSchema = schema.create({
        country_dial_code: schema.string.optional([
          rules.requiredIfExists('new_mobile_number'),
          rules.maxLength(7),
          rules.minLength(1),
        ]), // max length 4 because + sign and longest dial code is 3 digit long
        new_mobile_number: schema.string.optional([
          rules.requiredIfExists('country_code'),
          rules.maxLength(20),
        ]),
        new_email: schema.string.optional([rules.maxLength(255), rules.minLength(1)]),
        username: schema.string.optional([
          rules.minLength(1),
          rules.maxLength(12),
          rules.unique({ table: 'users', column: 'username' }),
        ]),
        longitude: schema.number.optional([
          rules.requiredIfExists('latitude'),
          rules.range(-180, 180),
        ]),
        latitude: schema.number.optional([
          rules.requiredIfExists('longitude'),
          rules.range(-90, 90),
        ]),
      })

      const { new_email, new_mobile_number, country_dial_code, longitude, latitude, username } =
        await request.validate({
          schema: validationSchema,
        })

      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      const fullMobileNumber =
        country_dial_code != null && new_mobile_number != null
          ? country_dial_code?.trim() + new_mobile_number?.trim()
          : null
      const isEmail = new_email != null && emailPattern.test(new_email)
      const isPhone = fullMobileNumber != null && validatePhoneNumber(fullMobileNumber)

      if (!isEmail && !isPhone) {
        return response.unprocessableEntity({
          success: false,
          message: 'Please provide either phone or email',
        })
      }

      // if (payload.password != payload.passwordConfirm) {
      //   return response.badRequest({
      //     success: false,
      //     message: 'Password and confirm password does not match',
      //   })
      // }

      const isExist: User | string | null = isEmail
        ? await User.findBy('email_address', new_email)
        : isPhone
        ? await User.query()
            .where('mobile_number', new_mobile_number!)
            .andWhere('country_dial_code', country_dial_code!)
            .first()
        : null

      if (isExist) {
        return response.conflict({
          success: false,
          message: `${isEmail ? 'Email address' : 'Phone number'} already exist`,
        })
      }

      let newUserPositionId: string | undefined
      if (longitude != null && latitude != null) {
        // Proceed with create new user after validation.
        // Create new position if not exist
        const existingPosition = await Position.query()
          .whereRaw(`ST_Equals(coordinate, POINT(?, ?))`, [longitude as number, latitude as number])
          .first()

        const geohash = ngeohash.encode(longitude, latitude, 20)

        if (existingPosition) {
          newUserPositionId = existingPosition.id
        } else {
          // Position not exist create new position
          const newPositionId = uuidv4()
          const result = await Database.rawQuery(
            'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
            [newPositionId, geohash, longitude, latitude]
          )

          if (result[0].affectedRows > 0) {
            //const newPosition = await Position.findOrFail(newPositionId) // For some reason, ORM cannot immediately reflect the real time rows inserted, hence commented, since row affected already there.
            newUserPositionId = newPositionId
          } else {
            newUserPositionId = undefined
          }
        }
      }

      const input: Partial<User> = {
        password: generateRandomCode(8) + '',
        username: username ? username : generateRandomCode(12) + '', // When register via gomama shopify, no username provided, hence random generate
        userType: UserType.user,
      }

      if (newUserPositionId) {
        input['positionId'] = newUserPositionId
      }

      // safe to assume if we get to this step, OTP is verified
      if (isEmail) {
        input['emailAddress'] = new_email
        input['isEmailAddressVerified'] = true
        input['authProvider'] = 'email'
      }

      if (isPhone) {
        const countryCodeAndName = dialCodeToCountryCodeAndName(fullMobileNumber?.trim()!)
        if (countryCodeAndName) {
          input['countryCode'] = countryCodeAndName.countryCode
          input['countryName'] = countryCodeAndName.countryName
        }
        input['mobileNumber'] = new_mobile_number?.trim()
        input['countryDialCode'] = country_dial_code?.trim().startsWith('+')
          ? country_dial_code?.trim().slice(1)
          : country_dial_code?.trim()
        input['isMobileNumberVerified'] = true
        input['authProvider'] = 'phone'
      }

      const newUser = await User.create(input)

      const token = await loginProcess(auth, newUser)

      return response.ok({
        success: true,
        data: { user: token.user.serialize(userFindFields), token: token },
        message: 'User created successfully',
      })
    } catch (error) {
      Logger.error(error, 'createUser')
      return response.badRequest(error)
    }
  }

  // R
  public async findMyself({ auth, response }: HttpContextContract) {
    Logger.info({ user: auth.user?.emailAddress ?? auth.user?.mobileNumber }, 'findMyself')

    try {
      const user = await auth.authenticate()

      if (!user) {
        return response.unauthorized({ success: false, message: 'User not found' })
      }

      await user.load('favoriteListings')
      await user.load('profiles')

      // return full load for admin
      if (user.userType == 'admin') {
        return response.ok({ data: user })
      }
      return response.ok({ data: user.serialize(userFindFields) })
    } catch (error) {
      Logger.error(error, 'findMyself')
      return response.badRequest(error)
    }
  }

  public async findUsers({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)
      const type = request.input('type')

      if (type && !Object.values(UserType).includes(type)) {
        return response.unprocessableEntity({
          success: false,
          message: `Invalid user type: ${type}, valid user type: ${Object.values(UserType)}`,
        })
      }

      const users = type
        ? await User.query()
            .where('is_hidden', false)
            .andWhere('user_type', type)
            .orderBy(sort[0], sort[1])
            .paginate(page, limit)
        : await User.query()
            .where('is_hidden', false)
            .orderBy(sort[0], sort[1])
            .paginate(page, limit)

      return response.ok(users)
    } catch (error) {
      Logger.error(error, 'findUsers')
      return response.badRequest(error)
    }
  }

  public async findUser({ params: { id }, response }: HttpContextContract) {
    try {
      const user = await User.query().where('is_hidden', false).andWhere('id', id).first()

      if (!user) {
        return response.notFound({ success: false, message: 'User not found' })
      }

      return response.ok({ data: user })
    } catch (error) {
      Logger.error(error, 'findUser')
      return response.badRequest(error)
    }
  }

  public async findUserByUsername({ params: { username }, response }: HttpContextContract) {
    try {
      const user = await User.query()
        .select('username', 'id')
        .where('is_hidden', false)
        .andWhere('username', username)
        .first()

      if (!user) {
        return response.notFound({ success: false })
      }

      return response.ok({ success: true })
    } catch (error) {
      Logger.error(error, 'findUserByUsername')
      return response.badRequest(error)
    }
  }

  public async findMyFavoriteListings({ auth, request, response }: HttpContextContract) {
    Logger.info(request.all(), 'findMyFavoriteListings')

    try {
      const user = await auth.authenticate()
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 10)
      const filters = _.omit(request.all(), ['page', 'sort', 'limit'])
      const type = request.input('type', undefined)

      if (type && !Object.values(ListingType).includes(type)) {
        return response.unprocessableEntity({
          success: false,
          message: `Invalid listing type: ${type}, valid listing type: ${Object.values(
            ListingType
          )}`,
        })
      }

      /// Note: optional
      /// use for ordering
      const lat = new BigNumber(request.input('lat', 999)).toNumber()
      const lon = new BigNumber(request.input('lon', 999)).toNumber()

      const listings = await Listing.filter(filters)
        .whereHas('favoriteByUsers', (query) => query.where('user_id', user.id))
        .select('listings.*') // need this since we inner join positions table
        .where('listings.is_hidden', false)
        .andWhere('is_verified', true)
        .preload('listingFiles', (query) => {
          query.where('listing_files.is_hidden', false)
        })
        .withAggregate('listingRatings', (query) => {
          query.avg('experience_rating').as('average_experience_ratings')
        })
        .withAggregate('listingRatings', (query) => {
          query.count('*').as('total_experience_ratings')
        })
        .withAggregate('sessions', (query) => {
          query.count('*').as('total_sessions')
        })
        .if(lat != 999 && lon != 999, (query) =>
          query
            .select(
              Database.raw(`ST_Distance_Sphere(positions.coordinate, Point(?,?)) as distance`, [
                lon,
                lat,
              ])
            )
            .innerJoin('positions', 'listings.position_id', 'positions.id')
            .orderBy('distance', 'asc')
        )
        .unless(lat != 999 && lon != 999, (query) => (query = query.orderBy(sort[0], sort[1])))
        .paginate(page, limit)

      return response.status(200).send(listings.serialize(fetchFields))
    } catch (error) {
      Logger.error(error, 'findMyFavoriteListings')
      return response.badRequest(error)
    }
  }

  // U
  public async updateUser({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'updateUser')
    const user = await auth.authenticate()

    try {
      const validationSchema = schema.create({
        username: schema.string.optional([
          rules.minLength(1),
          rules.maxLength(12),
          rules.unique({ table: 'users', column: 'username' }),
        ]),
        longitude: schema.number.optional([
          rules.requiredIfExists('latitude'),
          rules.range(-180, 180),
        ]),
        latitude: schema.number.optional([
          rules.requiredIfExists('longitude'),
          rules.range(-90, 90),
        ]),
        first_name: schema.string.optional(varcharRule),
        last_name: schema.string.optional(varcharRule),
        profile_image: schema.file.optional({
          size: '20mb',
          extnames: ['png', 'jpg', 'jpeg'],
        }),
        gender: schema.enum.optional(Object.values(UserGender)),
        birthday: schema.date.optional(),
        children_birthdays: schema.array.optional().members(schema.date()),
        mobile_number: schema.string.optional(varcharRule),
        company_name: schema.string.optional(varcharRule),
        nationality: schema.string.optional(varcharRule),
        password: schema.string.optional(varcharRule),
        reset_password_otp: schema.string.optional([
          rules.minLength(6),
          rules.maxLength(6),
          rules.requiredIfExists('password'),
        ]),
      })

      const { longitude, latitude, profile_image, reset_password_otp, ...payload } =
        await request.validate({
          schema: validationSchema,
        })

      // Match Reset Password OTP
      const reset_password_otp_not_match = false
      if (reset_password_otp_not_match) {
        // TODO: Validate RESET PASSWORD OTP, if  password needs to be updated, for now assume OTP is validated
        console.log(reset_password_otp)
        return response.badRequest({
          success: false,
          message: 'Reset password OTP matching is incorrect',
        })
      }

      if (
        payload.mobile_number &&
        (await User.query()
          .where('mobile_number', payload.mobile_number)
          .andWhereNot('id', user.id)
          .first())
      ) {
        return response.conflict({
          success: false,
          message: 'Mobile number already registered under an existing user',
        })
      }

      if (
        payload.username &&
        (await User.query().where('username', payload.username).andWhereNot('id', user.id).first())
      ) {
        return response.conflict({
          success: false,
          message: 'Username already registered under an existing user',
        })
      }

      // Update position; create new position if not exist
      let newUserPositionId: string | undefined
      if (longitude && latitude) {
        const existingPosition = await Position.query()
          .whereRaw(`ST_Equals(coordinate, POINT(?, ?))`, [longitude as number, latitude as number])
          .first()

        const geohash = ngeohash.encode(longitude as number, latitude as number, 20)

        if (existingPosition) {
          newUserPositionId = existingPosition.id
        } else {
          // Position not exist create new position
          const newPositionId = uuidv4()
          const result = await Database.rawQuery(
            'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
            [newPositionId, geohash, longitude, latitude]
          )

          if (result[0].affectedRows > 0) {
            const newPosition = await Position.findOrFail(newPositionId)
            newUserPositionId = newPosition.id
          } else {
            newUserPositionId = undefined
          }
        }
      }

      // Update Image
      let remoteNameNewImage: string | undefined
      if (profile_image) {
        remoteNameNewImage = `${user.id}_${generateRandomCode(10)}.${profile_image.extname}`
        profile_image.moveToDisk(
          remotePathUserProfile,
          { name: remoteNameNewImage },
          PUBLIC_DISKNAME
        )
      }

      newUserPositionId && (user.positionId = newUserPositionId)
      remoteNameNewImage && (user.photoUrl = remoteNameNewImage)
      const updatedUser = await user.merge(payload).save()
      const latestUser = await User.findOrFail(updatedUser.id)

      return response.ok({
        success: true,
        message: 'Successfully updated user info',
        data: latestUser.serialize(userFindFields),
      })
    } catch (error) {
      Logger.error(error, 'updateUser')
      return response.badRequest(error)
    }
  }

  public async gomamaVerify({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'gomamaVerify')

    try {
      const user = await auth.authenticate()

      if (user.latestVerifySelfieFailCount > Env.get('MAX_VERIFY_SELFIE_FAIL_TRIES')) {
        return response.badRequest({
          success: false,
          message: `Exceeded maximum number of verify selfie tries: ${Env.get(
            'MAX_VERIFY_SELFIE_FAIL_TRIES'
          )} times.`,
        })
      }

      const isExist = await UserVerificationRequest.query()
        .select('*')
        .where('user_id', user.id)
        .andWhereNull('reviewed_at')
        .andWhere('type', UserVerificationType.gomama)
        .andWhere('status', UserVerificationStatus.pending)
        .orderBy('created_at', 'desc')
        .first()

      // User has a pending request
      if (isExist) {
        return response.forbidden({
          success: false,
          message:
            'Only one Gomama verification request allowed at a time, please wait admin to review the latest verification request.',
        })
      }

      const validationSchema = schema.create({
        first_name: schema.string(varcharRule),
        last_name: schema.string(varcharRule),
        birthday: schema.date(),
        children_birthdays: schema.array.optional().members(schema.date()),
        mobile_number: schema.string(varcharRule),
        email: schema.string(varcharRule),
        gender: schema.enum(Object.values(UserGender)),
        user_selfie_file: schema.file(imageFileSpec),
        user_selfie_file_copy: schema.file(imageFileSpec),
        fin_or_passport: schema.string([rules.minLength(4), rules.maxLength(4)]),
      })

      const { user_selfie_file, user_selfie_file_copy, ...validatedData } = await request.validate({
        schema: validationSchema,
      })

      const remoteName = `${user.id}_${generateRandomCode(10)}.${user_selfie_file.extname}`

      // Upload to /user_profile dir
      await user_selfie_file.moveToDisk(
        remotePathUserProfile,
        { name: remoteName },
        PUBLIC_DISKNAME // user profile is public
      )

      // Upload to /user_verify dir
      await user_selfie_file_copy.moveToDisk(
        remotePathUserVerify,
        { name: remoteName },
        PRIVATE_DISKNAME // user verification is private
      )

      await Database.transaction(async (trx) => {
        // Create Request Verify Row
        // Update User isGomamaVerified to true (Assume all info are correct)
        user.isGomamaVerified = true
        user.photoUrl == null && (user.photoUrl = remoteName)
        user.firstName = validatedData.first_name
        user.lastName = validatedData.last_name
        user.birthday = validatedData.birthday
        user.gender = validatedData.gender
        user.childrenBirthdays = validatedData.children_birthdays ?? []
        user.latestVerifySelfieFailCount = 0 // reset selfie fail count to zero when success
        await user.useTransaction(trx).save()

        await UserVerificationRequest.create(
          {
            userId: user.id,
            gomamaUserVerifyPhotoUrl: remoteName,
            type: UserVerificationType.gomama,
            gomamaFirstName: validatedData.first_name,
            gomamaLastName: validatedData.last_name,
            gomamaBirthday: validatedData.birthday,
            gomamaChildrenBirthday: validatedData.children_birthdays,
            gomamaMobileNumber: validatedData.mobile_number,
            gomamaEmail: validatedData.email,
            gomamaGender: validatedData.gender,
            gomamaFinOrPassport: validatedData.fin_or_passport,
            status: UserVerificationStatus.approve, // auto approved as requested by product owner
          },
          { client: trx }
        )
      })

      const updatedUser = await User.findOrFail(user.id)

      return response.ok({
        success: true,
        message: 'Successfully submitted Gomama verification request.',
        data: updatedUser,
      })
    } catch (error) {
      Logger.error(error, 'gomamaVerify')
      return response.badRequest(error)
    }
  }

  public async gomamaVerifySelfieCountAdd({ response, auth }: HttpContextContract) {
    try {
      const user = await auth.authenticate()

      user.latestVerifySelfieFailCount += 1

      await user.save()

      const latestUser = await User.findOrFail(user.id)

      return response.ok({
        success: true,
        message: 'Added +1 for selfie fail count.',
        data: latestUser.serialize(userFindFields),
      })
    } catch (error) {
      Logger.error(error, 'gomamaVerifySelfieCountAdd')
      return response.badRequest(error)
    }
  }

  public async singpassVerify({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'singpassVerify')

    const user = await auth.authenticate()
    const validationSchema = schema.create({
      first_name: schema.string([rules.minLength(1), rules.maxLength(255)]),
      last_name: schema.string([rules.minLength(1), rules.maxLength(255)]),
      birthday: schema.date(),
      child_birthday: schema.date.optional(),
      children_birthdays: schema.array.optional().members(schema.date()),
      mobile_number: schema.string.optional(varcharRule),
      gender: schema.enum(Object.values(UserGender)),
      nric: schema.string(),
      email: schema.string.optional(),
    })

    const { ...validatedData } = await request.validate({
      schema: validationSchema,
    })
    try {
      await Database.transaction(async (trx) => {
        // Create Request Verify Row
        // Change User isSingpassVerified to true (Assume Info are all correct)
        user.isSingpassVerified = true
        user.firstName = validatedData.first_name
        user.lastName = validatedData.last_name
        user.birthday = validatedData.birthday
        user.gender = validatedData.gender
        user.childrenBirthdays = validatedData.child_birthday
          ? [validatedData.child_birthday]
          : validatedData.children_birthdays ?? []
        await user.useTransaction(trx).save()

        await UserVerificationRequest.create(
          {
            userId: user.id,
            type: UserVerificationType.singpass,
            singpassFirstName: validatedData.first_name,
            singpassLastName: validatedData.last_name,
            singpassBirthday: validatedData.birthday,
            singpassChildrenBirthday: validatedData.child_birthday
              ? [validatedData.child_birthday]
              : validatedData.children_birthdays,
            singpassMobileNumber: validatedData.mobile_number,
            singpassEmail: validatedData.email,
            singpassGender: validatedData.gender,
            singpassNric: validatedData.nric,
            status: UserVerificationStatus.approve, // auto approved as requested by product owner
          },
          { client: trx }
        )
      })

      const updatedUser = await User.findOrFail(user.id)

      return response.ok({
        success: true,
        message: 'Successfully submitted Singpass verification request.',
        data: updatedUser,
      })
    } catch (error) {
      Logger.error(error, 'singpassVerify')
      return response.badRequest(error)
    }
  }

  public async updateFavoriteListing({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'updateFavoriteListing')
    const user = await auth.authenticate()

    try {
      const validationSchema = schema.create({
        listing_id: schema.string([rules.exists({ column: 'id', table: 'listings' })]),
      })

      const { listing_id } = await request.validate({ schema: validationSchema })

      const isExist = await UserFavoriteListing.query()
        .where('listing_id', listing_id)
        .andWhere('user_id', user.id)
        .first()

      const listingTargeted = await Listing.findOrFail(listing_id)

      if (isExist) {
        await user.related('favoriteListings').detach([isExist.listingId])
      } else {
        await user.related('favoriteListings').attach({
          [listingTargeted.id as string]: {
            id: uuidv4(),
            is_hidden: false,
          },
        })
      }

      return response.ok({
        success: true,
        message: 'Updated user favorite listings successfully',
      })
    } catch (error) {
      Logger.error(error, 'updateFavoriteListing')
      return response.badRequest(error)
    }
  }

  // D
  public async deleteUser({ request, response, auth }: HttpContextContract) {
    const validationSchema = schema.create({
      reason: schema.string(),
    })

    try {
      const user = await auth.authenticate()
      const { reason } = await request.validate({ schema: validationSchema })
      const userProfiles = await Profile.query().where('email', user.emailAddress)

      if (user.deletedAt != null) {
        return response.forbidden({
          success: false,
          message: 'User has already been soft deleted.',
        })
      }

      // const toBeDeletedUrl = remotePathUserProfile + user.photoUrl.split(remotePathUserProfile)
      const isSuccess = await Database.transaction(async (trx) => {
        let success = false
        const appendName = `-deleted-${new Date().getTime()}`
        user.deletedAt = DateTime.now()
        if (user.emailAddress != null) {
          user.emailAddress = user.emailAddress + appendName
        }
        if (user.mobileNumber != null) {
          user.mobileNumber = user.mobileNumber + appendName
        }

        user.deleteReason = reason

        const userProfilePromise: Promise<void>[] = []
        if (userProfiles.length > 0) {
          userProfiles.forEach(async (userProfile) => {
            if (userProfile.socialType == SocialType.SHOPIFY) {
              // We do not want to delete shopify to keep the shopify generated password (shopify API have order linked user info, hence must not delete)
              // Simply set the associated user id to null, in future login again, just link with this profile again if match same email.
              userProfile.userId = null
              await userProfile.useTransaction(trx).save()
            } else {
              userProfilePromise.push(userProfile.useTransaction(trx).delete()) // Delete User profile so there is no duplicate social profile.
            }
          })
        }

        await Promise.all(userProfilePromise)
        await user.useTransaction(trx).save()
        success = true
        return success
      })

      // Delete User Photo Soft delete no need delete image
      // if (isSuccess) {
      //   await Drive.use(disk_name).delete(toBeDeletedUrl)
      // }

      return response.ok({
        success: isSuccess,
        message: 'Successfully soft deleted an user.',
      })
    } catch (error) {
      Logger.error(error, 'deleteUser')
      return response.badRequest(error)
    }
  }

  // Admin Routes

  // C
  public async createUserAdmin({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        new_account: schema.string({ trim: true }, [rules.minLength(1), rules.maxLength(255)]),
        password: schema.string({ trim: true }, [rules.minLength(8), rules.maxLength(255)]),
        passwordConfirm: schema.string({ trim: true }, [rules.minLength(8), rules.maxLength(255)]),
        first_name: schema.string([rules.minLength(1), rules.maxLength(255)]),
        last_name: schema.string([rules.minLength(1), rules.maxLength(255)]),
        longitude: schema.number([rules.requiredIfExists('latitude'), rules.range(-180, 180)]),
        latitude: schema.number([rules.requiredIfExists('longitude'), rules.range(-90, 90)]),
        user_type: schema.enum(Object.values(UserType)),
      })

      const { longitude, latitude, ...payload } = await request.validate({
        schema: validationSchema,
      })

      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      const isEmail = emailPattern.test(payload.new_account)
      const isPhone = validatePhoneNumber(payload.new_account)

      if (!isEmail && !isPhone) {
        return response.unprocessableEntity({
          success: false,
          message: 'Please provide either phone or email',
        })
      }

      if (payload.user_type == UserType.admin && !isEmail) {
        return response.forbidden({
          success: false,
          message: 'Admin user must be registered using an email',
        })
      }

      if (payload.password != payload.passwordConfirm) {
        return response.badRequest({
          success: false,
          message: 'Password and confirm password is not matched',
        })
      }

      const isExist: User | null = isEmail
        ? await User.findBy('email_address', payload.new_account)
        : isPhone
        ? await User.findBy('mobile_number', payload.new_account)
        : null

      if (isExist) {
        return response.conflict({
          success: false,
          message: `${isEmail ? 'Email address' : 'Phone number'} already exist`,
        })
      }

      const otpVerified = true // Temporary hard code to otp verify pass, OTP verification will be implemented later
      if (!otpVerified) {
        return response.unauthorized({ success: false, message: 'OTP does not match' })
      }

      // Proceed with create new user after validation.
      // Create new position if not exist
      const existingPosition = await Position.query()
        .whereRaw(`ST_Equals(coordinate, POINT(?, ?))`, [longitude as number, latitude as number])
        .first()

      const geohash = ngeohash.encode(longitude, latitude, 20)
      let newUserPositionId: string | undefined

      if (existingPosition) {
        newUserPositionId = existingPosition.id
      } else {
        // Position not exist create new position
        const newPositionId = uuidv4()
        const result = await Database.rawQuery(
          'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
          [newPositionId, geohash, longitude, latitude]
        )

        if (result[0].affectedRows > 0) {
          const newPosition = await Position.findOrFail(newPositionId)
          newUserPositionId = newPosition.id
        } else {
          newUserPositionId = undefined
        }
      }

      const newUser = await User.create({
        emailAddress: isEmail ? payload.new_account : undefined,
        mobileNumber: isPhone ? payload.new_account : undefined,
        password: payload.password,
        positionId: newUserPositionId,
        firstName: payload.first_name,
        lastName: payload.last_name,
        userType: payload.user_type,
      })

      await createShopifyCustomerIfNotExists(newUser)

      return response.ok({ success: true, message: 'User created successfully', data: newUser })
    } catch (error) {
      Logger.error(error, 'createUserAdmin')
      return response.badRequest(error)
    }
  }

  // R
  public async findUsersAdmin({ request, response }: HttpContextContract) {
    const sortCustomColumn = (column: string) => {
      if (column == 'full_name') {
        return 'first_name' // sort by first name since first_name will always be infront when computed.
      }

      if (column == 'full_mobile_number') {
        return 'country_dial_code' // sort by country_dial_code since country_dial_code will always be infront when computed.
      }
      return column
    }

    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)
      const type = request.input('type')
      const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      if (type && !Object.values(UserType).includes(type)) {
        return response.unprocessableEntity({
          success: false,
          message: `Invalid user type: ${type}, valid user type: ${Object.values(UserType)}`,
        })
      }

      const users = type
        ? await User.filter(filters)
            .where('user_type', type)
            .orderBy(sortCustomColumn(sort[0]), sort[1])
            .paginate(page, limit)
        : await User.filter(filters)
            .orderBy(sortCustomColumn(sort[0]), sort[1])
            .paginate(page, limit)

      return response.ok(users)
    } catch (error) {
      Logger.error(error, 'findUsersAdmin')
      return response.badRequest(error)
    }
  }

  public async findUserAdmin({ params: { id }, response }: HttpContextContract) {
    try {
      const user = await User.query().preload('devices').where('id', id).first()

      if (!user) {
        return response.notFound({ success: false, message: 'User not found' })
      }

      return response.ok({ data: user })
    } catch (error) {
      Logger.error(error, 'findUserAdmin')
      return response.badRequest(error)
    }
  }

  public async findUserByUsernameAdmin({ params: { username }, response }: HttpContextContract) {
    try {
      const user = await User.query().where('username', username).first()

      if (!user) {
        return response.notFound({ success: false, message: 'User not found' })
      }

      return response.ok({ data: user })
    } catch (error) {
      Logger.error(error, 'findUserByUsernameAdmin')
      return response.badRequest(error)
    }
  }

  // U
  public async updateUserAdmin({ params: { id }, request, response }: HttpContextContract) {
    try {
      const userToUpdate = await User.findOrFail(id)
      const validationSchema = schema.create({
        is_hidden: schema.boolean.optional(),
        is_admin_verified: schema.boolean.optional(),
      })

      const { ...payload } = await request.validate({
        schema: validationSchema,
      })

      payload.is_admin_verified && (userToUpdate.isAdminVerified = payload.is_admin_verified)
      payload.is_hidden && (userToUpdate.isHidden = payload.is_hidden)
      const updatedUser = await userToUpdate.save()

      return response.ok({
        success: true,
        message: 'Successfully updated user info.',
        data: updatedUser,
      })
    } catch (error) {
      Logger.error(error, 'updateUserAdmin')
      return response.badRequest(error)
    }
  }

  public async restoreSoftDeleteAccount({ params: { id }, response }: HttpContextContract) {
    try {
      const userToUpdate = await User.findOrFail(id)

      if (userToUpdate.deletedAt == null) {
        return response.forbidden({
          success: false,
          message: 'User is not soft deleted, hence cannot be restored.',
        })
      }

      userToUpdate.deletedAt = null
      userToUpdate.deleteReason = null
      await userToUpdate.save()

      return response.ok({
        success: true,
        message: 'Successfully restored the user account.',
      })
    } catch (error) {
      Logger.error(error, 'restoreSoftDeleteAccount')
      return response.badRequest(error)
    }
  }

  public async reviewVerification({
    params: { id },
    request,
    response,
    auth,
  }: HttpContextContract) {
    const user = await auth.authenticate()

    const verificationRequest = await UserVerificationRequest.find(id)

    if (!verificationRequest)
      return response.notFound({ success: false, message: 'Verification request not found' })

    const validationSchema = schema.create({
      status: schema.enum(Object.keys(UserVerificationStatus)),
      reason: schema.string([rules.maxLength(255)]),
    })

    const validatedData = await request.validate({ schema: validationSchema })
    try {
      await Database.transaction(async (trx) => {
        await verificationRequest
          .merge({
            reviewBy: user.id,
            reviewedAt: DateTime.now(),
            reason: validatedData.reason,
            status: validatedData.status as UserVerificationStatus,
          })
          .useTransaction(trx)
          .save()

        if (validatedData.status == UserVerificationStatus.approve) {
          const userToUpdate = await User.findOrFail(verificationRequest.userId)

          if (verificationRequest.type == UserVerificationType.gomama) {
            userToUpdate.firstName = verificationRequest.gomamaFirstName
            userToUpdate.lastName = verificationRequest.gomamaLastName
            userToUpdate.birthday = verificationRequest.gomamaBirthday
            userToUpdate.gender = verificationRequest.gomamaGender as UserGender
            userToUpdate.childrenBirthdays = verificationRequest.gomamaChildrenBirthday
          } else if (verificationRequest.type == UserVerificationType.singpass) {
            userToUpdate.firstName = verificationRequest.singpassFirstName
            userToUpdate.lastName = verificationRequest.singpassLastName
            userToUpdate.birthday = verificationRequest.singpassBirthday
            userToUpdate.gender = verificationRequest.singpassGender as UserGender
            userToUpdate.childrenBirthdays = verificationRequest.singpassChildrenBirthday
          }
          await userToUpdate.useTransaction(trx).save()
        }
      })

      return response.ok({ success: true, message: 'Review verification request success' })
    } catch (error) {
      Logger.error(error, 'reviewVerification')
      return response.badRequest(error)
    }
  }

  public async resetUserSelfieVerifyFailCount({ response, params: { id } }: HttpContextContract) {
    try {
      const user = await User.find(id)

      if (!user) {
        return response.notFound({ success: false, message: 'User not found' })
      }

      user.latestVerifySelfieFailCount = 0 // reset user selfie fail count

      await user.save()

      return response.ok({
        success: true,
        message: 'Reset user selfie fail count successfully',
      })
    } catch (error) {
      Logger.error(error, 'resetUserSelfieVerifyFailCount')
      return response.badRequest(error)
    }
  }

  // D
  public async deleteUserAdmin({ params: { id }, response }: HttpContextContract) {
    try {
      const findUser = await User.find(id)

      if (!findUser) {
        return response.notFound({ success: false, message: 'User not found.' })
      }

      const toBeDeletedUrl = remotePathUserProfile + findUser.photoUrl.split(remotePathUserProfile)
      const result = await Database.transaction(async (trx) => {
        findUser.useTransaction(trx)
        await findUser.delete()

        return {
          success: true,
        }
      })

      // Delete User Photo
      if (result.success) {
        await Drive.use(disk_name).delete(toBeDeletedUrl)
      }

      return response.ok({
        success: result.success,
        message: 'Successfully deleted a user.',
      })
    } catch (error) {
      Logger.error(error, 'deleteUserAdmin')
      return response.badRequest(error)
    }
  }
}
