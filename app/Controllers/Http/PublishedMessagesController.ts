import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Logger from '@ioc:Adonis/Core/Logger'
import PublishedMessage, { SendGroup } from 'App/Models/PublishedMessage'
import Database from '@ioc:Adonis/Lucid/Database'
import { sendNotification } from 'App/Services/FirebaseMessageService'
import NotificationMessage from 'App/Models/NotificationMessage'
import Device from 'App/Models/Device'
import { BatchResponse } from 'firebase-admin/messaging'
import OneTimePassword from 'App/Models/OneTimePassword'
import { OtpStatus, OtpType } from 'Contracts/otp_type'
import User from 'App/Models/User'

export default class PublishedMessagesController {
  public async create({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'create')
    const user = await auth.authenticate()
    const group = request.input('group')
    const send_group: string[] = group.split(',')

    const validationSchema = schema.create({
      users_selected: send_group.includes(SendGroup.ALL_USERS)
        ? schema.array
            .optional([rules.distinct('*')])
            .members(schema.string([rules.exists({ column: 'id', table: 'users' })]))
        : schema
            .array([rules.distinct('*')])
            .members(schema.string([rules.exists({ column: 'id', table: 'users' })])),
      message_chosen: schema.number([
        rules.exists({ column: 'id', table: 'notification_messages' }),
      ]),
    })
    const payload = await request.validate({ schema: validationSchema })

    // NOTE: Since there can be multiple user using 1 device, hence same device might receive multiple same notification message.
    // we don't have implementation to avoid sending multiple times since we expect this to be a corner case and wanted to handle it.

    // Note: Future might have more group added, to work on query refactor, to avoid split into multiple retrieves.
    // Filter Active 30 days or New Register Or Both
    let selectedGroupUsers: User[] = []
    if (send_group.length > 0 && !send_group.includes(SendGroup.ALL_USERS)) {
      let loginOrRegisterRowsQuery = OneTimePassword.query().distinct('email', 'phone')

      let conditions: string[] = []
      if (send_group.includes(SendGroup.ACTIVE_30)) {
        conditions.push(
          `(created_at >= NOW() - INTERVAL 30 DAY AND status = '${OtpStatus.verified}')`
        )
      }

      if (send_group.includes(SendGroup.NEW_REGISTERED)) {
        conditions.push(
          `(created_at >= NOW() - INTERVAL 30 DAY AND type = '${OtpType.registration}' AND one_time_passwords.status = '${OtpStatus.verified}')`
        )
      }

      loginOrRegisterRowsQuery.whereRaw(conditions.join(' OR '))

      const loginOrRegisterRows = await loginOrRegisterRowsQuery

      const userEmailOrPhoneFromGroup = loginOrRegisterRows.map((row) => row.phone ?? row.email)
      selectedGroupUsers = await User.query()
        .whereIn('email_address', userEmailOrPhoneFromGroup)
        .orWhereRaw(
          `CONCAT('+', country_dial_code, ' ', mobile_number) IN (${userEmailOrPhoneFromGroup
            .map((emailOrPhone) => "'" + emailOrPhone + "'")
            .join(',')})`
        )
    }

    const pivot_user_devices = await Database.query()
      .from('user_devices')
      .if(!send_group.includes(SendGroup.ALL_USERS), (query) =>
        query.whereIn('user_id', [
          ...selectedGroupUsers.map((user) => user.id),
          ...payload.users_selected!,
        ])
      )

    try {
      const publishMessagePromises: Promise<PublishedMessage>[] = []

      const notificationMessageChosen = await NotificationMessage.findOrFail(payload.message_chosen)

      const devices = await Promise.all(
        pivot_user_devices.map(async (pivot_user_device) => {
          return await Device.findOrFail(pivot_user_device.device_id)
        })
      )

      const batchMessageResponse: BatchResponse = await sendNotification(
        notificationMessageChosen.title,
        notificationMessageChosen.message,
        devices.map((device) => device.deviceToken),
        notificationMessageChosen.imageUrl
      )

      batchMessageResponse.responses.forEach((response, index) => {
        if (response.success) {
          publishMessagePromises.push(
            PublishedMessage.create({
              deviceId: devices[index].id,
              messageId: payload.message_chosen,
              publishedBy: user.id, // expect admin user to publish the message
            })
          )
        }
      })

      // after success (no error caught), proceed execute publish message create promises
      await Promise.all(publishMessagePromises)

      return response.ok({
        success: true,
        message: 'Published messages successfully',
        fail_count: batchMessageResponse.failureCount,
        success_count: batchMessageResponse.successCount,
      })
    } catch (error) {
      Logger.error(error, 'create')
      return response.badRequest(error)
    }
  }

  public async index({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)

      const messages = await PublishedMessage.query()
        .preload('publishUser')
        .preload('receiveDevice')
        .preload('messageUsed')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(messages)
    } catch (error) {
      Logger.error(error, 'index')
      return response.badRequest(error)
    }
  }

  public async show({ response, params: { id } }: HttpContextContract) {
    try {
      const message = await PublishedMessage.query()
        .preload('publishUser')
        .preload('receiveDevice')
        .preload('messageUsed')
        .where('id', id)
        .first()

      if (!message) {
        return response.notFound({ success: false, message: 'Published Message not found' })
      }

      return response.ok({ data: message })
    } catch (error) {
      Logger.error(error, 'show')
      return response.badRequest(error)
    }
  }
}
