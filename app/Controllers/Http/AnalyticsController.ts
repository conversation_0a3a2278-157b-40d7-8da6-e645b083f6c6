import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Session from 'App/Models/Session'
import { DateTime } from 'luxon'
import natural from 'natural'
import { createObjectCsvWriter } from 'csv-writer'
import { generateRandomCode } from 'App/utils'
import Drive from '@ioc:Adonis/Core/Drive'
import Application from '@ioc:Adonis/Core/Application'
import fs from 'fs'
import Logger from '@ioc:Adonis/Core/Logger'

const DRIVE = process.env.NODE_ENV === 'production' ? 's3_private' : 'local'

export default class AnalyticsController {
  private readonly TREND_THRESHOLD = 0.2
  private readonly PEAK_HOURS_THRESHOLD = 0.1
  private readonly PEAK_DAYS_THRESHOLD = 0.1
  private readonly INACTIVE_DAYS = 30
  private readonly ACTIVITY_DROP_THRESHOLD = 0.5

  private tokenizer = new natural.WordTokenizer()
  private stemmer = natural.PorterStemmer
  private analyzer = new natural.SentimentAnalyzer('English', natural.PorterStemmer, 'afinn')

  private async uploadToS3(filePath: string, type: string, directory?: string) {
    const fileName = directory
      ? `${directory}/${type}_${generateRandomCode(10)}.csv`
      : `${type}_${generateRandomCode(10)}.csv`
    const file = fs.readFileSync(filePath)
    await Drive.use(DRIVE).put(fileName, file)

    return fileName
  }

  private async getSignedUrl(fileName: string) {
    if (process.env.NODE_ENV !== 'production') {
      return Drive.use(DRIVE).getUrl(fileName)
    }

    return Drive.use(DRIVE).getSignedUrl(fileName)
  }

  public async generateSessionReport({ request, response }: HttpContextContract) {
    Logger.info(request.all(), 'generateSessionReport')

    try {
      const startDate = request.input('from')
      const endDate = request.input('to')
      const listingIds = request.input('listing_ids')

      const sessions = await Session.query()
        .if(startDate && endDate, (query) => {
          query.whereBetween('started_at', [startDate, endDate])
        })
        .if(listingIds && Array.isArray(listingIds) && listingIds.length > 0, (query) => {
          query.whereIn('listing_id', listingIds)
        })
        .preload('listing')
        .preload('user')
        .preload('listingRating')
        .orderBy('started_at', 'desc')

      const csvData = sessions.map((session) => ({
        'Listing Name': session.listing?.name || 'N/A',
        'Username': session.user?.username || 'N/A',
        'Email Address': session.user?.emailAddress || 'N/A',
        'App Rating': session.listingRating?.appRating || 'N/A',
        'Experience Rating': session.listingRating?.experienceRating || 'N/A',
        'Listing Rating': session.listingRating?.listingRating || 'N/A',
        'Review': session.listingRating?.review || 'N/A',
        'Started At': session.startedAt?.toFormat('yyyy-MM-dd HH:mm:ss') || 'N/A',
        'Actual Ended At': session.actualEndedAt?.toFormat('yyyy-MM-dd HH:mm:ss') || 'N/A',
        'Usage Duration (minutes)': session.actualEndedAt
          ? Math.round(session.actualEndedAt.diff(session.startedAt, 'minutes').minutes)
          : 'N/A',
      }))

      const filePath = Application.tmpPath('session_report.csv')
      const csvWriter = createObjectCsvWriter({
        path: filePath,
        header: [
          { id: 'Listing Name', title: 'Listing Name' },
          { id: 'Username', title: 'Username' },
          { id: 'Email Address', title: 'Email Address' },
          { id: 'App Rating', title: 'App Rating' },
          { id: 'Experience Rating', title: 'Experience Rating' },
          { id: 'Listing Rating', title: 'Listing Rating' },
          { id: 'Review', title: 'Review' },
          { id: 'Started At', title: 'Started At' },
          { id: 'Actual Ended At', title: 'Actual Ended At' },
          { id: 'Usage Duration (minutes)', title: 'Usage Duration (minutes)' },
        ],
      })

      await csvWriter.writeRecords(csvData)
      const fileName = await this.uploadToS3(filePath, 'session_report', 'analytics')
      const url = await this.getSignedUrl(fileName)

      return response.ok({
        success: true,
        data: { fileName, url },
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to generate session report',
      })
    }
  }

  public async analyzeSessionRatings({ request, response }: HttpContextContract) {
    try {
      const startDate = request.input('from')
      const endDate = request.input('to')
      const listingIds = request.input('listing_ids')

      const sessions = await Session.query()
        .if(startDate && endDate, (query) => {
          query.whereBetween('started_at', [startDate, endDate])
        })
        .if(listingIds && Array.isArray(listingIds) && listingIds.length > 0, (query) => {
          query.whereIn('listing_id', listingIds)
        })
        .preload('listing')
        .preload('listingRating')
        .orderBy('started_at', 'desc')

      const listingStats = new Map()

      sessions.forEach((session) => {
        if (!session.listing) return

        const listingId = session.listing.id
        if (!listingStats.has(listingId)) {
          listingStats.set(listingId, {
            name: session.listing.name,
            ratings: [],
            reviews: [],
            totalSessions: 0,
          })
        }

        const stats = listingStats.get(listingId)
        stats.totalSessions++

        if (session.listingRating) {
          stats.ratings.push({
            appRating: session.listingRating.appRating,
            experienceRating: session.listingRating.experienceRating,
            listingRating: session.listingRating.listingRating,
          })

          if (session.listingRating.review) {
            stats.reviews.push({
              text: session.listingRating.review,
              sentiment: this.analyzeSentiment(session.listingRating.review),
            })
          }
        }
      })

      const analysisData = Array.from(listingStats.entries()).map(([_, stats]) => {
        const ratings = stats.ratings
        const reviews = stats.reviews

        const avgAppRating = ratings.reduce((sum, r) => sum + r.appRating, 0) / ratings.length || 0
        const avgExperienceRating =
          ratings.reduce((sum, r) => sum + r.experienceRating, 0) / ratings.length || 0

        const positiveReviews = reviews.filter((r) => r.sentiment === 'Positive')
        const negativeReviews = reviews.filter((r) => r.sentiment === 'Negative')

        const positiveKeywords = this.extractKeywords(positiveReviews.map((r) => r.text).join(' '))

        const negativeKeywords = this.extractKeywords(negativeReviews.map((r) => r.text).join(' '))

        return {
          'Listing Name': stats.name,
          'Total Sessions': stats.totalSessions,
          'Average App Rating': avgAppRating.toFixed(2),
          'Average Experience Rating': avgExperienceRating.toFixed(2),
          'Total Reviews': reviews.length,
          'Positive Reviews': positiveReviews.length,
          'Negative Reviews': negativeReviews.length,
          'Neutral Reviews': reviews.length - positiveReviews.length - negativeReviews.length,
          'Top Positive Keywords': positiveKeywords.join(', '),
          'Top Negative Keywords': negativeKeywords.join(', '),
          'Low Rating Alert': avgExperienceRating < 2.5 ? 'Yes' : 'No',
        }
      })

      const filePath = Application.tmpPath('session_ratings_analysis.csv')
      const csvWriter = createObjectCsvWriter({
        path: filePath,
        header: [
          { id: 'Listing Name', title: 'Listing Name' },
          { id: 'Total Sessions', title: 'Total Sessions' },
          { id: 'Average App Rating', title: 'Average App Rating' },
          { id: 'Average Experience Rating', title: 'Average Experience Rating' },
          { id: 'Total Reviews', title: 'Total Reviews' },
          { id: 'Positive Reviews', title: 'Positive Reviews' },
          { id: 'Negative Reviews', title: 'Negative Reviews' },
          { id: 'Neutral Reviews', title: 'Neutral Reviews' },
          { id: 'Top Positive Keywords', title: 'Top Positive Keywords' },
          { id: 'Top Negative Keywords', title: 'Top Negative Keywords' },
          { id: 'Low Rating Alert', title: 'Low Rating Alert' },
        ],
      })

      await csvWriter.writeRecords(analysisData)
      const fileName = await this.uploadToS3(filePath, 'session_ratings')
      const url = await this.getSignedUrl(fileName)

      return response.ok({
        success: true,
        data: { fileName, url },
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to analyze session ratings',
      })
    }
  }

  private analyzeSentiment(text: string): 'Positive' | 'Negative' | 'Neutral' {
    if (!text) return 'Neutral'

    const tokens = this.tokenizer.tokenize(text)
    const score = this.analyzer.getSentiment(tokens)

    if (score > 0.2) return 'Positive'
    if (score < -0.2) return 'Negative'
    return 'Neutral'
  }

  private extractKeywords(text: string, count: number = 5): string[] {
    if (!text) return []

    const tokens = this.tokenizer.tokenize(text.toLowerCase())
    const wordFreq: { [key: string]: number } = {}

    tokens.forEach((token) => {
      if (token.length > 3) {
        wordFreq[token] = (wordFreq[token] || 0) + 1
      }
    })

    return Object.entries(wordFreq)
      .sort(([, a], [, b]) => b - a)
      .slice(0, count)
      .map(([word]) => word)
  }

  public async analyzeSessionDurations({ request, response }: HttpContextContract) {
    try {
      const startDate = request.input('from')
      const endDate = request.input('to')
      const listingIds = request.input('listing_ids')

      const sessions = await Session.query()
        .whereNotNull('actual_ended_at')
        .if(startDate && endDate, (query) => {
          query.whereBetween('started_at', [startDate, endDate])
        })
        .if(listingIds && Array.isArray(listingIds) && listingIds.length > 0, (query) => {
          query.whereIn('listing_id', listingIds)
        })
        .preload('listing')
        .preload('user')
        .preload('listingRating')
        .orderBy('started_at', 'desc')

      const listingSessions = new Map<string, Session[]>()
      sessions.forEach((session) => {
        if (session.listing) {
          const listingId = session.listing.id
          if (!listingSessions.has(listingId)) {
            listingSessions.set(listingId, [])
          }
          listingSessions.get(listingId)!.push(session)
        }
      })

      const anomalies: any[] = []
      const listingStats: { [key: string]: any } = {}

      for (const [listingId, sessions] of listingSessions) {
        const durations = sessions.map(
          (session) => session.actualEndedAt!.diff(session.startedAt, 'minutes').minutes
        )

        const stats = this.calculateStats(durations)
        listingStats[listingId] = stats

        sessions.forEach((session) => {
          const duration = session.actualEndedAt!.diff(session.startedAt, 'minutes').minutes

          if (this.isAnomaly(duration, stats)) {
            anomalies.push({
              listingName: session.listing?.name || 'N/A',
              sessionId: session.id,
              username: session.user?.username || 'N/A',
              startedAt: session.startedAt.toFormat('yyyy-MM-dd HH:mm:ss'),
              endedAt: session.actualEndedAt!.toFormat('yyyy-MM-dd HH:mm:ss'),
              duration: Math.round(duration),
              averageDuration: Math.round(stats.average),
              standardDeviation: Math.round(stats.standardDeviation),
              anomalyType: duration < stats.average ? 'Short' : 'Long',
              rating: session.listingRating?.experienceRating || null,
              review: session.listingRating?.review || null,
            })
          }
        })
      }

      const filePath = Application.tmpPath('session_duration_anomalies.csv')
      const csvWriter = createObjectCsvWriter({
        path: filePath,
        header: [
          { id: 'listingName', title: 'Listing Name' },
          { id: 'sessionId', title: 'Session ID' },
          { id: 'username', title: 'Username' },
          { id: 'startedAt', title: 'Started At' },
          { id: 'endedAt', title: 'Ended At' },
          { id: 'duration', title: 'Duration (minutes)' },
          { id: 'averageDuration', title: 'Average Duration (minutes)' },
          { id: 'standardDeviation', title: 'Standard Deviation' },
          { id: 'anomalyType', title: 'Anomaly Type' },
          { id: 'rating', title: 'Rating' },
          { id: 'review', title: 'Review' },
        ],
      })

      await csvWriter.writeRecords(anomalies)
      const fileName = await this.uploadToS3(filePath, 'session_durations')
      const url = await this.getSignedUrl(fileName)

      return response.ok({
        success: true,
        data: { fileName, url },
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to analyze session durations',
      })
    }
  }

  private calculateStats(durations: number[]): any {
    if (durations.length === 0) {
      return {
        average: 0,
        standardDeviation: 0,
        min: 0,
        max: 0,
        totalSessions: 0,
      }
    }

    const sum = durations.reduce((a, b) => a + b, 0)
    const average = sum / durations.length
    const squareDiffs = durations.map((duration) => Math.pow(duration - average, 2))
    const standardDeviation = Math.sqrt(squareDiffs.reduce((a, b) => a + b, 0) / durations.length)

    return {
      average,
      standardDeviation,
      min: Math.min(...durations),
      max: Math.max(...durations),
      totalSessions: durations.length,
    }
  }

  private isAnomaly(duration: number, stats: any): boolean {
    const threshold = 2 // Number of standard deviations
    return Math.abs(duration - stats.average) > threshold * stats.standardDeviation
  }

  public async segmentUsers({ request, response }: HttpContextContract) {
    try {
      const startDate = request.input('from')
      const endDate = request.input('to')
      const listingIds = request.input('listing_ids')

      const sessions = await Session.query()
        .if(startDate && endDate, (query) => {
          query.whereBetween('started_at', [startDate, endDate])
        })
        .if(listingIds && Array.isArray(listingIds) && listingIds.length > 0, (query) => {
          query.whereIn('listing_id', listingIds)
        })
        .preload('user')
        .orderBy('created_at', 'desc')

      const uniqueUsers = new Map<string, any>()
      sessions.forEach((session) => {
        if (session.user && !uniqueUsers.has(session.user.id)) {
          uniqueUsers.set(session.user.id, session.user)
        }
      })

      const segments: {
        powerUsers: any[]
        atRiskUsers: any[]
        inactiveUsers: any[]
      } = {
        powerUsers: [],
        atRiskUsers: [],
        inactiveUsers: [],
      }

      for (const [userId, user] of uniqueUsers) {
        const stats = await this.getUserStats(userId, listingIds)

        if (stats.totalSessions >= 10 && stats.totalDuration >= 300 && stats.detailedReviews >= 3) {
          segments.powerUsers.push({
            userId: user.id,
            username: user.username,
            emailAddress: user.emailAddress,
            totalSessions: stats.totalSessions,
            totalDuration: Math.round(stats.totalDuration),
            detailedReviews: stats.detailedReviews,
            lastActivity: stats.lastActivity?.toFormat('yyyy-MM-dd HH:mm:ss') || 'N/A',
            segment: 'Power User',
          })
        }

        if (
          stats.recentSessions > 0 &&
          stats.recentSessions < stats.totalSessions * this.ACTIVITY_DROP_THRESHOLD &&
          stats.lowRatings >= 2
        ) {
          segments.atRiskUsers.push({
            userId: user.id,
            username: user.username,
            emailAddress: user.emailAddress,
            totalSessions: stats.totalSessions,
            recentSessions: stats.recentSessions,
            lowRatings: stats.lowRatings,
            lastActivity: stats.lastActivity?.toFormat('yyyy-MM-dd HH:mm:ss') || 'N/A',
            segment: 'At-Risk User',
          })
        }

        if (
          !stats.lastActivity ||
          stats.lastActivity < DateTime.now().minus({ days: this.INACTIVE_DAYS })
        ) {
          segments.inactiveUsers.push({
            userId: user.id,
            username: user.username,
            emailAddress: user.emailAddress,
            totalSessions: stats.totalSessions,
            lastActivity: stats.lastActivity?.toFormat('yyyy-MM-dd HH:mm:ss') || 'N/A',
            segment: 'Inactive User',
          })
        }
      }

      const allSegments = [
        ...segments.powerUsers,
        ...segments.atRiskUsers,
        ...segments.inactiveUsers,
      ]

      const filePath = Application.tmpPath('user_segments.csv')
      const csvWriter = createObjectCsvWriter({
        path: filePath,
        header: [
          { id: 'userId', title: 'User ID' },
          { id: 'username', title: 'Username' },
          { id: 'emailAddress', title: 'Email Address' },
          { id: 'segment', title: 'Segment' },
          { id: 'totalSessions', title: 'Total Sessions' },
          { id: 'totalDuration', title: 'Total Duration (minutes)' },
          { id: 'recentSessions', title: 'Recent Sessions' },
          { id: 'lowRatings', title: 'Low Ratings' },
          { id: 'detailedReviews', title: 'Detailed Reviews' },
          { id: 'lastActivity', title: 'Last Activity' },
        ],
      })

      await csvWriter.writeRecords(allSegments)
      const fileName = await this.uploadToS3(filePath, 'user_segments')
      const url = await this.getSignedUrl(fileName)

      return response.ok({
        success: true,
        data: { fileName, url },
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to segment users',
      })
    }
  }

  private async getUserStats(userId: string, listingIds?: string[]) {
    const sessionsQuery = Session.query()
      .where('user_id', userId)
      .preload('listingRating')
      .orderBy('created_at', 'desc')
    if (listingIds && Array.isArray(listingIds) && listingIds.length > 0) {
      sessionsQuery.whereIn('listing_id', listingIds)
    }
    const sessions = await sessionsQuery

    const totalSessions = sessions.length
    const totalDuration = sessions.reduce((sum, session) => {
      if (session.actualEndedAt) {
        return sum + session.actualEndedAt.diff(session.startedAt, 'minutes').minutes
      }
      return sum
    }, 0)

    const recentSessions = sessions.filter(
      (session) => session.createdAt >= DateTime.now().minus({ days: this.INACTIVE_DAYS })
    )

    const recentDuration = recentSessions.reduce((sum, session) => {
      if (session.actualEndedAt) {
        return sum + session.actualEndedAt.diff(session.startedAt, 'minutes').minutes
      }
      return sum
    }, 0)

    const lowRatings = sessions.filter(
      (session) =>
        session.listingRating &&
        (session.listingRating.appRating <= 2 || session.listingRating.experienceRating <= 2)
    ).length

    const detailedReviews = sessions.filter(
      (session) =>
        session.listingRating &&
        session.listingRating.review &&
        session.listingRating.review.length > 50
    ).length

    return {
      totalSessions,
      totalDuration,
      recentSessions: recentSessions.length,
      recentDuration,
      lowRatings,
      detailedReviews,
      lastActivity: sessions[0]?.createdAt || null,
    }
  }

  public async monitorDataQuality({ request, response }: HttpContextContract) {
    try {
      const startDate = request.input('from')
      const endDate = request.input('to')
      const listingIds = request.input('listing_ids')

      const sessions = await Session.query()
        .if(startDate && endDate, (query) => {
          query.whereBetween('started_at', [startDate, endDate])
        })
        .if(listingIds && Array.isArray(listingIds) && listingIds.length > 0, (query) => {
          query.whereIn('listing_id', listingIds)
        })
        .preload('listing')
        .preload('user')
        .preload('listingRating')
        .orderBy('created_at', 'desc')

      const issues: any[] = []

      sessions.forEach((session) => {
        if (!session.listing) {
          issues.push({
            sessionId: session.id,
            listingName: 'N/A',
            username: session.user?.username || 'N/A',
            issueType: 'Missing Listing',
            details: 'Session has no associated listing',
            createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
          })
        }

        if (!session.user) {
          issues.push({
            sessionId: session.id,
            listingName: session.listing?.name || 'N/A',
            username: 'N/A',
            issueType: 'Missing User',
            details: 'Session has no associated user',
            createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
          })
        }

        if (!this.validateTimestamp(session.startedAt)) {
          issues.push({
            sessionId: session.id,
            listingName: session.listing?.name || 'N/A',
            username: session.user?.username || 'N/A',
            issueType: 'Invalid Start Time',
            details: 'Session start time is invalid',
            createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
          })
        }

        if (session.actualEndedAt && !this.validateTimestamp(session.actualEndedAt)) {
          issues.push({
            sessionId: session.id,
            listingName: session.listing?.name || 'N/A',
            username: session.user?.username || 'N/A',
            issueType: 'Invalid End Time',
            details: 'Session end time is invalid',
            createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
          })
        }

        if (
          session.actualEndedAt &&
          !this.validateDuration(session.startedAt, session.actualEndedAt)
        ) {
          issues.push({
            sessionId: session.id,
            listingName: session.listing?.name || 'N/A',
            username: session.user?.username || 'N/A',
            issueType: 'Invalid Duration',
            details: 'Session end time is before start time',
            createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
          })
        }

        if (session.actualEndedAt && !session.listingRating) {
          issues.push({
            sessionId: session.id,
            listingName: session.listing?.name || 'N/A',
            username: session.user?.username || 'N/A',
            issueType: 'Missing Rating',
            details: 'Completed session has no rating',
            createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
          })
        }

        if (session.user && !session.user.username && !session.user.emailAddress) {
          issues.push({
            sessionId: session.id,
            listingName: session.listing?.name || 'N/A',
            username: 'N/A',
            issueType: 'Incomplete User Data',
            details: 'User has neither username nor email address',
            createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
          })
        }
      })

      const filePath = Application.tmpPath('data_quality_issues.csv')
      const csvWriter = createObjectCsvWriter({
        path: filePath,
        header: [
          { id: 'sessionId', title: 'Session ID' },
          { id: 'listingName', title: 'Listing Name' },
          { id: 'username', title: 'Username' },
          { id: 'issueType', title: 'Issue Type' },
          { id: 'details', title: 'Details' },
          { id: 'createdAt', title: 'Created At' },
        ],
      })

      await csvWriter.writeRecords(issues)
      const fileName = await this.uploadToS3(filePath, 'data_quality')
      const url = await this.getSignedUrl(fileName)

      return response.ok({
        success: true,
        data: { fileName, url },
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to monitor data quality',
      })
    }
  }

  private validateTimestamp(timestamp: DateTime | null): boolean {
    if (!timestamp) return false
    return timestamp.isValid
  }

  private validateDuration(startedAt: DateTime, endedAt: DateTime | null): boolean {
    if (!endedAt) return false
    if (!this.validateTimestamp(startedAt) || !this.validateTimestamp(endedAt)) return false
    return endedAt > startedAt
  }

  public async analyzeReviewThemes({ request, response }: HttpContextContract) {
    try {
      const startDate = request.input('from')
      const endDate = request.input('to')
      const listingIds = request.input('listing_ids')

      const sessions = await Session.query()
        .whereNotNull('actual_ended_at')
        .if(startDate && endDate, (query) => {
          query.whereBetween('started_at', [startDate, endDate])
        })
        .if(listingIds && Array.isArray(listingIds) && listingIds.length > 0, (query) => {
          query.whereIn('listing_id', listingIds)
        })
        .preload('listing')
        .preload('listingRating')
        .orderBy('created_at', 'desc')

      const listingReviews = new Map<string, { name: string; reviews: string[] }>()

      sessions.forEach((session) => {
        if (session.listing && session.listingRating?.review) {
          const listingId = session.listing.id
          if (!listingReviews.has(listingId)) {
            listingReviews.set(listingId, {
              name: session.listing.name,
              reviews: [],
            })
          }
          listingReviews.get(listingId)!.reviews.push(session.listingRating.review)
        }
      })

      const themeSummaries: any[] = []

      for (const [_, data] of listingReviews) {
        const { name, reviews } = data

        const positiveThemes = new Set<string>()
        const negativeThemes = new Set<string>()
        const improvementAreas = new Set<string>()

        reviews.forEach((review) => {
          const sentiment = this.analyzer.getSentiment(this.tokenizer.tokenize(review))

          if (sentiment > 0.2) {
            this.extractThemes(review, 'positive').forEach((theme) => positiveThemes.add(theme))
          } else if (sentiment < -0.2) {
            this.extractThemes(review, 'negative').forEach((theme) => negativeThemes.add(theme))
          }

          this.extractThemes(review, 'improvement').forEach((theme) => improvementAreas.add(theme))
        })

        themeSummaries.push({
          listingName: name,
          totalReviews: reviews.length,
          positiveThemes: Array.from(positiveThemes),
          negativeThemes: Array.from(negativeThemes),
          improvementAreas: Array.from(improvementAreas),
          emergingTrends: this.identifyEmergingTrends(reviews),
        })
      }

      const filePath = Application.tmpPath('review_themes_analysis.csv')
      const csvWriter = createObjectCsvWriter({
        path: filePath,
        header: [
          { id: 'listingName', title: 'Listing Name' },
          { id: 'totalReviews', title: 'Total Reviews' },
          { id: 'positiveThemes', title: 'Positive Themes' },
          { id: 'negativeThemes', title: 'Negative Themes' },
          { id: 'improvementAreas', title: 'Areas for Improvement' },
          { id: 'emergingTrends', title: 'Emerging Trends' },
        ],
      })

      await csvWriter.writeRecords(
        themeSummaries.map((summary) => ({
          ...summary,
          positiveThemes: summary.positiveThemes.join(', '),
          negativeThemes: summary.negativeThemes.join(', '),
          improvementAreas: summary.improvementAreas.join(', '),
          emergingTrends: summary.emergingTrends.join(', '),
        }))
      )

      const fileName = await this.uploadToS3(filePath, 'review_themes')
      const url = await this.getSignedUrl(fileName)

      return response.ok({
        success: true,
        data: { fileName, url },
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to analyze review themes',
      })
    }
  }

  private readonly THEME_CATEGORIES = {
    positive: [
      'clean',
      'comfortable',
      'convenient',
      'friendly',
      'helpful',
      'nice',
      'quiet',
      'spacious',
      'well-maintained',
      'good',
    ],
    negative: [
      'dirty',
      'uncomfortable',
      'inconvenient',
      'noisy',
      'crowded',
      'broken',
      'poor',
      'bad',
      'difficult',
      'problem',
    ],
    improvement: [
      'improve',
      'better',
      'need',
      'should',
      'could',
      'suggestion',
      'recommend',
      'wish',
      'hope',
      'expect',
    ],
  }

  private extractThemes(text: string, category: 'positive' | 'negative' | 'improvement'): string[] {
    if (!text) return []

    const tokens = this.tokenizer.tokenize(text.toLowerCase())
    const stemmedTokens = tokens.map((token) => this.stemmer.stem(token))

    const themes = new Set<string>()
    this.THEME_CATEGORIES[category].forEach((theme) => {
      const stemmedTheme = this.stemmer.stem(theme)
      if (stemmedTokens.includes(stemmedTheme)) {
        themes.add(theme)
      }
    })

    return Array.from(themes)
  }

  private identifyEmergingTrends(reviews: string[]): string[] {
    if (reviews.length < 3) return []

    const recentReviews = reviews.slice(0, Math.floor(reviews.length / 3))
    const olderReviews = reviews.slice(Math.floor(reviews.length / 3))

    const recentThemes = new Set<string>()
    const olderThemes = new Set<string>()

    recentReviews.forEach((review) => {
      const tokens = this.tokenizer.tokenize(review.toLowerCase())
      tokens.forEach((token) => {
        if (token.length > 4) recentThemes.add(token)
      })
    })

    olderReviews.forEach((review) => {
      const tokens = this.tokenizer.tokenize(review.toLowerCase())
      tokens.forEach((token) => {
        if (token.length > 4) olderThemes.add(token)
      })
    })

    const emergingTrends = Array.from(recentThemes).filter((theme) => {
      const recentCount = recentReviews.filter((review) =>
        review.toLowerCase().includes(theme)
      ).length
      const olderCount = olderReviews.filter((review) =>
        review.toLowerCase().includes(theme)
      ).length
      return recentCount > olderCount * 2
    })

    return emergingTrends.slice(0, 5)
  }

  public async analyzeListingTrends({ request, response }: HttpContextContract) {
    try {
      const startDate = request.input('from')
      const endDate = request.input('to')
      const listingIds = request.input('listing_ids')

      const sessions = await Session.query()
        .whereNotNull('actual_ended_at')
        .if(startDate && endDate, (query) => {
          query.whereBetween('started_at', [startDate, endDate])
        })
        .if(listingIds && Array.isArray(listingIds) && listingIds.length > 0, (query) => {
          query.whereIn('listing_id', listingIds)
        })
        .preload('listing')
        .orderBy('started_at', 'desc')

      const listingSessions = new Map<string, Session[]>()
      sessions.forEach((session) => {
        if (session.listing) {
          const listingId = session.listing.id
          if (!listingSessions.has(listingId)) {
            listingSessions.set(listingId, [])
          }
          listingSessions.get(listingId)!.push(session)
        }
      })

      const usageStats: any[] = []

      for (const [_, sessions] of listingSessions) {
        const totalSessions = sessions.length
        const totalDuration = sessions.reduce((sum, session) => {
          return sum + session.actualEndedAt!.diff(session.startedAt, 'minutes').minutes
        }, 0)
        const averageDuration = totalDuration / totalSessions

        const now = DateTime.now()
        const recentSessions = sessions.filter((s) => s.startedAt >= now.minus({ months: 1 }))
        const olderSessions = sessions.filter(
          (s) => s.startedAt >= now.minus({ months: 2 }) && s.startedAt < now.minus({ months: 1 })
        )

        const { trend, percentage } = this.calculateTrend(
          recentSessions.length,
          olderSessions.length
        )

        usageStats.push({
          listingName: sessions[0].listing!.name,
          totalSessions,
          totalDuration: Math.round(totalDuration),
          averageDuration: Math.round(averageDuration),
          peakHours: this.findPeakHours(sessions),
          peakDays: this.findPeakDays(sessions),
          trend,
          trendPercentage: Math.round(percentage * 100),
        })
      }

      const filePath = Application.tmpPath('listing_trends_analysis.csv')
      const csvWriter = createObjectCsvWriter({
        path: filePath,
        header: [
          { id: 'listingName', title: 'Listing Name' },
          { id: 'totalSessions', title: 'Total Sessions' },
          { id: 'totalDuration', title: 'Total Duration (minutes)' },
          { id: 'averageDuration', title: 'Average Duration (minutes)' },
          { id: 'peakHours', title: 'Peak Hours' },
          { id: 'peakDays', title: 'Peak Days' },
          { id: 'trend', title: 'Usage Trend' },
          { id: 'trendPercentage', title: 'Trend Percentage' },
        ],
      })

      await csvWriter.writeRecords(
        usageStats.map((stats) => ({
          ...stats,
          peakHours: stats.peakHours.join(', '),
          peakDays: stats.peakDays.join(', '),
        }))
      )

      const fileName = await this.uploadToS3(filePath, 'listing_trends')
      const url = await this.getSignedUrl(fileName)

      return response.ok({
        success: true,
        data: { fileName, url },
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to analyze listing trends',
      })
    }
  }

  private calculateTrend(
    recentSessions: number,
    olderSessions: number
  ): { trend: 'up' | 'down' | 'stable'; percentage: number } {
    if (olderSessions === 0) return { trend: 'stable', percentage: 0 }

    const percentage = (recentSessions - olderSessions) / olderSessions

    if (percentage > this.TREND_THRESHOLD) return { trend: 'up', percentage }
    if (percentage < -this.TREND_THRESHOLD) return { trend: 'down', percentage }
    return { trend: 'stable', percentage }
  }

  private findPeakHours(sessions: Session[]): string[] {
    const hourlyCounts = new Array(24).fill(0)

    sessions.forEach((session) => {
      const hour = session.startedAt.hour
      hourlyCounts[hour]++
    })

    const average = hourlyCounts.reduce((a, b) => a + b, 0) / 24
    const threshold = average * (1 + this.PEAK_HOURS_THRESHOLD)

    return hourlyCounts
      .map((count, hour) => ({ hour, count }))
      .filter(({ count }) => count > threshold)
      .map(({ hour }) => `${hour.toString().padStart(2, '0')}:00`)
  }

  private findPeakDays(sessions: Session[]): string[] {
    const dayCounts = new Array(7).fill(0)
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

    sessions.forEach((session) => {
      const day = session.startedAt.weekday % 7
      dayCounts[day]++
    })

    const average = dayCounts.reduce((a, b) => a + b, 0) / 7
    const threshold = average * (1 + this.PEAK_DAYS_THRESHOLD)

    return dayCounts
      .map((count, day) => ({ day, count }))
      .filter(({ count }) => count > threshold)
      .map(({ day }) => days[day])
  }
}
