import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Device from 'App/Models/Device'
import Logger from '@ioc:Adonis/Core/Logger'
import { varcharRule } from 'App/utils'
import User from 'App/Models/User'
import Database from '@ioc:Adonis/Lucid/Database'

export default class DevicesController {
  public async create({ request, response, auth, route }: HttpContextContract) {
    Logger.info('DevicesController.create')

    const user = await auth.authenticate()

    const validationSchema = schema.create({
      user_id: schema.string.optional([rules.exists({ column: 'id', table: 'users' })]),
      device_token: schema.string(varcharRule),
    })
    const payload = await request.validate({ schema: validationSchema })

    try {
      if (!route?.pattern.match('/me/devices') && !payload.user_id) {
        // admin route calling needs to provide user id
        return response.unprocessableEntity({ success: false, message: 'Please provide user id' })
      }

      const isExistDeviceToken = await Device.findBy('device_token', payload.device_token)

      if (isExistDeviceToken) {
        const isExistUserIdDeviceToken = await Database.query()
          .from('user_devices')
          .where('user_id', route?.pattern.match('/me/devices') ? user.id : payload.user_id!)
          .andWhere('device_id', isExistDeviceToken.id)
          .first()

        // Make sure device token and user id is unique
        if (isExistUserIdDeviceToken) {
          return response.conflict({
            success: false,
            message:
              'User id and the token has already exist. Please provide unique combination of user id and device token.',
          })
        }
      }

      await Database.transaction(async (trx) => {
        const device = isExistDeviceToken
          ? isExistDeviceToken
          : await Device.create({ deviceToken: payload.device_token }, { client: trx })

        if (route?.pattern.match('/me/devices')) {
          // user route hence use auth user
          await user.related('devices').attach([device.id], trx)
        } else {
          // admin give a specific user id to attach device
          const specificUser = await User.findOrFail(payload.user_id)

          await specificUser.related('devices').attach([device.id], trx)
        }
      })

      return response.ok({ success: true, message: 'Device linked or created successfully' })
    } catch (error) {
      Logger.error(error, 'DevicesController.create')
      return response.badRequest(error)
    }
  }

  public async index({ request, response }: HttpContextContract) {
    try {
      Logger.info('DevicesController.index')
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)

      const devices = await Device.query().orderBy(sort[0], sort[1]).paginate(page, limit)

      return response.ok(devices)
    } catch (error) {
      Logger.error(error, 'DevicesController.index')
      return response.badRequest(error)
    }
  }

  public async show({ response, params: { id } }: HttpContextContract) {
    try {
      Logger.info('DevicesController.show')
      const device = await Device.query().where('id', id).first()

      if (!device) {
        return response.notFound({ success: false, message: 'Device not found' })
      }

      return response.ok({ data: device })
    } catch (error) {
      Logger.error(error, 'DevicesController.show')
      return response.badRequest(error)
    }
  }

  public async delete({ response, params: { id } }: HttpContextContract) {
    try {
      Logger.info('DevicesController.delete')
      const toDelete = await Device.find(id)

      if (!toDelete) {
        return response.notFound({ success: false, message: 'Device not found' })
      }

      await toDelete.delete()

      return response.ok({ success: true, message: 'Device deleted successfully' })
    } catch (error) {
      Logger.error(error, 'DevicesController.delete')
      return response.badRequest(error)
    }
  }
}
