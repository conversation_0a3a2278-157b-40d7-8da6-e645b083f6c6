import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ListingFile from 'App/Models/ListingFile'
import Logger from '@ioc:Adonis/Core/Logger'

export default class ListingFilesController {
  public async findListingFile({ params: { id }, response }: HttpContextContract) {
    try {
      const listingFile = await ListingFile.query()
        .where('id', id)
        .andWhere('is_hidden', false)
        .first()

      if (!listingFile) {
        return response.notFound({ success: false, message: 'Listing not found' })
      }

      return response.ok({ data: listingFile })
    } catch (error) {
      Logger.error(error, 'findListingFile')
      return response.badRequest(error)
    }
  }

  public async findListingFileAdmin({ params: { id }, response }: HttpContextContract) {
    try {
      const listingFile = await ListingFile.query().where('id', id).first()

      if (!listingFile) {
        return response.notFound({ success: false, message: 'Listing not found' })
      }

      return response.ok({ data: listingFile })
    } catch (error) {
      Logger.error(error, 'findListingFile')
      return response.badRequest(error)
    }
  }

  public async findListingFilesAdmin({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)

      const listingFiles = await ListingFile.query().orderBy(sort[0], sort[1]).paginate(page, limit)

      return response.ok(listingFiles)
    } catch (error) {
      Logger.error(error, 'findListingFilesAdmin')
      return response.badRequest(error)
    }
  }
}
