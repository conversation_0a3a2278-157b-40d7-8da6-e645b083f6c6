import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import { schema } from '@ioc:Adonis/Core/Validator'
import Logger from '@ioc:Adonis/Core/Logger'
import Profile, { SocialType } from 'App/Models/Profile'
import User, { userFindFields } from 'App/Models/User'
import { generateRandomCode } from 'App/utils'
import { UserType } from 'Contracts/user_type'
import _ from 'lodash'
import { loginProcess } from './AuthController'

export default class OAuthController {
  // GOOGLE WEB
  // public async googleRedirect({ ally, response }: HttpContextContract) {
  //   Logger.info('google redirect')
  //   const google = ally.use('google')
  //   const authState = generateRandomCode(32)

  //   authSessionStorage[authState] = {
  //     ref: setTimeout(() => {
  //       // session expired
  //       delete authSessionStorage[authState]
  //     }, expiredIn),
  //   }
  //   const redirectUrl = await google.redirectUrl((req) => req.param('state', authState))
  //   return response.ok({ success: true, redirect: redirectUrl })
  // }

  // public async googleLinkingRedirect({ ally, auth, response }: HttpContextContract) {
  //   Logger.info('google linking redirect')
  //   const user = await User.findOrFail(auth.user?.id)
  //   const google = ally.use('google')
  //   const authState = generateRandomCode(32)

  //   authSessionStorage[authState] = {
  //     user_id: user.id,
  //     ref: setTimeout(() => {
  //       // session expired
  //       delete authSessionStorage[authState]
  //     }, expiredIn),
  //   }

  //   const redirectUrl = await google.redirectUrl((req) => req.param('state', authState))
  //   return response.ok({ success: true, redirect: redirectUrl })
  // }

  // public async googleCallback({ ally, request, response, auth }: HttpContextContract) {
  //   try {
  //     Logger.info('google callback')
  //     // Check if the authState in our authSession
  //     let user: User | null = null
  //     const authState = request.input('state')
  //     let accountLinking = false
  //     if (authSessionStorage[authState] && authSessionStorage[authState].user_id) {
  //       accountLinking = true
  //       user = await User.findOrFail(authSessionStorage[authState].user_id)
  //       delete authSessionStorage[authState]
  //     } else if (authSessionStorage[authState]) {
  //       delete authSessionStorage[authState]
  //     } else {
  //       throw new Error('Request expired, Retry again')
  //     }

  //     // if account linking = true, bypass driver state checking
  //     const google = ally.use('google').stateless()

  //     if (google.accessDenied()) {
  //       throw new Error('Access was denied')
  //     }

  //     if (google.hasError()) {
  //       throw new Error(google.getError() ?? 'google error')
  //     }

  //     const googleUser = await google.user()
  //     Logger.info('Google authenticated: ', googleUser.email)

  //     if (!googleUser.id) {
  //       throw new Error('Unable to find google user')
  //     }

  //     // User > Profile, meaning User exist, profile not necessary exist
  //     let googleProfile = await Profile.findBy('social_id', googleUser.id)
  //     // if google profile exist, user must exist
  //     if (user == null) {
  //       if (googleProfile) {
  //         user = await User.findOrFail(googleProfile.userId)
  //       } else {
  //         user = await User.findBy('email', googleUser.email)
  //       }
  //     }
  //     // first or create
  //     if (user == null) {
  //       await Database.transaction(async (trx) => {
  //         // create user
  //         user = new User()
  //         user.merge({
  //           username: googleUser.nickName,
  //           emailAddress: googleUser.email ?? undefined,
  //           password: generateRandomCode(8) + '',
  //           authProvider: 'email',
  //           isEmailAddressVerified: false,
  //           userType: UserType.user,
  //         })

  //         user.useTransaction(trx)
  //         await user.save()

  //         const googleProfile = new Profile()
  //         googleProfile.merge({
  //           userId: user.id,
  //           socialType: SocialType.GOOGLE,
  //           socialId: googleUser.id,
  //           email: googleUser.email ?? undefined,
  //           firstName: googleUser.original?.given_name,
  //           lastName: googleUser.original?.family_name,
  //         })
  //         googleProfile.useTransaction(trx)
  //         await googleProfile.save()
  //       })
  //     } else {
  //       if (googleProfile && googleProfile.userId !== user.id) {
  //         throw new Error('Invalid access')
  //       }
  //       // user exist, but profile not necessary exist
  //       await Database.transaction(async (trx) => {
  //         if (googleProfile) {
  //           googleProfile.merge({
  //             ...(googleUser.email && { email: googleUser.email }),
  //             firstName: googleUser.original?.first_name,
  //             lastName: googleUser.original?.last_name,
  //           })
  //         } else {
  //           googleProfile = new Profile()
  //           googleProfile.merge({
  //             userId: user!.id,
  //             socialType: SocialType.GOOGLE,
  //             socialId: googleUser.id,
  //             email: googleUser.email!,
  //             firstName: googleUser.original?.given_name,
  //             lastName: googleUser.original?.family_name,
  //           })
  //         }
  //         googleProfile.useTransaction(trx)
  //         await googleProfile.save()
  //       })
  //     }

  //     // avoid user login if soft deleted
  //     if (user?.deletedAt) {
  //       return response.forbidden({ success: false, code: 'User are soft deleted' })
  //     }

  //     Logger.info('Login with Google as: ', googleUser.name, googleUser.email)

  //     const userProfiles = await user!.related('profiles').query()
  //     if (accountLinking) {
  //       return response.ok({ user: { ...user?.toJSON(), profiles: userProfiles } })
  //     }

  //     const token = await auth.use('api').generate(user!)
  //     await token.user.load('favoriteListings')
  //     await token.user.load('profiles')

  //     return response.ok({
  //       data: { user: token.user.serialize(userFindFields), token: token, profiles: userProfiles },
  //     })
  //   } catch (e) {
  //     Logger.error(e)
  //     return response.status(401).send({ success: false, code: 'Social authentication failed' })
  //   }
  // }

  // public async getProfiles({ auth, response }: HttpContextContract) {
  //   const user = await User.query()
  //     .where('id', auth.user?.id ?? 0)
  //     .preload('profiles')
  //     .firstOrFail()

  //   return response.ok({ data: { ...user.toJSON() } })
  // }

  public async loginGoogle({ ally, auth, response, request }: HttpContextContract) {
    const validationSchema = schema.create({
      access_token: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      const googleProfile = await ally.use('google').userFromToken(validationData.access_token)
      Logger.info(googleProfile, 'loginGoogle')

      const existUserHasProfile = await User.query()
        .whereHas('profiles', (query) => {
          query.where('social_type', SocialType.GOOGLE).where('social_id', googleProfile.id)
        })
        .first()

      const existUserSameGoogleEmailWithoutProfile = await User.findBy(
        'email_address',
        googleProfile.email
      )
      if (
        existUserHasProfile != null
        // TODO: what to do if user is soft deleted
        // && user.deletedAt == null
      ) {
        // login user
        const token = await loginProcess(auth, existUserHasProfile)

        return response.ok({
          success: true,
          data: { user: token.user.serialize(userFindFields), token: token },
        })
      } else if (existUserSameGoogleEmailWithoutProfile != null) {
        // create profile to link with existing User with the same Google email
        await Profile.firstOrCreate(
          {
            userId: existUserSameGoogleEmailWithoutProfile.id,
            socialType: SocialType.GOOGLE,
            socialId: googleProfile.id!,
          },
          {
            userId: existUserSameGoogleEmailWithoutProfile.id,
            socialType: SocialType.GOOGLE,
            socialId: googleProfile.id!,
            email: googleProfile.email!,
            firstName: googleProfile.original?.first_name,
            lastName: googleProfile.original?.last_name,
            photoUrl: googleProfile.avatarUrl ?? undefined,
            // username
          }
        )

        const token = await loginProcess(auth, existUserSameGoogleEmailWithoutProfile)

        return response.ok({
          success: true,
          data: { user: token.user.serialize(userFindFields), token: token },
        })
      } else {
        const user = await Database.transaction(async (trx) => {
          // register user
          const newUser = await User.create(
            {
              emailAddress: googleProfile.email!,
              password: generateRandomCode(8) + '',
              userType: UserType.user,
              authProvider: 'email',
              firstName: googleProfile.original?.first_name,
              lastName: googleProfile.original?.last_name,
              photoUrl: googleProfile.avatarUrl ?? undefined,
              // TODO: should we ask user for username in another step before call login api
              // username: payload.username,
            },
            { client: trx }
          )

          // create profile to link with new created User
          await Profile.firstOrCreate(
            {
              userId: newUser.id,
              socialType: SocialType.GOOGLE,
              socialId: googleProfile.id!,
            },
            {
              userId: newUser.id,
              socialType: SocialType.GOOGLE,
              socialId: googleProfile.id!,
              email: googleProfile.email!,
              firstName: googleProfile.original?.first_name,
              lastName: googleProfile.original?.last_name,
              photoUrl: googleProfile.avatarUrl ?? undefined,
              // username
            },
            { client: trx }
          )

          return newUser
        })

        if (user) {
          const token = await loginProcess(auth, user)

          return response.ok({
            success: true,
            data: { user: token.user.serialize(userFindFields), token: token },
          })
        } else {
          return response.status(401).send({
            message: 'Failed to authenticate',
          })
        }
      }
    } catch (error) {
      Logger.error(error, 'Google login error')
      return response.status(400).send({
        message: 'Failed to authenticate.',
      })
    }
  }

  public async loginFacebook({ ally, request, auth, response }: HttpContextContract) {
    const validationSchema = schema.create({
      access_token: schema.string(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      const facebookProfile = await ally.use('facebook').userFromToken(validationData.access_token)
      Logger.info(facebookProfile, 'loginFacebook')

      const existUserHasProfile = await User.query()
        .whereHas('profiles', (query) => {
          query.where('social_type', SocialType.FACEBOOK).where('social_id', facebookProfile.id)
        })
        .first()

      const existUserSameFacebookEmailWithoutProfile = await User.findBy(
        'email_address',
        facebookProfile.email
      )

      if (
        existUserHasProfile != null
        // TODO: what to do if user is soft deleted
        // && user.deletedAt == null
      ) {
        // login user
        const token = await loginProcess(auth, existUserHasProfile)

        return response.ok({
          success: true,
          data: { user: token.user.serialize(userFindFields), token: token },
        })
      } else if (existUserSameFacebookEmailWithoutProfile != null) {
        // create profile to link with existing User with the same Facebook email
        await Profile.firstOrCreate(
          {
            userId: existUserSameFacebookEmailWithoutProfile.id,
            socialType: SocialType.FACEBOOK,
            socialId: facebookProfile.id!,
          },
          {
            userId: existUserSameFacebookEmailWithoutProfile.id,
            socialType: SocialType.FACEBOOK,
            socialId: facebookProfile.id!,
            email: facebookProfile.email!,
            firstName: facebookProfile.original?.first_name,
            lastName: facebookProfile.original?.last_name,
            photoUrl: facebookProfile.avatarUrl ?? undefined,
            // username
          }
        )

        const token = await loginProcess(auth, existUserSameFacebookEmailWithoutProfile)

        return response.ok({
          success: true,
          data: { user: token.user.serialize(userFindFields), token: token },
        })
      } else {
        // register user
        const user = await Database.transaction(async (trx) => {
          const newUser = await User.create(
            {
              emailAddress: facebookProfile.email!,
              password: generateRandomCode(8) + '',
              userType: UserType.user,
              authProvider: 'email',
              firstName: facebookProfile.original?.first_name,
              lastName: facebookProfile.original?.last_name,
              photoUrl: facebookProfile.avatarUrl ?? undefined,
              // TODO: should we ask user for username in another step before call login api
              // username: payload.username,
            },
            { client: trx }
          )

          // create profile
          await Profile.firstOrCreate(
            {
              userId: newUser.id,
              socialType: SocialType.FACEBOOK,
              socialId: facebookProfile.id!,
            },
            {
              userId: newUser.id,
              socialType: SocialType.FACEBOOK,
              socialId: facebookProfile.id!,
              email: facebookProfile.email!,
              firstName: facebookProfile.original?.first_name,
              lastName: facebookProfile.original?.last_name,
              photoUrl: facebookProfile.avatarUrl ?? undefined,
              // username
            },
            { client: trx }
          )

          return newUser
        })

        if (user) {
          const token = await loginProcess(auth, user)

          return response.ok({
            success: true,
            data: { user: token.user.serialize(userFindFields), token: token },
          })
        } else {
          return response.status(401).send({
            message: 'Failed to authenticate',
          })
        }
      }
    } catch (error) {
      Logger.error(error, 'Facebook login error')
      return response.status(400).send({
        message: 'Failed to authenticate',
      })
    }
  }

  public async loginApple({ ally, auth, response, request }: HttpContextContract) {
    const validationSchema = schema.create({
      access_token: schema.string(),
      authorization_code: schema.string(),
      first_name: schema.string.optional(),
      last_name: schema.string.optional(),
      email: schema.string.optional(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      const appleProfile = await ally.use('apple').userFromToken(validationData.access_token)
      Logger.info(appleProfile, 'loginApple')

      const existUserHasProfile = await User.query()
        .whereHas('profiles', (query) => {
          query.where('social_type', SocialType.APPLE).where('social_id', appleProfile.id)
        })
        .first()

      const existUserSameAppleEmailWithoutProfile = await User.findBy(
        'email_address',
        appleProfile.email
      )

      if (
        existUserHasProfile != null
        // TODO: what to do if user is soft deleted
        // && user.deletedAt == null
      ) {
        // login user
        const token = await loginProcess(auth, existUserHasProfile)

        return response.ok({
          success: true,
          data: { user: token.user.serialize(userFindFields), token: token },
        })
      } else if (existUserSameAppleEmailWithoutProfile != null) {
        // create profile to link with existing User with the same Apple email
        await Profile.firstOrCreate(
          {
            userId: existUserSameAppleEmailWithoutProfile.id,
            socialType: SocialType.APPLE,
            socialId: appleProfile.id!,
          },
          {
            userId: existUserSameAppleEmailWithoutProfile.id,
            socialType: SocialType.APPLE,
            socialId: appleProfile.id!,
            email: appleProfile.email!,
            firstName: appleProfile.original?.first_name,
            lastName: appleProfile.original?.last_name,
            photoUrl: appleProfile.avatarUrl ?? undefined,
            // username
          }
        )

        const token = await loginProcess(auth, existUserSameAppleEmailWithoutProfile)

        return response.ok({
          success: true,
          data: { user: token.user.serialize(userFindFields), token: token },
        })
      } else {
        // register user
        const user = await Database.transaction(async (trx) => {
          const newUser = await User.create(
            {
              emailAddress: appleProfile.email ?? validationData.email,
              password: generateRandomCode(8) + '',
              userType: UserType.user,
              authProvider: 'email',
              firstName: validationData.first_name,
              lastName: validationData.last_name,
              // TODO: should we ask user for username in another step before call login api
              // username: payload.username,
            },
            { client: trx }
          )

          // create profile
          await Profile.firstOrCreate(
            {
              userId: newUser.id,
              socialType: SocialType.APPLE,
              socialId: appleProfile.id!,
            },
            {
              userId: newUser.id,
              socialType: SocialType.APPLE,
              socialId: appleProfile.id!,
              email: appleProfile.email ?? validationData.email,
              firstName: validationData.first_name,
              lastName: validationData.last_name,
              // username
            },
            { client: trx }
          )

          return newUser
        })

        if (user) {
          const token = await loginProcess(auth, user)

          return response.ok({
            success: true,
            data: { user: token.user.serialize(userFindFields), token: token },
          })
        } else {
          return response.status(401).send({
            message: 'Failed to authenticate',
          })
        }
      }
    } catch (error) {
      Logger.error(error, 'Apple login error')
      return response.status(400).send({
        message: 'Failed to authenticate.',
      })
    }
  }
}
