import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Logger from '@ioc:Adonis/Core/Logger'
import NotificationMessage from 'App/Models/NotificationMessage'
import {
  generateRandomCode,
  imageFileSpec,
  remotePathNotificationMessage,
  varcharRule,
} from 'App/utils'
import { PUBLIC_DISKNAME } from './FilesController'
import _ from 'lodash'

export default class NotificationMessagesController {
  public async create({ request, response, auth }: HttpContextContract) {
    const user = await auth.authenticate()

    const validationSchema = schema.create({
      message: schema.string(varcharRule),
      title: schema.string(varcharRule),
      image_file: schema.file.optional(imageFileSpec),
      image_url: schema.string.optional(varcharRule),
    })
    const { image_url, image_file, ...payload } = await request.validate({
      schema: validationSchema,
    })

    try {
      const notificationMessageCreated = await NotificationMessage.create({
        title: payload.title,
        message: payload.message,
        createdBy: user.id,
      })

      if (image_url) {
        notificationMessageCreated.imageUrl = image_url
      } else if (image_file) {
        const remoteName = `${notificationMessageCreated.id}_${generateRandomCode(10)}.${
          image_file.extname
        }`

        await image_file.moveToDisk(
          remotePathNotificationMessage,
          { name: remoteName },
          PUBLIC_DISKNAME // notification message image is public
        )

        notificationMessageCreated.imageUrl = remoteName
      }

      return response.ok({ success: true, message: 'New message created' })
    } catch (error) {
      Logger.error(error, 'create')
      return response.badRequest(error)
    }
  }

  public async index({ request, response }: HttpContextContract) {
    const sortCustomColumn = (column: string) => {
      if (column == 'update_user') {
        return 'updated_by' // sort by updated_by since update_user is frontend renamed column name.
      }

      if (column == 'create_user') {
        return 'created_by' // sort by created_by since create_user is frontend renamed column name.
      }
      return column
    }

    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)
      const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      const messages = await NotificationMessage.filter(filters)
        .preload('createUser')
        .preload('updateUser')
        .orderBy(sortCustomColumn(sort[0]), sort[1])
        .paginate(page, limit)

      return response.ok(messages)
    } catch (error) {
      Logger.error(error, 'index')
      return response.badRequest(error)
    }
  }

  public async show({ response, params: { id } }: HttpContextContract) {
    try {
      const message = await NotificationMessage.query()
        .preload('createUser')
        .preload('updateUser')
        .where('id', id)
        .first()

      if (!message) {
        return response.notFound({ success: false, message: 'Message not found' })
      }

      return response.ok({ data: message })
    } catch (error) {
      Logger.error(error, 'show')
      return response.badRequest(error)
    }
  }

  public async update({ request, response, params: { id }, auth }: HttpContextContract) {
    const user = await auth.authenticate()

    const validationSchema = schema.create({
      message: schema.string.optional(varcharRule),
      title: schema.string.optional(varcharRule),
      image_file: schema.file.optional(imageFileSpec),
      image_url: schema.string.optional(varcharRule),
    })
    const { image_file, image_url, ...payload } = await request.validate({
      schema: validationSchema,
    })

    try {
      const toUpdate = await NotificationMessage.find(id)

      if (!toUpdate) {
        return response.notFound({ success: false, message: 'Message not found' })
      }

      const updated = await toUpdate.merge({ updatedBy: user.id, ...payload }).save()

      if (image_url) {
        updated.imageUrl = image_url
      } else if (image_file) {
        const remoteName = `${updated.id}_${generateRandomCode(10)}.${image_file.extname}`

        await image_file.moveToDisk(
          remotePathNotificationMessage,
          { name: remoteName },
          PUBLIC_DISKNAME // notification message image is public
        )

        updated.imageUrl = remoteName
      }

      await updated.save()

      return response.ok({
        success: true,
        message: 'Message updated successfully',
        device: toUpdate,
      })
    } catch (error) {
      Logger.error(error, 'update')
      return response.badRequest(error)
    }
  }

  public async delete({ response, params: { id } }: HttpContextContract) {
    try {
      const toDelete = await NotificationMessage.find(id)

      if (!toDelete) {
        return response.notFound({ success: false, message: 'Message not found' })
      }

      await toDelete.delete()

      return response.ok({ success: true, message: 'Message deleted successfully' })
    } catch (error) {
      Logger.error(error, 'delete')
      return response.badRequest(error)
    }
  }
}
