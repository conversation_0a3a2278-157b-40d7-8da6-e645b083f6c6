import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Listing from 'App/Models/Listing'
import ListingFile from 'App/Models/ListingFile'
import Position from 'App/Models/Position'
import BigNumber from 'bignumber.js'
import _ from 'lodash'
import * as ngeohash from 'ngeohash'
import Drive from '@ioc:Adonis/Core/Drive'
import Logger from '@ioc:Adonis/Core/Logger'
import { v4 as uuidv4 } from 'uuid'
// import ListingType from 'App/Models/ListingType'
import Region from 'App/Models/Region'
import Activity from 'App/Models/Activity'
import Amenity from 'App/Models/Amenity'
import { ListingStatus } from 'Contracts/listing_type'
import {
  newImageHandler,
  updateImageHandler,
  disk_name,
  remotePathListing,
  generateRandomCode,
  varcharRule,
} from 'App/utils'
import { ListingType } from 'Contracts/listing_type'
import { readFile } from 'fs/promises'
import { join } from 'path'
import Application from '@ioc:Adonis/Core/Application'
import Event from '@ioc:Adonis/Core/Event'
import MeilisearchService from 'App/Services/meilisearch'

const imageFileSpec = {
  size: '10mb',
  extnames: ['jpg', 'png', 'jpeg', 'JPG', 'PNG', 'JPEG'],
}
const normalUserOmitFields = {
  fields: {
    omit: ['note'],
  },
}
// multiple listings
const fetchFields = {
  fields: {
    pick: [
      'id',
      'firestore_id',
      'name',
      'company_name',
      'contact_number',
      'address_name',
      'full_address',
      'note',
      'description',
      'number_of_private_feeding_rooms',
      'opening_hours',
      'listing_type',
      'status',
      'created_at',
      'updated_at',
      'position',
      'distance',
      'is_hidden',
      'is_verified',
    ],
  },
  relations: {
    position: {
      fields: {
        pick: ['coordinate'],
      },
    },
    listing_files: {
      fields: {
        pick: ['id', 'image_url', 'is_main'],
      },
    },
  },
}
// single listings
const findFields = {
  fields: {
    pick: [
      'id',
      'firestore_id',
      'name',
      'company_name',
      'full_address',
      'opening_hours',
      'created_at',
      'updated_at',
      'position',
      'status',
      'listing_type',
      'listing_ratings',
      'description',
      'postal_code',
      'contact_number',
    ],
  },
  relations: {
    listing_ratings: {
      fields: {
        omit: ['user'],
      },
      relations: {
        session: {
          fields: {
            pick: ['id'],
          },
          relations: {
            user: {
              fields: {
                pick: ['username'],
              },
            },
          },
        },
      },
    },
    position: {
      fields: {
        pick: ['coordinate'],
      },
    },
    listing_files: {
      fields: {
        pick: ['id', 'image_url'],
      },
    },
    activities: {
      fields: {
        pick: ['id', 'name', 'slug', 'image_url'],
      },
    },
    amenities: {
      fields: {
        pick: ['id', 'name', 'slug', 'font_icon_name'],
      },
    },
  },
}

export default class ListingsController {
  // C
  public async suggestListing({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'suggestListing')
    const user = await auth.authenticate()
    try {
      const validationSchema = schema.create({
        // common attributes
        listing_type: schema.enum(Object.values(ListingType)), // schema.string([rules.exists({ column: 'slug', table: 'listing_types' })]),
        region: schema.string.optional([rules.exists({ column: 'slug', table: 'regions' })]),
        full_address: schema.string(varcharRule),
        longitude: schema.number([rules.requiredIfExists('latitude'), rules.range(-180, 180)]),
        latitude: schema.number([rules.requiredIfExists('longitude'), rules.range(-90, 90)]),
        note: schema.string.optional(varcharRule), // submission reason
        // optionals
        name: schema.string.optional(varcharRule),
        address_name: schema.string.optional(varcharRule),
        description: schema.string.optional(varcharRule),
        main_image_file: schema.file.optional(imageFileSpec, [
          rules.requiredIfNotExists('main_image_url'),
        ]),
        main_image_url: schema.string.optional([
          rules.maxLength(255),
          rules.requiredIfNotExists('main_image_file'),
        ]),
        sub_image_files: schema.array.optional().members(schema.file(imageFileSpec)),
        sub_image_urls: schema.array.optional().members(schema.string(varcharRule)),
        keywords: schema.array.optional([rules.minLength(1)]).members(schema.string()),
        // pod suggestions

        // nursing facility suggestions
        amenities: schema.array
          .optional([
            rules.requiredWhen('listing_type', '=', 'care'),
            rules.maxLength((await Amenity.all()).length),
            rules.distinct('*'),
          ])
          .members(schema.string([rules.exists({ table: 'amenities', column: 'slug' })])),
        company_name: schema.string.optional([...varcharRule]),
        postal_code: schema.string.optional([...varcharRule]),
        contact_number: schema.string.optional([rules.minLength(2), rules.maxLength(18)]),
        country_dial_code: schema.string.optional([rules.minLength(1), rules.maxLength(7)]),
        opening_hours: schema.string.optional([...varcharRule]),
        // NOTE: user suggestion will not pass in these
        // diaper_changing_mat_type: schema.string.optional(varcharRule),
        // usage_durations: schema.array.optional([rules.minLength(1)]).members(schema.number()),
        // usage_extension_durations: schema.array
        //   .optional([rules.minLength(1)])
        //   .members(schema.number()),
        // max_number_of_usage_extensions: schema.number.optional(),
        // number_of_private_feeding_rooms: schema.number.optional(),
        // number_of_diaper_changing_mats: schema.number.optional(),
        // door_is_lockable: schema.boolean.optional(),
      })

      const {
        main_image_file,
        main_image_url,
        sub_image_files,
        sub_image_urls,
        longitude,
        latitude,
        listing_type,
        region,
        amenities,
        ...validationData
      } = await request.validate({
        schema: validationSchema,
      })

      // const findListingType = await ListingType.findByOrFail('slug', listing_type)
      let findRegion: Region | null
      if (region) {
        findRegion = await Region.findBy('slug', region)
      }

      const existingPosition = await Position.query()
        .whereRaw(`ST_Equals(coordinate, POINT(?, ?))`, [longitude, latitude])
        .first()

      const newCreatedListing = await Database.transaction(async (trx) => {
        const geohash = ngeohash.encode(longitude, latitude, 20)
        let positionId: string | undefined

        if (existingPosition) {
          positionId = existingPosition.id
        } else {
          // Position not exist create new position
          const newPositionId = uuidv4()
          const result = await Database.rawQuery(
            'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
            [newPositionId, geohash, longitude, latitude]
          )

          if (result[0].affectedRows > 0) {
            const newPosition = await Position.findOrFail(newPositionId)
            positionId = newPosition.id
          } else {
            positionId = undefined
          }
        }

        const newListing = await Listing.create(
          {
            suggestedBy: user.id,
            positionId: positionId,
            regionId: findRegion?.id,
            listingType: listing_type,
            // typeId: findListingType.id,
            ...validationData,
          },
          { client: trx }
        )

        // Create Listing Amenity
        if (amenities) {
          amenities.forEach(async (amenitySlug) => {
            const amenity = await Amenity.findByOrFail('slug', amenitySlug)
            await newListing.related('amenities').attach({
              [amenity.id as string]: {
                id: uuidv4(),
                listing_id: newListing.id,
                is_hidden: newListing.isHidden,
              },
            })
          })
        }

        await newImageHandler(
          user,
          newListing,
          trx,
          false,
          main_image_file,
          main_image_url,
          sub_image_files,
          sub_image_urls
        )

        return newListing
      })

      const listing = await Listing.query()
        .where('id', newCreatedListing.id)
        .preload('listingFiles')
        .preload('activities')
        .preload('amenities')
        .first()

      Event.emit('update:geojson', {})

      Event.emit('sync:meilisearch', { type: 'update', listingId: newCreatedListing!.id }) //Sync Meili with latest update

      return response.ok({
        success: listing?.id ? true : false,
        data: listing?.serialize(normalUserOmitFields),
      })
    } catch (error) {
      Logger.error(error, 'suggestListing')
      return response.badRequest(error)
    }
  }

  // R
  public async findListings({ request, response }: HttpContextContract) {
    Logger.info(request.all(), 'findListings')
    try {
      const page = request.input('page', 1)
      let sort = request.input('sort', 'name:asc')
      const limit = request.input('limit', 10)
      let amenities = request.input('amenities')

      if (amenities != null && !Array.isArray(amenities)) {
        // Purpose is to convert string to String[]
        const temp = amenities
        amenities = [temp]
      }

      const listingsFromMeili = await MeilisearchService.admin.searchListingsIndex(
        amenities,
        sort,
        parseInt(page),
        parseInt(limit),
        false
      )

      if (!Array.isArray(listingsFromMeili.data.hits)) {
        return response.badRequest({
          success: false,
          message: 'Listing data returned from search is not an array',
        })
      }

      const listings = await Listing.fetchWithLocation(
        Listing.query()
          .whereIn(
            'id',
            listingsFromMeili.data.hits.map((listing) => listing.id)
          )
          .preload('listingFiles', (query) => {
            query.where('listing_files.is_hidden', false).orderBy('is_main', 'desc')
          })
          .withAggregate('listingRatings', (query) => {
            query.avg('experience_rating').as('average_experience_ratings')
          })
          .orderByRaw(
            `FIELD(id, ${listingsFromMeili.data.hits.map((listing) => `'${listing.id}'`)})`
          )
          .paginate(1, limit), // always page 1 because listing ids are from Meilisearch
        listingsFromMeili
      )

      //--------------------------delete adonis's ORM paginate metadata, create custom meilisearch paginate metadata.--------------------------
      const customMeiliPaginateMeta = {
        total: listingsFromMeili.data.totalHits,
        per_page: listingsFromMeili.data.hitsPerPage,
        current_page: listingsFromMeili.data.page,
        last_page: listingsFromMeili.data.totalPages,
        first_page: 1,
        first_page_url: '/?page=1',
        last_page_url: `/?page=${listingsFromMeili.data.totalPages}`,
        next_page_url: `/?page=${listingsFromMeili.data.page + 1}`,
        previous_page_url:
          listingsFromMeili.data.page <= 1 ? null : `/?page=${listingsFromMeili.data.page - 1}`,
      }
      const listingsWithMeta = listings.serialize(fetchFields)
      delete listingsWithMeta['meta']
      //--------------------------delete adonis's ORM paginate metadata, create custom meilisearch paginate metadata.--------------------------

      return response
        .status(200)
        .send({ meta: customMeiliPaginateMeta, data: [...listingsWithMeta['data']] })
    } catch (error) {
      Logger.error(error, 'findListings')
      return response.badRequest(error)
    }
  }

  public async findListingsGeoJson({ request, response }: HttpContextContract) {
    Logger.info(request.all(), 'findListingsGeoJson')

    try {
      // Construct the path to the GeoJSON file in the public directory
      const filePath = join(Application.appRoot, 'data/geojson.json')

      // Read the file
      const fileContent = await readFile(filePath, 'utf8')

      // Parse the JSON content
      const geoJsonResponse = JSON.parse(fileContent)

      // Send the response
      return response.status(200).json(geoJsonResponse)
    } catch (error) {
      Logger.error(error, 'findListingsGeoJson')
      return response.badRequest(error)
    }
  }

  public async findListing({ params: { id }, request, response }: HttpContextContract) {
    Logger.info(request.all(), 'findListing')
    const lat = new BigNumber(request.input('lat', 999)).toNumber()
    const lon = new BigNumber(request.input('lon', 999)).toNumber()

    try {
      const listing = await Listing.query()
        .select('listings.*')
        .where('listings.id', id)
        .andWhere('listings.is_hidden', false)
        .andWhere('is_verified', true)
        .preload('listingFiles', (query) => {
          query.where('listing_files.is_hidden', false).orderBy('is_main', 'desc')
        })
        .preload('activities', (query) => {
          query.where('activities.is_hidden', false)
        })
        .preload('amenities', (query) => {
          query.where('amenities.is_hidden', false)
        })
        .preload('listingRatings', (query) => {
          query
            .preload('session', (query) => query.preload('user'))
            .where('listing_ratings.is_hidden', false)
            .orderBy('created_at', 'desc')
            .limit(3)
        })
        .withAggregate('listingRatings', (query) => {
          query.avg('experience_rating').as('average_experience_ratings')
        })
        .withAggregate('listingRatings', (query) => {
          query.count('*').as('total_experience_ratings')
        })
        .withAggregate('sessions', (query) => {
          query.count('*').as('total_sessions')
        })
        .if(lon != 999 && lat != 999, (query) =>
          query
            .select(
              Database.raw(`ST_Distance_Sphere(positions.coordinate, Point(?,?)) as distance`, [
                lon,
                lat,
              ])
            )
            .innerJoin('positions', 'listings.position_id', 'positions.id')
        )
        .first()

      if (!listing) {
        return response.notFound({ success: false, message: 'Listing not found' })
      }

      return response.status(200).send({
        data: listing.serialize(findFields),
      })
    } catch (error) {
      Logger.error(error, 'findListing')
      return response.badRequest(error)
    }
  }

  public async findListingsByPosition({ request, response }: HttpContextContract) {
    Logger.info(request.all(), 'findListingsByPosition')

    try {
      // convert to radians
      const lat = new BigNumber(request.input('lat', 0)).toNumber()
      const lon = new BigNumber(request.input('lon', 0)).toNumber()
      const r = new BigNumber(request.input('r', 0)).toNumber() * 1000 // within metres

      const deviceLat = new BigNumber(request.input('device_lat', 0)).toNumber()
      const deviceLon = new BigNumber(request.input('device_lon', 0)).toNumber()

      let amenities = request.input('amenities', [])
      const page = request.input('page', 1)
      const sort = request.input('sort', 'distance:asc')
      const limit = request.input('limit', 10)
      const type = request.input('types', 'all')

      if (amenities != null && !Array.isArray(amenities)) {
        // Purpose is to convert string to String[]
        const temp = amenities
        amenities = [temp]
      }

      const listingsFromMeili = await MeilisearchService.admin.searchListingsIndexWithPosition(
        lat,
        lon,
        r,
        type,
        amenities,
        sort,
        parseInt(page),
        parseInt(limit),
        false,
        deviceLat,
        deviceLon
      )

      if (!Array.isArray(listingsFromMeili.data.hits)) {
        return response.badRequest({
          success: false,
          message: 'Listing data returned from search is not an array',
        })
      }

      // TODO: can be enhance by more specific location like which countries, regions in order to narrow down/speed up the query
      const withinRadiusListings = await Listing.fetchWithLocation(
        Listing.query()
          .whereIn(
            'id',
            listingsFromMeili.data.hits.map((listing) => listing.id)
          )
          .preload('listingFiles', (query) => {
            query.where('listing_files.is_hidden', false).orderBy('is_main', 'desc')
          })
          .withAggregate('listingRatings', (query) => {
            query.avg('experience_rating').as('average_experience_ratings')
          })
          .withAggregate('listingRatings', (query) => {
            query.count('*').as('total_experience_ratings')
          })
          .withAggregate('sessions', (query) => {
            query.count('*').as('total_sessions')
          })
          .orderByRaw(
            `FIELD(id, ${listingsFromMeili.data.hits.map((listing) => `'${listing.id}'`)})`
          )
          .paginate(1, limit), // always page 1 because listing ids are from Meilisearch
        listingsFromMeili
      )

      //--------------------------delete adonis's ORM paginate metadata, create custom meilisearch paginate metadata.--------------------------
      const customMeiliPaginateMeta = {
        total: listingsFromMeili.data.totalHits,
        per_page: listingsFromMeili.data.hitsPerPage,
        current_page: listingsFromMeili.data.page,
        last_page: listingsFromMeili.data.totalPages,
        first_page: 1,
        first_page_url: '/?page=1',
        last_page_url: `/?page=${listingsFromMeili.data.totalPages}`,
        next_page_url: `/?page=${listingsFromMeili.data.page + 1}`,
        previous_page_url:
          listingsFromMeili.data.page <= 1 ? null : `/?page=${listingsFromMeili.data.page - 1}`,
      }
      const listingsWithMeta = withinRadiusListings.serialize(fetchFields)
      delete listingsWithMeta['meta']
      //--------------------------delete adonis's ORM paginate metadata, create custom meilisearch paginate metadata.--------------------------

      return response
        .status(200)
        .send({ meta: customMeiliPaginateMeta, data: [...listingsWithMeta['data']] })
    } catch (error) {
      Logger.error(error, 'findListingsByPosition')
      return response.badRequest(error)
    }
  }

  // TODO: use meilisearch for indexing
  public async findListingsByKeywords({ request, response }: HttpContextContract) {
    Logger.info(request.all(), 'findListingsByKeywords')
    try {
      const page = request.input('page', 1)
      let sort = request.input('sort', 'distance:asc')
      const limit = request.input('limit', 10)
      let keywords = request.input('keywords')
      let amenities = request.input('amenities')

      if (amenities != null && !Array.isArray(amenities)) {
        // Purpose is to convert string to String[]
        const temp = amenities
        amenities = [temp]
      }
      /// use for ordering
      const lat = new BigNumber(request.input('lat', 0)).toNumber()
      const lon = new BigNumber(request.input('lon', 0)).toNumber()
      const deviceLat = new BigNumber(request.input('device_lat', lat)).toNumber()
      const deviceLon = new BigNumber(request.input('device_lon', lon)).toNumber()

      const listingsFromMeili = await MeilisearchService.admin.searchListingsIndexWithKeywords(
        keywords,
        amenities,
        sort,
        parseInt(page),
        parseInt(limit),
        false,
        lat,
        lon,
        deviceLat,
        deviceLon
      )

      if (!Array.isArray(listingsFromMeili.data.hits)) {
        return response.badRequest({
          success: false,
          message: 'Listing data returned from search is not an array',
        })
      }

      const listings = await Listing.fetchWithLocation(
        Listing.query()
          .whereIn(
            'id',
            listingsFromMeili.data.hits.map((listing) => listing.id)
          )
          .preload('listingFiles', (query) => {
            query.where('listing_files.is_hidden', false).orderBy('is_main', 'desc')
          })
          .withAggregate('listingRatings', (query) => {
            query.avg('experience_rating').as('average_experience_ratings')
          })
          .orderByRaw(
            `FIELD(id, ${listingsFromMeili.data.hits.map((listing) => `'${listing.id}'`)})`
          )
          .paginate(1, limit), // always page 1 because listing ids are from Meilisearch
        listingsFromMeili
      )

      //--------------------------delete adonis's ORM paginate metadata, create custom meilisearch paginate metadata.--------------------------
      const customMeiliPaginateMeta = {
        total: listingsFromMeili.data.totalHits,
        per_page: listingsFromMeili.data.hitsPerPage,
        current_page: listingsFromMeili.data.page,
        last_page: listingsFromMeili.data.totalPages,
        first_page: 1,
        first_page_url: '/?page=1',
        last_page_url: `/?page=${listingsFromMeili.data.totalPages}`,
        next_page_url: `/?page=${listingsFromMeili.data.page + 1}`,
        previous_page_url:
          listingsFromMeili.data.page <= 1 ? null : `/?page=${listingsFromMeili.data.page - 1}`,
      }
      const listingsWithMeta = listings.serialize(fetchFields)
      delete listingsWithMeta['meta']
      //--------------------------delete adonis's ORM paginate metadata, create custom meilisearch paginate metadata.--------------------------

      return response
        .status(200)
        .send({ meta: customMeiliPaginateMeta, data: [...listingsWithMeta['data']] })
    } catch (error) {
      Logger.error(error, 'findListingsByKeywords')
      return response.badRequest(error)
    }
  }

  // public async findListingByType({
  //   params: { listing_type_id },
  //   request,
  //   response,
  // }: HttpContextContract) {
  //   try {
  //     const gomama_type = await ListingType.findByOrFail('name', 'GO!MAMA')
  //     const listing_type = await ListingType.findOrFail(listing_type_id)
  //     let listings: ModelPaginatorContract<Listing>
  //     const page = request.input('page', 1)
  //     const sort = request.input('sort', 'created_at:desc').split(':')
  //     const limit = request.input('limit', 10)

  //     if (listing_type_id == gomama_type.id) {
  //       listings = await Listing.query()
  //         .where('type_id', gomama_type.id)
  //         .orWhere('type_id', listing_type.id)
  //         .andWhere('is_hidden', false)
  //         .andWhere('is_verified', true)
  //         .preload('listingFiles', (query) => {
  //           query.where('is_hidden', false)
  //         })
  //         .preload('activities', (query) => {
  //           query.where('activities.is_hidden', false)
  //         })
  //         .preload('amenities', (query) => {
  //           query.where('amenities.is_hidden', false)
  //         })
  //         .orderBy(sort[0], sort[1])
  //         .paginate(page, limit)
  //     } else {
  //       listings = await Listing.query()
  //         .whereIn('type_id', [listing_type.id, gomama_type.id])
  //         .andWhere('is_hidden', false)
  //         .andWhere('is_verified', true)
  //         .preload('listingFiles', (query) => {
  //           query.where('is_hidden', false)
  //         })
  //         .preload('activities', (query) => {
  //           query.where('activities.is_hidden', false)
  //         })
  //         .preload('amenities', (query) => {
  //           query.where('amenities.is_hidden', false)
  //         })
  //         .orderBy(sort[0], sort[1])
  //         .paginate(page, limit)
  //     }

  //     return response.status(200).send(listings.serialize(normalUserOmitFields))
  //   } catch (error) {
  //     Logger.error(error, 'findListing')
  //     return response.badRequest(error)
  //   }
  // }

  public async findSuggestedListings({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'findSuggestedListings')
    const user = await auth.authenticate()
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 10)

      // User self suggested listing, so no need to filter is_hidden for image file, activities, amenities
      const listings = await Listing.query()
        .where('suggested_by', user.id)
        .preload('listingFiles')
        // .preload('activities')
        .preload('amenities')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(listings.serialize(fetchFields))
    } catch (error) {
      Logger.error(error, 'findSuggestedListings')
      return response.badRequest(error)
    }
  }

  // U
  public async uploadListingImage({
    params: { id },
    request,
    response,
    auth,
  }: HttpContextContract) {
    Logger.info(request.all(), 'uploadListingImage')
    const user = await auth.authenticate()
    try {
      const findListing = await Listing.query().where('id', id).first()

      if (!findListing) {
        return response.notFound({ success: false, message: 'Listing not found' })
      }

      const validationSchema = schema.create({
        sub_image_file: schema.file({
          size: '20mb',
          extnames: ['png', 'jpg', 'jpeg'],
        }),
      })

      // TODO: Remove type_name, region_name in prod, id will be included in validationData
      const { sub_image_file } = await request.validate({
        schema: validationSchema,
      })

      // Create new sub image
      const remoteName = `${findListing.id}_${generateRandomCode(10)}.${sub_image_file.extname}`
      await sub_image_file.moveToDisk(remotePathListing, { name: remoteName }, disk_name)

      await ListingFile.create({
        listingId: findListing.id,
        isApproved: false, // User submitted image should always be reviewed by admin
        isHidden: true, // User submitted image should always be reviewed by admin
        isMain: false,
        uploadedBy: user.id,
        imageUrl: `${remoteName}`,
      })

      const updatedListing = await Listing.query()
        .where('id', findListing.id)
        .preload('listingFiles')
        .preload('activities')
        .preload('amenities')
        .first()

      return response.ok({
        success: true,
        message: 'Successfully uploaded listing image',
        data: updatedListing,
      })
    } catch (error) {
      Logger.error(error, 'uploadListingImage')
      return response.badRequest(error)
    }
  }

  //===================================================
  //    _____   ____                     _ _
  //   /  _  \ |  __ \  ||\\        //||  |  ||\\    ||
  //   | |_| | | |  | | || \\      // ||  |  || \\   ||
  //   | ___ | | |  | | ||  \\    //  ||  |  ||  \\  ||
  //   | | | | | |__| | ||   \\  //   ||  |  ||   \\ ||
  //   |_| |_| |____ /  ||    \\//    || _|_ ||    \\||
  //===================================================

  // C
  public async quickSuggestListingAdmin({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'quickSuggestListingAdmin')

    // Admin created listing is automatically verified, and all image are approved.
    const user = await auth.authenticate()

    try {
      const validationSchema = schema.create({
        suggested_by: schema.string.optional({ trim: true }, [
          rules.exists({ column: 'id', table: 'users' }),
        ]),
        listing_type: schema.enum(Object.values(ListingType)),
        name: schema.string(varcharRule),
        firestore_id: schema.string.optional(
          varcharRule.concat([rules.requiredWhen('listing_type', '=', ListingType.gomama)])
        ),
        longitude: schema.number([rules.requiredIfExists('latitude'), rules.range(-180, 180)]),
        latitude: schema.number([rules.requiredIfExists('longitude'), rules.range(-90, 90)]),
      })

      const { longitude, latitude, suggested_by, ...validationData } = await request.validate({
        schema: validationSchema,
      })

      const existingPosition = await Position.query()
        .whereRaw(`ST_Equals(coordinate, POINT(?, ?))`, [longitude, latitude])
        .first()

      const geohash = ngeohash.encode(longitude, latitude, 20)
      let positionId: string | undefined

      if (existingPosition) {
        positionId = existingPosition.id
      } else {
        // Position not exist create new position
        const newPositionId = uuidv4()
        const result = await Database.rawQuery(
          'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
          [newPositionId, geohash, longitude, latitude]
        )

        if (result[0].affectedRows > 0) {
          const newPosition = await Position.findOrFail(newPositionId)
          positionId = newPosition.id
        } else {
          positionId = undefined
        }
      }

      const newListing = await Listing.create({
        suggestedBy: suggested_by ? suggested_by : user.id,
        positionId: positionId,
        verifiedBy: user.id,
        isVerified: false,
        isHidden: true,
        ...validationData,
      })

      Event.emit('update:geojson', {})

      Event.emit('sync:meilisearch', { type: 'update', listingId: newListing!.id }) //Sync Meili with latest update

      const listing = await Listing.query()
        .with('listing_files', (builder) => {
          builder.where('is_hidden', false)
        })
        .where('id', newListing.id)
        .preload('listingFiles')
        .preload('activities')
        .preload('amenities')
        .first()

      return response.ok({
        success: listing?.id ? true : false,
        data: listing?.serialize(normalUserOmitFields),
      })
    } catch (error) {
      Logger.error(error, 'suggestListingAdmin')
      return response.badRequest(error)
    }
  }

  public async suggestListingAdmin({ request, response, auth }: HttpContextContract) {
    // Admin created listing is automatically verified, and all image are approved.
    const user = await auth.authenticate()
    try {
      const validationSchema = schema.create({
        suggested_by: schema.string.optional({ trim: true }, [
          rules.exists({ column: 'id', table: 'users' }),
        ]),
        listing_type: schema.enum(Object.values(ListingType)),
        region: schema.string([rules.exists({ column: 'slug', table: 'regions' })]),
        name: schema.string(varcharRule),
        company_name: schema.string(varcharRule),
        address_name: schema.string(varcharRule),
        description: schema.string(varcharRule),
        full_address: schema.string(varcharRule),
        longitude: schema.number([rules.requiredIfExists('latitude'), rules.range(-180, 180)]),
        latitude: schema.number([rules.requiredIfExists('longitude'), rules.range(-90, 90)]),
        contact_number: schema.string.optional([rules.minLength(2), rules.maxLength(18)]),
        country_dial_code: schema.string([rules.minLength(1), rules.maxLength(7)]),
        keywords: schema.array([rules.minLength(1)]).members(schema.string()),
        postal_code: schema.string(varcharRule),
        diaper_changing_mat_type: schema.string.optional(varcharRule),
        opening_hours: schema.string.optional(varcharRule),
        usage_durations: schema.array.optional([rules.minLength(1)]).members(schema.number()),
        usage_extension_durations: schema.array
          .optional([rules.minLength(1)])
          .members(schema.number()),
        max_number_of_usage_extensions: schema.number(),
        number_of_private_feeding_rooms: schema.number(),
        number_of_diaper_changing_mats: schema.number(),
        main_image_file: schema.file.optional(imageFileSpec, [
          rules.requiredIfNotExists('main_image_url'),
        ]),
        main_image_url: schema.string.optional([
          rules.maxLength(255),
          rules.requiredIfNotExists('main_image_file'),
        ]),
        sub_image_files: schema.array.optional().members(schema.file(imageFileSpec)),
        sub_image_urls: schema.array.optional().members(schema.string(varcharRule)),
        activities: schema.array
          .optional([rules.maxLength((await Activity.all()).length), rules.distinct('*')])
          .members(schema.string([rules.exists({ table: 'activities', column: 'slug' })])),
        amenities: schema.array
          .optional([rules.maxLength((await Amenity.all()).length), rules.distinct('*')])
          .members(schema.string([rules.exists({ table: 'amenities', column: 'slug' })])),
        note: schema.string.optional(varcharRule),
        api_key: schema.string.optional(varcharRule),
        pi_id: schema.string.optional(varcharRule),
        pi_last_updated: schema.date.optional(),
        lock_id: schema.string.optional(varcharRule),
        lock_master_pin: schema.string.optional(
          varcharRule.concat([rules.requiredIfExists('lock_id')])
        ),
        lock_bluetooth_admin_key: schema.string.optional(
          varcharRule.concat([rules.requiredIfExists('lock_id')])
        ),
        firestore_id: schema.string.optional(
          varcharRule.concat([rules.requiredWhen('listing_type', '=', ListingType.gomama)])
        ),
      })

      const {
        main_image_file,
        main_image_url,
        sub_image_files,
        sub_image_urls,
        longitude,
        latitude,
        region,
        suggested_by,
        activities,
        amenities,
        ...validationData
      } = await request.validate({
        schema: validationSchema,
      })

      const existingPosition = await Position.query()
        .whereRaw(`ST_Equals(coordinate, POINT(?, ?))`, [longitude, latitude])
        .first()

      const listingRegion = await Region.findBy('slug', region)
      const geohash = ngeohash.encode(longitude, latitude, 20)
      let positionId: string | undefined

      if (existingPosition) {
        positionId = existingPosition.id
      } else {
        // Position not exist create new position
        const newPositionId = uuidv4()
        const result = await Database.rawQuery(
          'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
          [newPositionId, geohash, longitude, latitude]
        )

        if (result[0].affectedRows > 0) {
          const newPosition = await Position.findOrFail(newPositionId)
          positionId = newPosition.id
        } else {
          positionId = undefined
        }
      }

      const newListing = await Database.transaction(async (trx) => {
        const newListing = await Listing.create(
          {
            suggestedBy: suggested_by ? suggested_by : user.id,
            positionId: positionId,
            verifiedBy: user.id,
            isVerified: true,
            isHidden: false,
            regionId: listingRegion?.id,
            ...validationData,
          },
          { client: trx }
        )

        // Create Listing Activity
        if (activities) {
          await Promise.all(
            activities.map(async (activitySlug) => {
              const activity = await Activity.findByOrFail('slug', activitySlug)
              await newListing
                .useTransaction(trx)
                .related('activities')
                .attach({
                  [activity.id as string]: {
                    id: uuidv4(),
                    activity_id: activity.id,
                    is_hidden: newListing.isHidden,
                  },
                })
            })
          )
        }

        // Create Listing Amenity
        if (amenities) {
          await Promise.all(
            amenities.map(async (amenitySlug) => {
              const amenity = await Amenity.findByOrFail('slug', amenitySlug)
              await newListing
                .useTransaction(trx)
                .related('amenities')
                .attach({
                  [amenity.id as string]: {
                    id: uuidv4(),
                    amenity_id: amenity.id,
                    is_hidden: newListing.isHidden,
                  },
                })
            })
          )
        }

        await newImageHandler(
          user,
          newListing,
          trx,
          true,
          main_image_file,
          main_image_url,
          sub_image_files,
          sub_image_urls
        )

        return newListing
      })

      Event.emit('update:geojson', {})

      Event.emit('sync:meilisearch', { type: 'update', listingId: newListing!.id }) //Sync Meili with latest update

      const listing = await Listing.query()
        .with('listing_files', (builder) => {
          builder.where('is_hidden', false)
        })
        .where('id', newListing.id)
        .preload('listingFiles')
        .preload('activities')
        .preload('amenities')
        .first()

      return response.ok({
        success: listing?.id ? true : false,
        data: listing?.serialize(normalUserOmitFields),
      })
    } catch (error) {
      Logger.error(error, 'suggestListingAdmin')
      return response.badRequest(error)
    }
  }

  // R
  public async findListingsAdmin({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'name:asc')
      const limit = request.input('limit', 10)
      let amenities = request.input('amenities')
      const filters = _.omit(request.all(), ['page', 'sort', 'limit', 'is_hidden', 'is_verified'])

      if (amenities != null && !Array.isArray(amenities)) {
        // Purpose is to convert string to String[]
        const temp = amenities
        amenities = [temp]
      }

      const isHidden = request.input('is_hidden')
      const isVerified = request.input('is_verified')
      const listingsFromMeili = await MeilisearchService.admin.searchListingsIndex(
        amenities,
        sort,
        parseInt(page),
        parseInt(limit),
        true,
        filters,
        isHidden != undefined ? Boolean(isHidden === 'true') : undefined,
        isVerified != undefined ? Boolean(isVerified === 'true') : undefined
      )

      if (!Array.isArray(listingsFromMeili.data.hits)) {
        return response.badRequest({
          success: false,
          message: 'Listing data returned from search is not an array',
        })
      }

      const listings = await Listing.query()
        .whereIn(
          'id',
          listingsFromMeili.data.hits.map((listing) => listing.id)
        )
        .preload('listingFiles', (query) => {
          query.orderBy('is_main', 'desc')
        })
        .withAggregate('listingRatings', (query) => {
          query.avg('experience_rating').as('average_experience_ratings')
        })
        .orderByRaw(`FIELD(id, ${listingsFromMeili.data.hits.map((listing) => `'${listing.id}'`)})`)
        .paginate(1, limit) // always page 1 because listing ids are from Meilisearch

      //--------------------------delete adonis's ORM paginate metadata, create custom meilisearch paginate metadata.--------------------------
      const customMeiliPaginateMeta = {
        total: listingsFromMeili.data.totalHits,
        per_page: listingsFromMeili.data.hitsPerPage,
        current_page: listingsFromMeili.data.page,
        last_page: listingsFromMeili.data.totalPages,
        first_page: 1,
        first_page_url: '/?page=1',
        last_page_url: `/?page=${listingsFromMeili.data.totalPages}`,
        next_page_url: `/?page=${listingsFromMeili.data.page + 1}`,
        previous_page_url:
          listingsFromMeili.data.page <= 1 ? null : `/?page=${listingsFromMeili.data.page - 1}`,
      }
      const listingsWithMeta = listings.serialize(fetchFields)
      delete listingsWithMeta['meta']
      //--------------------------delete adonis's ORM paginate metadata, create custom meilisearch paginate metadata.--------------------------

      return response
        .status(200)
        .send({ meta: customMeiliPaginateMeta, data: [...listingsWithMeta['data']] })
    } catch (error) {
      Logger.error(error, 'findListingsByKeywordsAdmin')
      return response.badRequest(error)
    }
  }

  public async findListingAdmin({ params: { id }, response }: HttpContextContract) {
    try {
      const listing = await Listing.query()
        .select('listings.*')
        .where('listings.id', id)
        .preload('listingFiles')
        .preload('activities')
        .preload('amenities')
        .preload('suggestUser')
        .preload('verifyAdmin')
        .preload('sessions', (query) => {
          query.orderBy('actual_ended_at', 'desc').preload('user').limit(1)
        })
        .preload('listingRatings', (query) => {
          query
            .preload('session', (query) => query.preload('user'))
            .orderBy('created_at', 'desc')
            .limit(3)
        })
        .withAggregate('listingRatings', (query) => {
          query.avg('experience_rating').as('average_experience_ratings')
        })
        .withAggregate('listingRatings', (query) => {
          query.count('*').as('total_experience_ratings')
        })
        .withAggregate('sessions', (query) => {
          query.count('*').as('total_sessions')
        })
        .first()

      if (!listing) return response.notFound({ success: false, message: 'Listing not found' })

      return response.status(200).send({ data: listing })
    } catch (error) {
      Logger.error(error, 'findListingAdmin')
      return response.badRequest(error)
    }
  }

  public async findListingsByPositionAdmin({ request, response }: HttpContextContract) {
    Logger.info(request.all(), 'findListingsByPositionAdmin')
    try {
      // convert to radians
      const lat = new BigNumber(request.input('lat', 0)).toNumber()
      const lon = new BigNumber(request.input('lon', 0)).toNumber()
      const r = new BigNumber(request.input('r', 0)).toNumber() * 1000 // within metres
      let amenities = request.input('amenities', [])
      const page = request.input('page', 1)
      const sort = request.input('sort', 'distance:asc')
      const limit = request.input('limit', 10)
      const type = request.input('types', 'all')

      if (amenities != null && !Array.isArray(amenities)) {
        // Purpose is to convert string to String[]
        const temp = amenities
        amenities = [temp]
      }

      const listingsFromMeili = await MeilisearchService.admin.searchListingsIndexWithPosition(
        lat,
        lon,
        r,
        type,
        amenities,
        sort,
        parseInt(page),
        parseInt(limit),
        true,
        request.input('device_lat', 0),
        request.input('device_lon', 0)
      )

      if (!Array.isArray(listingsFromMeili.data.hits)) {
        return response.badRequest({
          success: false,
          message: 'Listing data returned from search is not an array',
        })
      }

      // TODO: can be enhance by more specific location like which countries, regions in order to narrow down/speed up the query
      const withinRadiusListings = await Listing.fetchWithLocation(
        Listing.query()
          .whereIn(
            'id',
            listingsFromMeili.data.hits.map((listing) => listing.id)
          )
          .preload('listingFiles', (query) => {
            query.orderBy('is_main', 'desc')
          })
          .withAggregate('listingRatings', (query) => {
            query.avg('experience_rating').as('average_experience_ratings')
          })
          .withAggregate('listingRatings', (query) => {
            query.count('*').as('total_experience_ratings')
          })
          .withAggregate('sessions', (query) => {
            query.count('*').as('total_sessions')
          })
          .orderByRaw(
            `FIELD(id, ${listingsFromMeili.data.hits.map((listing) => `'${listing.id}'`)})`
          )
          .paginate(1, limit), // always page 1 because listing ids are from Meilisearch
        listingsFromMeili
      )

      //--------------------------delete adonis's ORM paginate metadata, create custom meilisearch paginate metadata.--------------------------
      const customMeiliPaginateMeta = {
        total: listingsFromMeili.data.totalHits,
        per_page: listingsFromMeili.data.hitsPerPage,
        current_page: listingsFromMeili.data.page,
        last_page: listingsFromMeili.data.totalPages,
        first_page: 1,
        first_page_url: '/?page=1',
        last_page_url: `/?page=${listingsFromMeili.data.totalPages}`,
        next_page_url: `/?page=${listingsFromMeili.data.page + 1}`,
        previous_page_url:
          listingsFromMeili.data.page <= 1 ? null : `/?page=${listingsFromMeili.data.page - 1}`,
      }
      const listingsWithMeta = withinRadiusListings.serialize(fetchFields)
      delete listingsWithMeta['meta']
      //--------------------------delete adonis's ORM paginate metadata, create custom meilisearch paginate metadata.--------------------------

      return response
        .status(200)
        .send({ meta: customMeiliPaginateMeta, data: [...listingsWithMeta['data']] })
    } catch (error) {
      Logger.error(error, 'findListingsByPositionAdmin')
      return response.badRequest(error)
    }
  }

  public async findListingsByKeywordsAdmin({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc')
      const limit = request.input('limit', 10)
      let keywords = request.input('keywords')
      let amenities = request.input('amenities')
      const lat = new BigNumber(request.input('lat', 0)).toNumber()
      const lon = new BigNumber(request.input('lon', 0)).toNumber()

      if (amenities != null && !Array.isArray(amenities)) {
        // Purpose is to convert string to String[]
        const temp = amenities
        amenities = [temp]
      }

      const listingsFromMeili = await MeilisearchService.admin.searchListingsIndexWithKeywords(
        keywords,
        amenities,
        sort,
        parseInt(page),
        parseInt(limit),
        true,
        lat,
        lon
      )

      if (!Array.isArray(listingsFromMeili.data.hits)) {
        return response.badRequest({
          success: false,
          message: 'Listing data returned from search is not an array',
        })
      }

      const listings = await Listing.query()
        .whereIn(
          'id',
          listingsFromMeili.data.hits.map((listing) => listing.id)
        )
        .preload('listingFiles', (query) => {
          query.orderBy('is_main', 'desc')
        })
        .withAggregate('listingRatings', (query) => {
          query.avg('experience_rating').as('average_experience_ratings')
        })
        .orderByRaw(`FIELD(id, ${listingsFromMeili.data.hits.map((listing) => `'${listing.id}'`)})`)
        .paginate(1, limit) // always page 1 because listing ids are from Meilisearch

      //--------------------------delete adonis's ORM paginate metadata, create custom meilisearch paginate metadata.--------------------------
      const customMeiliPaginateMeta = {
        total: listingsFromMeili.data.totalHits,
        per_page: listingsFromMeili.data.hitsPerPage,
        current_page: listingsFromMeili.data.page,
        last_page: listingsFromMeili.data.totalPages,
        first_page: 1,
        first_page_url: '/?page=1',
        last_page_url: `/?page=${listingsFromMeili.data.totalPages}`,
        next_page_url: `/?page=${listingsFromMeili.data.page + 1}`,
        previous_page_url:
          listingsFromMeili.data.page <= 1 ? null : `/?page=${listingsFromMeili.data.page - 1}`,
      }
      const listingsWithMeta = listings.serialize(fetchFields)
      delete listingsWithMeta['meta']
      //--------------------------delete adonis's ORM paginate metadata, create custom meilisearch paginate metadata.--------------------------

      return response
        .status(200)
        .send({ meta: customMeiliPaginateMeta, data: [...listingsWithMeta['data']] })
    } catch (error) {
      Logger.error(error, 'findListingsByKeywordsAdmin')
      return response.badRequest(error)
    }
  }

  // U
  public async updateListingAdmin({
    params: { id },
    request,
    response,
    auth,
  }: HttpContextContract) {
    const user = await auth.authenticate()
    try {
      const findListing = await Listing.query().where('id', id).first()

      if (!findListing) {
        return response.notFound({ success: false, message: 'Listing not found' })
      }

      const validationSchema = schema.create({
        suggested_by: schema.string.optional({ trim: true }, [
          rules.exists({ column: 'id', table: 'users' }),
        ]),
        listing_type: schema.enum.optional(Object.values(ListingType)), // schema.string([rules.exists({ column: 'slug', table: 'listing_types' })]),
        region: schema.string.optional([rules.exists({ column: 'slug', table: 'regions' })]),
        name: schema.string.optional(varcharRule),
        company_name: schema.string.optional(varcharRule),
        address_name: schema.string.optional(varcharRule),
        description: schema.string.optional(varcharRule),
        full_address: schema.string.optional(varcharRule),
        longitude: schema.number.optional([
          rules.requiredIfExists('latitude'),
          rules.range(-180, 180),
        ]),
        latitude: schema.number.optional([
          rules.requiredIfExists('longitude'),
          rules.range(-90, 90),
        ]),
        contact_number: schema.string.optional([rules.minLength(2), rules.maxLength(18)]),
        country_dial_code: schema.string.optional([rules.minLength(1), rules.maxLength(7)]),
        keywords: schema.array.optional([rules.minLength(1)]).members(schema.string()),
        postal_code: schema.string.optional(varcharRule),
        diaper_changing_mat_type: schema.string.optional(varcharRule),
        opening_hours: schema.string.optional(varcharRule),
        usage_durations: schema.array.optional().members(schema.number()),
        usage_extension_durations: schema.array.optional().members(schema.number()),
        max_number_of_usage_extensions: schema.number.optional(),
        number_of_private_feeding_rooms: schema.number.optional(),
        number_of_diaper_changing_mats: schema.number.optional(),
        main_image_id: schema.string.optional([
          rules.exists({ table: 'listing_files', column: 'id' }),
        ]),
        main_image_file: schema.file.optional(imageFileSpec),
        main_image_url: schema.string.optional(varcharRule),
        sub_image_ids: schema.array
          .optional([rules.distinct('*')])
          .members(schema.string([rules.exists({ table: 'listing_files', column: 'id' })])),
        sub_image_files: schema.array
          .optional([rules.minLength(1)])
          .members(schema.file(imageFileSpec)),
        sub_image_urls: schema.array
          .optional([rules.minLength(1)])
          .members(schema.string(varcharRule)),
        activities: schema.array
          .optional([
            rules.maxLength((await Activity.all()).length),
            rules.distinct('activity_slug'),
          ])
          .members(
            schema.object().members({
              is_hidden: schema.boolean(),
              activity_slug: schema.string([rules.exists({ table: 'activities', column: 'slug' })]),
            })
          ),
        amenities: schema.array
          .optional([rules.maxLength((await Amenity.all()).length), rules.distinct('amenity_slug')])
          .members(
            schema.object().members({
              is_hidden: schema.boolean(),
              amenity_slug: schema.string([rules.exists({ table: 'amenities', column: 'slug' })]),
            })
          ),
        note: schema.string.optional(varcharRule),
        api_key: schema.string.optional(varcharRule),
        pi_id: schema.string.optional(varcharRule),
        pi_last_updated: schema.date.optional(),
        lock_id: schema.string.optional(varcharRule),
        lock_master_pin: schema.string.optional(varcharRule),
        lock_bluetooth_admin_key: schema.string.optional(varcharRule),
        is_hidden: schema.boolean.optional(),
        is_verified: schema.boolean.optional(),
        is_usage_extendable: schema.boolean.optional(),
        status: schema.enum.optional(Object.values(ListingStatus)),
        door_is_lockable: schema.boolean.optional(),
        humidity: schema.number.optional(),
        temperature: schema.number.optional(),
        firestore_id: schema.string.optional(
          varcharRule.concat([rules.requiredWhen('listing_type', '=', ListingType.gomama)])
        ),
      })

      // TODO: Remove type_name, region_name in prod, id will be included in validationData
      const {
        main_image_id,
        main_image_file,
        main_image_url,
        sub_image_ids,
        sub_image_files,
        sub_image_urls,
        longitude,
        latitude,
        region,
        suggested_by,
        activities,
        amenities,
        ...validationData
      } = await request.validate({
        schema: validationSchema,
      })

      let existingPosition: Position | null
      let positionId: string | undefined
      let regionId: string | undefined

      existingPosition =
        longitude && latitude
          ? await Position.query()
              .whereRaw(`ST_Equals(coordinate, POINT(?, ?))`, [longitude, latitude])
              .first()
          : null

      if (existingPosition) {
        positionId = existingPosition.id
      } else {
        if (longitude && latitude) {
          // Position not exist create new position
          const geohash = ngeohash.encode(longitude, latitude, 20)
          const newPositionId = uuidv4()
          const result = await Database.rawQuery(
            'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
            [newPositionId, geohash, longitude, latitude]
          )

          if (result[0].affectedRows > 0) {
            const newPosition = await Position.findOrFail(newPositionId)
            positionId = newPosition.id
          } else {
            positionId = undefined
          }
        }
      }

      if (region) {
        const findRegion = await Region.findByOrFail('slug', region)
        regionId = findRegion.id
      }

      const result = await Database.transaction(async (trx) => {
        await findListing
          .merge({
            positionId: positionId,
            regionId: regionId,
            verifiedBy: validationData.is_verified ? user.id : undefined,
            ...validationData,
          })
          .useTransaction(trx)
          .save()

        //----------------------------------Activity Update----------------------------------

        if (activities) {
          const prepareActivitySyncObjs = {}
          await Promise.all(
            activities.map(async (activity) => {
              const activityFind = await Activity.findByOrFail('slug', activity.activity_slug)
              Object.assign(prepareActivitySyncObjs, {
                [activityFind.id]: { id: uuidv4(), is_hidden: activity.is_hidden },
              })
            })
          )
          await findListing.related('activities').sync(prepareActivitySyncObjs, true, trx)
        }

        //----------------------------------Activity Update----------------------------------

        //----------------------------------Amenity Update----------------------------------
        if (amenities) {
          const prepareAmenitySyncObjs = {}
          await Promise.all(
            amenities.map(async (amenity) => {
              const amenityFind = await Amenity.findByOrFail('slug', amenity.amenity_slug)
              Object.assign(prepareAmenitySyncObjs, {
                [amenityFind.id as string]: { id: uuidv4(), is_hidden: amenity.is_hidden },
              })
            })
          )
          await findListing.related('amenities').sync(prepareAmenitySyncObjs, true, trx)
        }
        //----------------------------------Amenity Update----------------------------------

        //----------------------------------Image Update----------------------------------
        const result = await updateImageHandler(
          findListing,
          user,
          trx,
          main_image_id,
          main_image_file,
          main_image_url,
          sub_image_ids,
          sub_image_files,
          sub_image_urls
        )

        return result
      })

      if (result && result.status == 'unprocessableEntity') {
        return response.unprocessableEntity({ success: false, message: result.msg })
      }

      if (result && result.status == 'conflict') {
        return response.conflict({ success: false, message: result.msg })
      }
      //----------------------------------Image Update----------------------------------

      const updatedListing = await Listing.query()
        .where('id', findListing.id)
        .preload('listingFiles')
        .preload('activities')
        .preload('amenities')
        .first()

      Event.emit('update:geojson', {})

      Event.emit('sync:meilisearch', { type: 'update', listingId: findListing!.id }) //Sync Meili with latest update

      return response.ok({
        success: true,
        message: 'Successfully updated listing',
        data: updatedListing,
      })
    } catch (error) {
      Logger.error(error, 'updateListingAdmin')
      return response.badRequest(error)
    }
  }

  public async updateListingImageAdmin({
    params: { id },
    request,
    response,
    auth,
  }: HttpContextContract) {
    const user = await auth.authenticate()
    const trx = await Database.transaction()
    try {
      const findListingFile = await ListingFile.query().where('id', id).first()

      if (!findListingFile) {
        await trx.rollback()
        return response.notFound({ success: false, message: 'Listing File not found' })
      }

      const validationSchema = schema.create({
        image_file: schema.file.optional(imageFileSpec, [
          rules.requiredIfNotExistsAll([
            'image_url',
            'is_approved',
            'is_hidden',
            'is_main',
            'to_delete',
          ]),
        ]),
        image_url: schema.string.optional([
          rules.maxLength(255),
          rules.requiredIfNotExistsAll([
            'image_file',
            'is_approved',
            'is_hidden',
            'is_main',
            'to_delete',
          ]),
        ]),
        is_approved: schema.boolean.optional(),
        is_hidden: schema.boolean.optional(),
        is_main: schema.boolean.optional(),
        to_delete: schema.boolean.optional(),
      })

      const { image_file, image_url, to_delete, ...validatedData } = await request.validate({
        schema: validationSchema,
      })

      // Request to delete image
      if (to_delete) {
        const currentMain = await ListingFile.query()
          .where('listing_id', findListingFile.listingId)
          .andWhere('is_main', true)
          .first()
        if (currentMain && currentMain.id == findListingFile.id) {
          await trx.rollback()
          return response.forbidden({
            success: false,
            message:
              'Unable to delete image, each listing must have atleast one main image, please upload/update a new main image before delete this image',
          })
        }

        // Proceed to delete
        findListingFile.useTransaction(trx)
        await findListingFile.delete()
        await Drive.use(disk_name).delete(
          remotePathListing + findListingFile.imageUrl.split(remotePathListing)[1]
        )
        await trx.commit()
        return response.ok({ success: true, message: 'Deleted image successfully' })
      }

      // Uploaded Image File
      findListingFile.isApproved = true
      findListingFile.uploadedBy = user.id
      findListingFile.reviewedBy = user.id

      findListingFile.imageUrl &&
        (image_file || image_url) &&
        (await Drive.use(disk_name).delete(
          remotePathListing + findListingFile.imageUrl.split(remotePathListing)[1]
        )) // Delete old image file if any, if image media source need to be updated

      if (image_file) {
        const remoteName = `${findListingFile.id}_${generateRandomCode(10)}.${image_file.extname}`
        await image_file.moveToDisk(remotePathListing, { name: remoteName }, disk_name)
        findListingFile.imageUrl = `${remoteName}`
      }

      // Use URL (If there is image_file and image_url, image_file will be priority, the 'else if' will skipped the image_url if both image_url and image_file present)
      else if (image_url) {
        findListingFile.imageUrl = image_url
      }

      findListingFile.merge({
        isApproved: validatedData.is_approved,
        isHidden: validatedData.is_hidden,
      })

      if (validatedData.is_main) {
        const currentMain = await ListingFile.query()
          .where('listing_id', findListingFile.listingId)
          .andWhere('is_main', true)
          .first()

        if (currentMain && currentMain.id != findListingFile.id) {
          currentMain.isMain = false
          await currentMain.useTransaction(trx).save()
        }
      } else if (validatedData.is_main == false) {
        const currentMain = await ListingFile.query()
          .where('listing_id', findListingFile.listingId)
          .andWhere('is_main', true)
          .first()
        if (currentMain && currentMain.id == findListingFile.id) {
          await trx.rollback()
          return response.forbidden({
            success: false,
            message: 'Each listing must have atleast one main image',
          })
        }
      }

      findListingFile.isMain = true
      await findListingFile.useTransaction(trx).save()

      await trx.commit()
      return response.ok({ success: true, message: 'Updated image successfully' })
    } catch (error) {
      await trx.rollback()
      Logger.error(error, 'updateListingImageAdmin')
      return response.badRequest(error)
    }
  }

  // D
  public async deleteListing({ params: { id }, response }: HttpContextContract) {
    try {
      const findListing = await Listing.find(id)

      if (!findListing) {
        return response.notFound({ success: false, message: 'Listing not found' })
      }

      await Database.transaction(async (trx) => {
        // delete many in a single query and return deleted files
        // const associatedListingFiles =
        await ListingFile.query({ client: trx }).where('listing_id', findListing.id).delete()

        findListing.useTransaction(trx)
        await findListing.delete()

        // TODO: do we want to do this? maybe it's fine to keep in disk
        // remove all related listing images from disk
        // for await (const [, listfile] of associatedListingFiles.entries()) {
        //   const toBeDeleteUrl = remotePathListing + listfile.imageUrl.split(remotePathListing)[1]
        //   await Drive.use(disk_name).delete(toBeDeleteUrl)
        // }
      })

      Event.emit('update:geojson', {})

      Event.emit('sync:meilisearch', { type: 'delete', listingId: findListing!.id }) //Sync Meili after listing deleted

      return response.ok({
        success: true,
        message: 'Successfully deleted the listing',
      })
    } catch (error) {
      Logger.error(error, 'deleteListing')
      return response.badRequest(error)
    }
  }
}
