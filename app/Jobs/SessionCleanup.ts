import { Job, Worker } from 'bullmq'
import Logger from '@ioc:Adonis/Core/Logger'
import Session from 'App/Models/Session'
import { DateTime } from 'luxon'
import Redis from '@ioc:Adonis/Addons/Redis'
import { REDIS_KEYS } from 'App/Services/RedisService'
import Env from '@ioc:Adonis/Core/Env'

const redisConnection = {
  host: Env.get('REDIS_HOST', '127.0.0.1'),
  port: Env.get('REDIS_PORT', 6379),
  password: Env.get('REDIS_PASSWORD', undefined),
}

const worker = new Worker(
  'session-cleanup',
  async (job: Job) => {
    const { sessionId } = job.data
    Logger.info({ sessionId }, 'Processing session cleanup job')

    try {
      const lastSessionByUser = await Session.query()
        .where('id', sessionId)
        .whereNull('actual_ended_at')
        .first()

      if (!lastSessionByUser) {
        Logger.info({ sessionId }, 'Session already cleaned up or does not exist')
        return
      }

      lastSessionByUser.actualEndedAt = DateTime.now()
      await lastSessionByUser.save()

      await Redis.pipeline()
        .del(`${REDIS_KEYS.SESSION_HASH}:${lastSessionByUser.userId}`)
        .hdel(REDIS_KEYS.SESSION_IDS, lastSessionByUser.id)
        .zrem(REDIS_KEYS.SESSION_EXPIRY_SET, lastSessionByUser.id)
        .publish(
          `${REDIS_KEYS.SESSION_HASH}:${lastSessionByUser.userId}`,
          JSON.stringify({ type: 'session_cleanup' })
        )
        .exec()

      Logger.info({ sessionId }, 'Session cleanup successful')
    } catch (error) {
      Logger.error(error, { sessionId })
      throw error // Throw error to let BullMQ handle retry
    }
  },
  {
    connection: redisConnection,
  }
)

export default worker
