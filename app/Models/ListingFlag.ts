import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, beforeCreate, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Listing from './Listing'
import User from './User'
import { v4 as uuidv4 } from 'uuid'
import { remotePathListingFlag, s3PrivateEndpoint } from 'App/utils'
import ListingFlagFilter from './Filters/ListingFlagFilter'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'

export default class ListingFlag extends compose(BaseModel, Filterable) {
  public static $filter = () => ListingFlagFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public listingId: string

  @column()
  public userId: string

  @column()
  public category: string // ENUM

  @column()
  public reason: string

  @column({
    prepare: (value: string[]) => JSON.stringify(value),
    consume: (value: string) => {
      const arrImageFile = JSON.parse(value)
      if (Array.isArray(arrImageFile)) {
        return arrImageFile.map((imageFile: string) => {
          return s3PrivateEndpoint + '/' + remotePathListingFlag + '/' + imageFile
        })
      }
      return value
    },
  })
  public referenceImages: string[]

  @column.dateTime()
  public reviewedAt: DateTime

  @column()
  public action: string // ENUM

  @column()
  public actionReason: string

  @column()
  public actionBy: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  //Relationship
  @belongsTo(() => Listing, { foreignKey: 'listingId', localKey: 'id' })
  public listing: BelongsTo<typeof Listing>

  @belongsTo(() => User, { foreignKey: 'userId', serializeAs: 'complainant', localKey: 'id' }) // User who submitted Listing Flag
  public user: BelongsTo<typeof User>

  @belongsTo(() => User, { foreignKey: 'actionBy', serializeAs: 'reviewer', localKey: 'id' }) // Admin who reviewed the Listing and took action
  public admin: BelongsTo<typeof User>
  //Relationship

  @beforeCreate()
  public static async createUUID(listingFlag: ListingFlag) {
    listingFlag.id = uuidv4()
  }
}
