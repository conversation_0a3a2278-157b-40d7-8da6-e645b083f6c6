import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, column, ManyToMany, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import AmenityFilter from './Filters/AmenityFilter'
import { v4 as uuidv4 } from 'uuid'
import { slugify } from '@ioc:Adonis/Addons/LucidSlugify'
import Listing from './Listing'

export default class Amenity extends compose(BaseModel, Filterable) {
  public static $filter = () => AmenityFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  @slugify({
    strategy: 'dbIncrement',
    fields: ['name'],
  })
  public slug: string

  @column()
  public description: string

  @column()
  public fontIconName: string

  @column({ consume: (value: boolean) => Boolean(value) })
  public isHidden: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async createUUID(amenity: Amenity) {
    amenity.id = uuidv4()
  }

  // Relationship
  @manyToMany(() => Listing, {
    pivotTable: 'listing_amenities',
    pivotColumns: ['is_hidden'],
    pivotTimestamps: true,
    localKey: 'id',
    pivotForeignKey: 'listing_id',
    relatedKey: 'id',
    pivotRelatedForeignKey: 'amenity_id',
  })
  public amenities: ManyToMany<typeof Listing>
  // Relationship
}
