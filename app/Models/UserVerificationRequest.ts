import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import {
  UserVerificationStatus,
  UserVerificationType,
} from 'Contracts/user_verification_request_type'
import { v4 as uuidv4 } from 'uuid'
import User from './User'
import { remotePathListing, s3PrivateEndpoint } from 'App/utils'

export default class UserVerificationRequest extends BaseModel {
  @column({ isPrimary: true })
  public id: string

  @column()
  public type: UserVerificationType

  @column()
  public userId: string

  @column({
    consume: (value: string) => s3PrivateEndpoint + '/' + remotePathListing + '/' + value,
  })
  public gomamaUserVerifyPhotoUrl: string

  // Gomama details
  @column()
  public gomamaFirstName: string

  @column()
  public gomamaLastName: string

  @column.dateTime()
  public gomamaBirthday: DateTime

  @column({
    prepare: (value: string[]) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value),
  })
  public gomamaChildrenBirthday: DateTime[]

  @column()
  public gomamaMobileNumber: string

  @column()
  public gomamaEmail: string

  @column()
  public gomamaGender: string

  @column()
  public gomamaFinOrPassport: string

  // Singpass details
  @column()
  public singpassMobileNumber: string

  @column()
  public singpassEmail: string

  @column.dateTime()
  public singpassBirthday: DateTime

  @column()
  public singpassNric: string

  @column()
  public singpassFirstName: string

  @column()
  public singpassLastName: string

  @column()
  public singpassGender: string

  @column({
    prepare: (value: string[]) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value),
  })
  public singpassChildrenBirthday: DateTime[]

  @column.dateTime()
  public reviewedAt: DateTime

  @column()
  public status: UserVerificationStatus

  @column()
  public reason: string

  @column()
  public reviewBy: string // admin that review the request

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // relationship
  @belongsTo(() => User, { foreignKey: 'userId', localKey: 'id' })
  public user: BelongsTo<typeof User>

  @belongsTo(() => User, { foreignKey: 'reviewBy', localKey: 'id' })
  public reviewAdmin: BelongsTo<typeof User>
  // relationship

  @beforeCreate()
  public static async createUUID(userVerificationRequest: UserVerificationRequest) {
    userVerificationRequest.id = uuidv4()
  }
}
