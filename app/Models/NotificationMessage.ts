import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import NotificationMessageFilter from './Filters/NotificationMessageFilter'
import PublishedMessage from './PublishedMessage'
import { protocolRegex, remotePathNotificationMessage, s3PublicEndpoint } from 'App/utils'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'

export enum MessageStatus {
  SENDS = 'sends',
  RECEIVED = 'received', // android only
  IMPRESSIONS = 'impressions', // android only
  OPENS = 'opens',
}

export default class NotificationMessage extends compose(BaseModel, Filterable) {
  public static $filter = () => NotificationMessageFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public title: string

  @column()
  public message: string

  @column({
    consume: (value: string) =>
      value
        ? String(
            !protocolRegex.test(value)
              ? s3PublicEndpoint + '/' + remotePathNotificationMessage + '/' + value
              : value
          )
        : null,
  })
  public imageUrl: string

  @column()
  public createdBy: string

  @column()
  public updatedBy: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User, { foreignKey: 'createdBy', localKey: 'id', serializeAs: 'create_user' })
  public createUser: BelongsTo<typeof User>

  @belongsTo(() => User, { foreignKey: 'updatedBy', localKey: 'id', serializeAs: 'update_user' })
  public updateUser: BelongsTo<typeof User>

  @hasMany(() => PublishedMessage, {
    foreignKey: 'messageId',
    localKey: 'id',
    serializeAs: 'used_by_published_messages',
  })
  public usedByPublishedMessages: HasMany<typeof PublishedMessage>
}
