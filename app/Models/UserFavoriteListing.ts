import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, beforeCreate, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Listing from './Listing'
import User from './User'
import { v4 as uuidv4 } from 'uuid'

export default class UserFavoriteListing extends BaseModel {
  @column({ isPrimary: true })
  public id: string

  @column()
  public listingId: string

  @column()
  public userId: string

  @column({ consume: (value: boolean) => Boolean(value) })
  public isHidden: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // relationship
  @belongsTo(() => Listing, { foreignKey: 'listingId', localKey: 'id' })
  public listing: BelongsTo<typeof Listing>

  @belongsTo(() => User, { foreignKey: 'userId', localKey: 'id' })
  public user: BelongsTo<typeof User>
  // relationship

  // computation
  // computation

  @beforeCreate()
  public static async createUUID(userFavoriteListing: UserFavoriteListing) {
    userFavoriteListing.id = uuidv4()
  }
}
