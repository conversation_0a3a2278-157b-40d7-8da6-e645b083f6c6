import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, beforeCreate, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Amenity from './Amenity'
import Listing from './Listing'
import { v4 as uuidv4 } from 'uuid'

export default class ListingAmenity extends BaseModel {
  @column({ isPrimary: true })
  public id: string

  @column()
  public amenityId: string

  @column()
  public listingId: string

  @column({ consume: (value: boolean) => Boolean(value) })
  public isHidden: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Amenity, { foreignKey: 'amenityId', localKey: 'id' })
  public amenity: BelongsTo<typeof Amenity>

  @belongsTo(() => Listing, { foreignKey: 'listingId', localKey: 'id' })
  public listing: BelongsTo<typeof Listing>

  @beforeCreate()
  public static async createUUID(ListingAmenity: ListingAmenity) {
    ListingAmenity.id = uuidv4()
  }
}
