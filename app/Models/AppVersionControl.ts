import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { v4 as uuidv4 } from 'uuid'
import User from './User'

export default class AppVersionControl extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public version: string

  @column()
  public description: string // description about the version, optional

  @column()
  public publishedBy: string | null //publish admin

  @column()
  public addedBy: string // version added by which admin

  @column.dateTime()
  public publishedAt: DateTime | null

  @column({
    consume: (value: boolean) => Boolean(value),
    columnName: 'force_update',
    serializeAs: 'force_update',
  })
  public mandatory: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async createUUID(appVersion: AppVersionControl) {
    appVersion.id = uuidv4()
  }

  // Relationship
  @belongsTo(() => User, { foreignKey: 'addedBy', localKey: 'id' })
  public addedByAdmin: BelongsTo<typeof User>

  @belongsTo(() => User, { foreignKey: 'publishedBy', localKey: 'id' })
  public publishedByAdmin: BelongsTo<typeof User>
  // Relationship
}
