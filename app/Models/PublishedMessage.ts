import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import NotificationMessage from './NotificationMessage'
import Device from './Device'

export enum SendGroup {
  ALL_USERS = 'all_users',
  NEW_REGISTERED = 'new_registered',
  ACTIVE_30 = 'active_30',
}

export default class PublishedMessage extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public messageId: number

  @column()
  public publishedBy: string

  @column()
  public deviceId: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => NotificationMessage, {
    foreignKey: 'messageId',
    localKey: 'id',
    serializeAs: 'message_used',
  })
  public messageUsed: BelongsTo<typeof NotificationMessage>

  @belongsTo(() => User, { foreignKey: 'publishedBy', localKey: 'id', serializeAs: 'publish_user' })
  public publishUser: BelongsTo<typeof User>

  @belongsTo(() => Device, {
    foreignKey: 'deviceId',
    localKey: 'id',
    serializeAs: 'receive_device',
  })
  public receiveDevice: BelongsTo<typeof Device>
}
