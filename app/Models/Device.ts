import { DateTime } from 'luxon'
import { BaseModel, column, HasMany, hasMany, ManyToMany, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import PublishedMessage from './PublishedMessage'

export default class Device extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public deviceToken: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @manyToMany(() => User, {
    pivotTable: 'user_devices',
    pivotTimestamps: true,
    localKey: 'id',
    pivotForeignKey: 'user_id',
    relatedKey: 'id',
    pivotRelatedForeignKey: 'device_id',
    serializeAs: 'owners',
  })
  public owners: ManyToMany<typeof User>

  @hasMany(() => PublishedMessage, {
    foreignKey: 'deviceId',
    localKey: 'id',
    serializeAs: 'received_messages',
  })
  public receivedMessages: HasMany<typeof PublishedMessage>
}
