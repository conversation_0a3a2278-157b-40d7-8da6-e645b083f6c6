import { DateTime } from 'luxon'
import {
  BaseModel,
  beforeCreate,
  BelongsTo,
  belongsTo,
  column,
  computed,
  HasOne,
  hasOne,
} from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import ListingRating from './ListingRating'
import Listing from './Listing'
import User from './User'
import SessionFilter from './Filters/SessionFilter'
import { v4 as uuidv4 } from 'uuid'
import BigNumber from 'bignumber.js'

export default class Session extends compose(BaseModel, Filterable) {
  public static $filter = () => SessionFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public listingId: string

  @column()
  public userId: string

  @column()
  public lockBluetoothGuestKey: string

  @column()
  public lockCustomPin: string

  @column()
  public lockDailyPin: string

  @column()
  public lockHourlyPin: string

  @column()
  public lockOneTimePin: string

  @column.dateTime()
  public startedAt: DateTime

  @column.dateTime()
  public expectedEndedAt: DateTime

  @column.dateTime()
  public actualEndedAt: DateTime

  @column({ consume: (value) => Number(value) })
  public numberOfUsageExtensions: number

  @column({ consume: (value: boolean) => Boolean(value) })
  public isHidden: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  //TODO: TO BE REMOVED After Import data.
  @column()
  public firestoreId: string

  // relationship
  @belongsTo(() => Listing, { foreignKey: 'listingId', localKey: 'id' })
  public listing: BelongsTo<typeof Listing>

  @belongsTo(() => User, { foreignKey: 'userId', localKey: 'id' })
  public user: BelongsTo<typeof User>

  @hasOne(() => ListingRating, {
    serializeAs: 'listing_rating',
    foreignKey: 'sessionId',
    localKey: 'id',
  })
  public listingRating: HasOne<typeof ListingRating>
  // relationship

  // computation
  @computed({ serializeAs: 'actual_usage_duration' })
  public get actualUsageDuration() {
    if (this.startedAt && this.actualEndedAt) {
      const diff = this.actualEndedAt.diff(this.startedAt)
      return BigNumber(diff.milliseconds / 1000 / 60)
        .decimalPlaces(2)
        .toNumber()
    }
    return null
  }

  @computed({ serializeAs: 'expected_usage_duration' })
  public get expectedUsageDuration() {
    if (this.startedAt && this.expectedEndedAt) {
      const diff = this.expectedEndedAt.diff(this.startedAt)
      return BigNumber(diff.milliseconds / 1000 / 60)
        .decimalPlaces(2)
        .toNumber()
    }
    return null
  }

  @computed({ serializeAs: 'is_ended' })
  public get isEnded() {
    if (this.actualEndedAt != null) {
      return true
    }
    return false
  }
  // computation

  @beforeCreate()
  public static async createUUID(session: Session) {
    session.id = uuidv4()
  }
}
