import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import NotificationMessage from '../NotificationMessage'

export default class NotificationMessageFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof NotificationMessage, NotificationMessage>

  public id(value: any): void {
    this.$query.where('id', 'LIKE', '%' + value + '%')
  }

  public title(value: any): void {
    this.$query.where('title', 'LIKE', '%' + value + '%')
  }
}
