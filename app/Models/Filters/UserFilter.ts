import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import User from 'App/Models/User'

export default class UserFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof User, User>

  public id(value: string): void {
    this.$query.where('id', 'LIKE', `%${value}%`)
  }

  public username(value: any): void {
    this.$query.where('username', 'LI<PERSON>', `%${value}%`)
  }

  public fullName(value: string): void {
    this.$query.whereRaw(`CONCAT(first_name, ' ', last_name) LIKE ?`, [`%${value}%`])
  }

  public fullMobileNumber(value: string): void {
    this.$query.whereRaw(`CONCAT('+', country_dial_code, ' ', mobile_number) LIKE ?`, [
      `%${value}%`,
    ])
  }

  public emailAddress(value: string): void {
    this.$query.where('email_address', 'LIKE', `%${value}%`)
  }

  public countryName(value: string): void {
    this.$query.where('country_name', 'LIKE', `%${value}%`)
  }

  public authType(value: string): void {
    this.$query.where('auth_type', 'LIKE', `%${value}%`)
  }
}
