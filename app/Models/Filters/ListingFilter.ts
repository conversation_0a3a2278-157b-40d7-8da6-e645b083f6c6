import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Listing from 'App/Models/Listing'
import { isArray } from 'radash'

export default class ListingFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Listing, Listing>

  public amenities(value: any): void {
    let id = [value]
    if (isArray(value)) {
      id = value
    }

    id.forEach((amenityId) => {
      this.$query.whereHas('amenities', (subQuery) => {
        subQuery.where('amenity_id', amenityId).andWhere('amenities.is_hidden', false)
      })
    })
  }

  public types(value: any): void {
    if (value == 'all') {
      return
    }

    let filter = [value]
    if (isArray(value)) {
      filter = value
    }

    this.$query.whereIn('listing_type', filter)
  }
}
