import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Activity from 'App/Models/Activity'

export default class ActivityFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Activity, Activity>

  public id(value: any): void {
    this.$query.where('id', 'LIKE', '%' + value + '%')
  }

  public name(value: any): void {
    this.$query.where('name', 'LIKE', '%' + value + '%')
  }

  public description(value: any): void {
    this.$query.where('description', 'LIKE', '%' + value + '%')
  }

  public slug(value: any): void {
    this.$query.where('slug', 'LIKE', '%' + value + '%')
  }
}
