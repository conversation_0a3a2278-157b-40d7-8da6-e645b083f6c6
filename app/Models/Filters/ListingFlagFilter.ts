import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import ListingFlag from 'App/Models/ListingFlag'

export default class ListingFlagFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof ListingFlag, ListingFlag>

  public id(value: any): void {
    this.$query.where('id', 'LIKE', '%' + value + '%')
  }

  public listingId(value: any): void {
    this.$query.where('listing_id', 'LIKE', '%' + value + '%')
  }

  public category(value: any): void {
    this.$query.where('category', 'LIKE', '%' + value + '%')
  }
}
