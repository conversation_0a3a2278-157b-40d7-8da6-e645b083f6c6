import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import ListingRating from 'App/Models/ListingRating'

export default class ListingRatingFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof ListingRating, ListingRating>

  // public method (value: any): void {
  //   this.$query.where('name', value)
  // }

  public listingId(value: number): void {
    this.$query.where('listing_id', value)
  }
}
