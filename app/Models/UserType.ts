// import { DateTime } from 'luxon'
// import { BaseModel, beforeCreate, column } from '@ioc:Adonis/Lucid/Orm'
// import { v4 as uuidv4 } from 'uuid'
// import { slugify } from '@ioc:Adonis/Addons/LucidSlugify'

// export default class UserType extends BaseModel {
//   @column({ isPrimary: true })
//   public id: string

//   @column()
//   public name: string

//   @column()
//   @slugify({
//     strategy: 'dbIncrement',
//     fields: ['name'],
//   })
//   public slug: string

//   @column()
//   public description: string

//   @column({ consume: (value: boolean) => Boolean(value) })
//   public isHidden: boolean

//   @column.dateTime({ autoCreate: true })
//   public createdAt: DateTime

//   @column.dateTime({ autoCreate: true, autoUpdate: true })
//   public updatedAt: DateTime

//   @beforeCreate()
//   public static async createUUID(userType: UserType) {
//     userType.id = uuidv4()
//   }
// }
