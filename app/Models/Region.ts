import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import Listing from './Listing'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import RegionFilter from './Filters/RegionFilter'
import { v4 as uuidv4 } from 'uuid'
import { protocolRegex, remotePathRegion, s3PublicEndpoint } from 'App/utils'
import { slugify } from '@ioc:Adonis/Addons/LucidSlugify'

export default class Region extends compose(BaseModel, Filterable) {
  public $filter = () => RegionFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  @slugify({
    strategy: 'dbIncrement',
    fields: ['name'],
  })
  public slug: string

  @column()
  public description: string

  @column({
    consume: (value: string) =>
      String(
        !protocolRegex.test(value) ? s3PublicEndpoint + '/' + remotePathRegion + '/' + value : value
      ),
  })
  public imageUrl: string

  @column({ consume: (value: boolean) => Boolean(value) })
  public isHidden: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // relationship
  @hasMany(() => Listing, { foreignKey: 'regionId', localKey: 'id' })
  public listings: HasMany<typeof Listing>
  // relationship

  // computation
  // computation

  @beforeCreate()
  public static async createUUID(region: Region) {
    region.id = uuidv4()
  }
}
