import { DateTime } from 'luxon'
import { <PERSON>Model, BelongsTo, beforeCreate, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Listing from './Listing'
import { v4 as uuidv4 } from 'uuid'
import User from './User'
import { protocolRegex, remotePathListing, s3PublicEndpoint } from 'App/utils'

export default class ListingFile extends BaseModel {
  @column({ isPrimary: true })
  public id: string

  @column()
  public listingId: string

  @column()
  public uploadedBy: string

  @column({
    consume: (value: string) =>
      String(
        !protocolRegex.test(value)
          ? s3PublicEndpoint + '/' + remotePathListing + '/' + value
          : value
      ),
  })
  public imageUrl: string

  @column({ consume: (value: boolean) => Boolean(value) })
  public isMain: boolean

  @column({ consume: (value: boolean) => Boolean(value) })
  public isApproved: boolean

  @column({ consume: (value: boolean) => Boolean(value) })
  public isHidden: boolean

  @column()
  public reviewedBy: string | undefined

  @column()
  public notApprovedReason: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // Relationship
  @belongsTo(() => Listing, { foreignKey: 'listingId', localKey: 'id' })
  public listing: BelongsTo<typeof Listing>

  @belongsTo(() => User, { foreignKey: 'uploadedBy', localKey: 'id' }) // User who submitted the image
  public uploader: BelongsTo<typeof User>

  @belongsTo(() => User, { foreignKey: 'reviewedBy', localKey: 'id' }) // User who reviewed the image
  public reviewer: BelongsTo<typeof User>
  // Relationship

  @beforeCreate()
  public static async createUUID(listingFile: ListingFile) {
    listingFile.id = uuidv4()
  }
}
