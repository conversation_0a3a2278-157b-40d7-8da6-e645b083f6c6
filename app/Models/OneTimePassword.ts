import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { OtpStatus, OtpType } from 'Contracts/otp_type'

export default class OneTimePassword extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public phone: string

  @column()
  public email: string

  @column()
  public oneTimePassword: string

  @column()
  public type: OtpType

  @column()
  public status: OtpStatus

  @column.dateTime({ autoCreate: true })
  public expiresAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
