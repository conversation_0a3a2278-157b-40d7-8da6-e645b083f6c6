import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, column, ManyToMany, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import ActivityFilter from './Filters/ActivityFilter'
import { v4 as uuidv4 } from 'uuid'
import { protocolRegex, remotePathActivity, s3PublicEndpoint } from 'App/utils'
import { slugify } from '@ioc:Adonis/Addons/LucidSlugify'
import Listing from './Listing'

export default class Activity extends compose(BaseModel, Filterable) {
  public static $filter = () => ActivityFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  @slugify({
    strategy: 'dbIncrement',
    fields: ['name'],
  })
  public slug: string

  @column()
  public description: string //indoor_play, kids_friendly_restaurants, breastfeeding, diaper_changing, essentials, toys_books_fashion, medical, outdoor_play

  @column({
    consume: (value: string) =>
      String(
        !protocolRegex.test(value)
          ? s3PublicEndpoint + '/' + remotePathActivity + '/' + value
          : value
      ),
  })
  public imageUrl: string

  @column({ consume: (value: boolean) => Boolean(value) })
  public isHidden: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async createUUID(activity: Activity) {
    activity.id = uuidv4()
  }

  // Relationship
  @manyToMany(() => Listing, {
    pivotTable: 'listing_activities',
    pivotColumns: ['is_hidden'],
    pivotTimestamps: true,
    localKey: 'id',
    pivotForeignKey: 'listing_id',
    relatedKey: 'id',
    pivotRelatedForeignKey: 'activity_id',
  })
  public listings: ManyToMany<typeof Listing>
  // Relationship
}
