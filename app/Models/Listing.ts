import { DateTime } from 'luxon'
import {
  BaseModel,
  beforeCreate,
  beforeFetch,
  beforeFind,
  BelongsTo,
  belongsTo,
  column,
  computed,
  HasMany,
  hasMany,
  ManyToMany,
  ModelQueryBuilderContract,
  manyToMany,
  afterFetch,
  afterFind,
} from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import ListingFilter from './Filters/ListingFilter'
import ListingRating from './ListingRating'
// import ListingType from './ListingType'
import Position from './Position'
import Region from './Region'
import Session from './Session'
import _ from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import ListingFlag from './ListingFlag'
import ListingFile from './ListingFile'
import User from './User'
import { ListingStatus, ListingType } from 'Contracts/listing_type'
import Activity from './Activity'
import Amenity from './Amenity'
import { AxiosResponse } from 'axios'

// In radians
type Coordinate = { lat: number; lon: number }

export function calculateDistance(cord1: Coordinate, cord2: Coordinate) {
  if (cord1.lat == cord2.lat && cord1.lon == cord2.lon) {
    return 0
  }

  const radlat1 = (Math.PI * cord1.lat) / 180
  const radlat2 = (Math.PI * cord2.lat) / 180

  const theta = cord1.lon - cord2.lon
  const radtheta = (Math.PI * theta) / 180

  let dist =
    Math.sin(radlat1) * Math.sin(radlat2) +
    Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta)

  if (dist > 1) {
    dist = 1
  }

  dist = Math.acos(dist)
  dist = (dist * 180) / Math.PI
  dist = dist * 60 * 1.1515
  dist = dist * 1.609344 //convert miles to km
  dist = dist * 1000 // convert to metres

  return dist
}

export default class Listing extends compose(BaseModel, Filterable) {
  public static $filter = () => ListingFilter

  @column({ isPrimary: true })
  public id: string

  // @column()
  // public typeId: string

  @column()
  public positionId: string

  @column()
  public regionId: string

  @column()
  public name: string

  @column()
  public companyName: string

  @column()
  public addressName: string

  @column()
  public description: string

  @column()
  public fullAddress: string

  @column()
  public contactNumber: string

  @column()
  public countryDialCode: string

  @column({
    prepare: (value: string[]) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value),
  })
  public keywords: string[]

  @column()
  public postalCode: string

  @column({
    prepare: (value: number[]) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value),
  })
  public usageDurations: number[]

  @column({
    prepare: (value: number[]) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value),
  })
  public usageExtensionDurations: number[]

  @column()
  public maxNumberOfUsageExtensions: number

  @column()
  public numberOfPrivateFeedingRooms: number

  @column()
  public numberOfDiaperChangingMats: number

  @column()
  public diaperChangingMatType: string

  @column()
  public humidity: number

  @column()
  public temperature: number

  @column()
  public openingHours: string

  @column()
  public piId: string

  @column.dateTime()
  public piLastUpdated: DateTime

  @column()
  public apiKey: string

  @column()
  public lockId: string

  @column()
  public lockMasterPin: string

  @column()
  public lockBluetoothAdminKey: string

  @column()
  public verifiedBy: string // admin that review the listing

  @column()
  public suggestedBy: string // user that suggest the listing

  @column()
  public listingType: ListingType

  @column()
  public status: ListingStatus

  @column({ consume: (value: boolean) => Boolean(value) })
  public doorIsLockable: boolean

  @column({ consume: (value: boolean) => Boolean(value) })
  public isUsageExtendable: boolean

  @column({ consume: (value: boolean) => Boolean(value) })
  public isVerified: boolean

  @column({ consume: (value: boolean) => Boolean(value) })
  public isHidden: boolean

  @column()
  public note: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  //TODO: TO BE REMOVED After Import data.
  @column()
  public firestoreId: string

  private _distance: number
  private _deviceDistance: number
  private static _meilisearchListingsInfo: AxiosResponse<any, any> | null

  // relationship
  // @hasOne(() => ListingType, {
  //   serializeAs: 'listing_type',
  //   foreignKey: 'id',
  //   localKey: 'typeId',
  // })
  // public listingType: HasOne<typeof ListingType>

  @belongsTo(() => Position, { foreignKey: 'positionId', localKey: 'id' })
  public position: BelongsTo<typeof Position>

  @belongsTo(() => Region, { foreignKey: 'regionId', localKey: 'id' })
  public region: BelongsTo<typeof Region>

  @belongsTo(() => User, { foreignKey: 'suggestedBy', localKey: 'id', serializeAs: 'suggest_user' })
  public suggestUser: BelongsTo<typeof User>

  @belongsTo(() => User, { foreignKey: 'verifiedBy', localKey: 'id', serializeAs: 'verify_admin' })
  public verifyAdmin: BelongsTo<typeof User>

  @hasMany(() => ListingRating, {
    serializeAs: 'listing_ratings',
    foreignKey: 'listingId',
    localKey: 'id',
  })
  public listingRatings: HasMany<typeof ListingRating>

  @hasMany(() => Session, { foreignKey: 'listingId', localKey: 'id' })
  public sessions: HasMany<typeof Session>

  @hasMany(() => ListingFlag, {
    serializeAs: 'listing_flags',
    foreignKey: 'listingId',
    localKey: 'id',
  })
  public listingFlags: HasMany<typeof ListingFlag>

  @hasMany(() => ListingFile, {
    serializeAs: 'listing_files',
    foreignKey: 'listingId',
    localKey: 'id',
  })
  public listingFiles: HasMany<typeof ListingFile>

  @manyToMany(() => Activity, {
    pivotTable: 'listing_activities',
    pivotColumns: ['is_hidden'],
    pivotTimestamps: true,
    localKey: 'id',
    pivotForeignKey: 'listing_id',
    relatedKey: 'id',
    pivotRelatedForeignKey: 'activity_id',
  })
  public activities: ManyToMany<typeof Activity>

  @manyToMany(() => Amenity, {
    pivotTable: 'listing_amenities',
    pivotColumns: ['is_hidden'],
    pivotTimestamps: true,
    localKey: 'id',
    pivotForeignKey: 'listing_id',
    relatedKey: 'id',
    pivotRelatedForeignKey: 'amenity_id',
  })
  public amenities: ManyToMany<typeof Amenity>

  @manyToMany(() => User, {
    pivotTable: 'user_favorite_listings',
    pivotColumns: ['is_hidden'],
    pivotTimestamps: true,
    localKey: 'id',
    pivotForeignKey: 'listing_id',
    relatedKey: 'id',
    pivotRelatedForeignKey: 'user_id',
  })
  public favoriteByUsers: ManyToMany<typeof User>
  // relationship

  // computation
  @computed({ serializeAs: 'full_contact_number' })
  public get fullContactNumber() {
    if (this.contactNumber && this.countryDialCode) {
      return `${this.countryDialCode}${this.contactNumber}`
    }
    return null
  }

  @computed({ serializeAs: 'distance' })
  public get getDistance() {
    return this._deviceDistance ?? this._distance
  }

  public serializeExtras() {
    return {
      average_experience_ratings: Number(this.$extras.average_experience_ratings),
      total_experience_ratings: Number(this.$extras.total_experience_ratings),
      total_sessions: Number(this.$extras.total_sessions),
      five_stars_count: Number(this.$extras.five_stars_count),
      four_stars_count: Number(this.$extras.four_stars_count),
      three_stars_count: Number(this.$extras.three_stars_count),
      two_stars_count: Number(this.$extras.two_stars_count),
      one_star_count: Number(this.$extras.one_star_count),
    }
  }

  // computation

  // For Multiple rows.
  @beforeFetch()
  public static async preloadData(query: ModelQueryBuilderContract<typeof Listing>) {
    query.preload('position').if('region_id' != null, (query) => query.preload('region'))
  }

  // For Single row, first()
  @beforeFind()
  public static async preloadDataSingle(query: ModelQueryBuilderContract<typeof Listing>) {
    query.preload('position').if('region_id' != null, (query) => query.preload('region'))
  }

  @beforeCreate()
  public static async createUUID(listing: Listing) {
    listing.id = uuidv4()
  }

  // For Multiple rows.
  @afterFetch()
  public static async getMeilisearchDistanceFetch(listings: Listing[]) {
    if (Listing._meilisearchListingsInfo != null) {
      for (const listing of listings) {
        const matchIndex = Listing._meilisearchListingsInfo.data.hits.findIndex(
          (hit) => hit.id === listing.id
        )
        listing._distance = Listing._meilisearchListingsInfo.data.hits[matchIndex]._geoDistance
        listing._deviceDistance =
          Listing._meilisearchListingsInfo.data.hits[matchIndex].device_distance
      }
      Listing._meilisearchListingsInfo = null
    }
  }

  // For Single row, first()
  @afterFind()
  public static async getMeilisearchDistanceFind(listing: Listing) {
    if (Listing._meilisearchListingsInfo != null) {
      listing._distance = Listing._meilisearchListingsInfo.data.hits[0]._geoDistance
      listing._deviceDistance = Listing._meilisearchListingsInfo.data.hits[0].device_distance
    }
    Listing._meilisearchListingsInfo = null
  }

  public static async fetchWithLocation(query, meilisearchListings: AxiosResponse<any, any>) {
    const listings = await query
    this._meilisearchListingsInfo = meilisearchListings
    await this.getMeilisearchDistanceFetch(listings)
    return listings
  }

  public static async findWithLocation(query, meilisearchListings: AxiosResponse<any, any>) {
    const listing = await query
    this._meilisearchListingsInfo = meilisearchListings
    await this.getMeilisearchDistanceFind(listing)
    return listing
  }
}
