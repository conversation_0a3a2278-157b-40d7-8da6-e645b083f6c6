import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  beforeCreate,
  belongsTo,
  column,
  computed,
} from '@ioc:Adonis/Lucid/Orm'
import Listing from './Listing'
import Session from './Session'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import ListingRatingFilter from './Filters/ListingRatingFilter'
import { v4 as uuidv4 } from 'uuid'

export default class ListingRating extends compose(BaseModel, Filterable) {
  public static $filter = () => ListingRatingFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public listingId: string

  @column()
  public sessionId: string

  @column({ consume: (value) => Number(value) })
  public appRating: number

  @column({ consume: (value) => Number(value) })
  public experienceRating: number

  @column({ consume: (value) => Number(value) })
  public listingRating: number

  @column()
  public review: string

  @column({ consume: (value: boolean) => Boolean(value) })
  public isHidden: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // relationship
  @belongsTo(() => Listing, { foreignKey: 'listingId', localKey: 'id' })
  public listing: BelongsTo<typeof Listing>

  @belongsTo(() => Session, { foreignKey: 'sessionId', localKey: 'id' })
  public session: BelongsTo<typeof Session>
  // relationship

  // computation
  @computed({ serializeAs: 'user' })
  public get user() {
    if (this.session) {
      const user = this.session.user
      return user
    }

    return undefined
  }

  @computed({ serializeAs: 'username' })
  public get username() {
    if (this.session && this.session.user) {
      const username = this.session.user.username
      return username
    }

    return undefined
  }
  // computation

  @beforeCreate()
  public static async createUUID(listingRating: ListingRating) {
    listingRating.id = uuidv4()
  }
}
