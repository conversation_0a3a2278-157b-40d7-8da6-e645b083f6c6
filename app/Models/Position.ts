import { DateTime } from 'luxon'
import { BaseModel, beforeCreate, column, HasOne, hasOne } from '@ioc:Adonis/Lucid/Orm'
import Listing from './Listing'
import PositionFilter from './Filters/PositionFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { v4 as uuidv4 } from 'uuid'

export default class Position extends compose(BaseModel, Filterable) {
  public $filter = () => PositionFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public geoHash: string

  @column()
  public coordinate: any

  @column({ consume: (value: boolean) => Boolean(value) })
  public isHidden: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // relationship
  @hasOne(() => Listing, { foreignKey: 'positionId', localKey: 'id' })
  public listing: HasOne<typeof Listing>
  // relationship

  // computation
  // computation

  @beforeCreate()
  public static async createUUID(position: Position) {
    position.id = uuidv4()
  }
}
