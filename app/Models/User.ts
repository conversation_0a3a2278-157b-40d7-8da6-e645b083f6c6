import { DateTime } from 'luxon'
import {
  BaseModel,
  beforeCreate,
  beforeFetch,
  beforeFind,
  beforeSave,
  column,
  computed,
  HasMany,
  hasMany,
  HasOne,
  hasOne,
  ManyToMany,
  manyToMany,
  ModelQueryBuilderContract,
  scope,
} from '@ioc:Adonis/Lucid/Orm'
// import UserType from './UserType'
import { UserType } from 'Contracts/user_type'
import Listing from './Listing'
import Session from './Session'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import UserFilter from './Filters/UserFilter'
import { v4 as uuidv4 } from 'uuid'
import Position from './Position'
import ListingFlag from './ListingFlag'
import Hash from '@ioc:Adonis/Core/Hash'
import ListingFile from './ListingFile'
import { protocolRegex, remotePathUserProfile, s3PublicEndpoint } from 'App/utils'
import { UserGender } from 'Contracts/users'
import UserVerificationRequest from './UserVerificationRequest'
import { UserVerificationType } from 'Contracts/user_verification_request_type'
import Profile from './Profile'
import AppVersionControl from './AppVersionControl'
import Database from '@ioc:Adonis/Lucid/Database'
import Device from './Device'
import NotificationMessage from './NotificationMessage'
import PublishedMessage from './PublishedMessage'

export default class User extends compose(BaseModel, Filterable) {
  public static $filter = () => UserFilter

  @column({ isPrimary: true })
  public id: string

  // @column()
  // public typeId: string

  @column()
  public userType: UserType

  @column()
  public positionId: string

  @column()
  public username: string

  @column()
  public emailAddress: string

  @column()
  public firstName: string

  @column()
  public lastName: string

  @column({
    consume: (value: string) =>
      String(
        !protocolRegex.test(value)
          ? s3PublicEndpoint + '/' + remotePathUserProfile + '/' + value
          : value
      ),
  })
  public photoUrl: string

  @column()
  public shareCode: string

  @column()
  public gender: UserGender

  @column.dateTime()
  public birthday: DateTime

  @column({
    prepare: (value: DateTime[]) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value),
  })
  public childrenBirthdays: DateTime[]

  @column()
  public mobileNumber: string

  @column()
  public companyName: string

  @column()
  public countryName: string

  @column({ consume: (value: string) => '+' + value })
  public countryCode: string

  @column()
  public countryDialCode: string

  @column()
  public nationality: string

  @column({ serializeAs: null })
  public cipherSecret: string

  @column({ serializeAs: null })
  public cipherIv: string

  @column({ serializeAs: null })
  public codeVerifier: string

  @column()
  public timezone: string

  @column()
  public authProvider: string

  @column({ consume: (value: boolean) => Boolean(value) })
  public isAdminVerified: boolean

  @column({ consume: (value: boolean) => Boolean(value) })
  public isEmailAddressVerified: boolean

  @column({ consume: (value: boolean) => Boolean(value) })
  public isMobileNumberVerified: boolean

  @column({ consume: (value: boolean) => Boolean(value) })
  public isSingpassVerified: boolean

  @column()
  public passportNumber: string

  @column({ consume: (value: boolean) => Boolean(value) })
  public isPassportVerified: boolean

  @column({ consume: (value: boolean) => Boolean(value) })
  public isGomamaVerified: boolean

  @column({ consume: (value: boolean) => Boolean(value) })
  public isHidden: boolean

  @column({ serializeAs: null })
  public singpassNonce: string

  @column({ serializeAs: null })
  public singpassState: string

  @column({ serializeAs: null })
  public singpassSecret: string

  @column()
  public latestVerifySelfieFailCount: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime()
  public deletedAt: DateTime | null

  @column()
  public deleteReason: string | null

  //TODO: TO BE REMOVED After Import data.
  @column()
  public firestoreId: string

  //TODO: TO BE REMOVED IN PROD
  @column({ serializeAs: null })
  public password: string

  // relationship
  @hasOne(() => Position, { foreignKey: 'positionId', localKey: 'id' })
  public position: HasOne<typeof Position>

  // @hasOne(() => UserType, { foreignKey: 'typeId', localKey: 'id' })
  // public userType: HasOne<typeof UserType>

  @manyToMany(() => Listing, {
    pivotTable: 'user_favorite_listings',
    pivotColumns: ['is_hidden'],
    pivotTimestamps: true,
    localKey: 'id',
    pivotForeignKey: 'user_id',
    relatedKey: 'id',
    pivotRelatedForeignKey: 'listing_id',
    serializeAs: 'favorite_listings',
  })
  public favoriteListings: ManyToMany<typeof Listing>

  @hasOne(() => UserVerificationRequest, {
    serializeAs: 'latest_gomama_verification_request',
    foreignKey: 'userId',
    localKey: 'id',
  })
  public latestGomamaVerificationRequest: HasOne<typeof UserVerificationRequest>

  @hasOne(() => UserVerificationRequest, {
    serializeAs: 'latest_singpass_verification_request',
    foreignKey: 'userId',
    localKey: 'id',
  })
  public latestSingpassVerificationRequest: HasOne<typeof UserVerificationRequest>

  @hasMany(() => UserVerificationRequest, {
    serializeAs: 'user_verification_requests',
    foreignKey: 'userId',
    localKey: 'id',
  })
  public userVerificationRequests: HasMany<typeof UserVerificationRequest>

  @hasMany(() => UserVerificationRequest, {
    serializeAs: 'reviewed_user_verification_requests',
    foreignKey: 'reviewBy',
    localKey: 'id',
  })
  public reviewedUserVerificationRequests: HasMany<typeof UserVerificationRequest>

  @hasMany(() => Listing, { foreignKey: 'suggestedBy', localKey: 'id' })
  public suggestedListings: HasMany<typeof Listing>

  @hasMany(() => Listing, { foreignKey: 'verifiedBy', localKey: 'id' })
  public verifiedListings: HasMany<typeof Listing>

  @hasMany(() => Session, { foreignKey: 'userId', localKey: 'id' })
  public sessions: HasMany<typeof Session>

  @hasMany(() => ListingFlag, { foreignKey: 'userId', localKey: 'id' })
  public submittedListingFlags: HasMany<typeof ListingFlag>

  @hasMany(() => ListingFlag, { foreignKey: 'actionBy', localKey: 'id' })
  public reviewedListingFlags: HasMany<typeof ListingFlag>

  @hasMany(() => ListingFile, { foreignKey: 'uploadedBy', localKey: 'id' })
  public uploadedListingFiles: HasMany<typeof ListingFile>

  @hasMany(() => ListingFile, { foreignKey: 'reviewedBy', localKey: 'id' })
  public reviewedListingFiles: HasMany<typeof ListingFile>

  @hasMany(() => Profile, { foreignKey: 'userId', localKey: 'id' })
  public profiles: HasMany<typeof Profile>

  @hasMany(() => AppVersionControl, { foreignKey: 'addedBy', localKey: 'id' })
  public addedAppVersion: HasMany<typeof AppVersionControl>

  @hasMany(() => AppVersionControl, { foreignKey: 'publishedBy', localKey: 'id' })
  public publishedAppVersion: HasMany<typeof AppVersionControl>

  @manyToMany(() => Device, {
    pivotTable: 'user_devices',
    pivotTimestamps: true,
    localKey: 'id',
    pivotForeignKey: 'user_id',
    relatedKey: 'id',
    pivotRelatedForeignKey: 'device_id',
    serializeAs: 'devices',
  })
  public devices: ManyToMany<typeof Device>

  @hasMany(() => NotificationMessage, { foreignKey: 'createdBy', localKey: 'id' })
  public createdMessage: HasMany<typeof NotificationMessage>

  @hasMany(() => NotificationMessage, { foreignKey: 'updatedBy', localKey: 'id' })
  public updatedMessage: HasMany<typeof NotificationMessage>

  @hasMany(() => PublishedMessage, { foreignKey: 'publishedBy', localKey: 'id' })
  public publishedMessage: HasMany<typeof PublishedMessage>
  // relationship

  // computation
  @computed({ serializeAs: 'full_name' })
  public get fullName() {
    if (this.firstName && this.lastName) {
      return this.firstName + ' ' + this.lastName
    }
    return null
  }

  @computed({ serializeAs: 'full_mobile_number' })
  public get fullMobileNumber() {
    if (this.mobileNumber && this.countryDialCode) {
      return '+' + this.countryDialCode + ' ' + this.mobileNumber
    }
    return null
  }

  @computed({ serializeAs: 'number_of_sessions' })
  public get numberOfSessions() {
    if (this.sessions) {
      return this.sessions?.length
    }
    return null
  }
  // computation
  @beforeCreate()
  public static async createShareCode(user: User) {
    user.shareCode =
      Math.random().toString(36).substring(2, 6) + Math.random().toString(36).substring(2, 6)
  }

  @beforeFind()
  public static async getLatestGomamaVerificationRequestsFind(
    query: ModelQueryBuilderContract<typeof User>
  ) {
    query.preload('latestGomamaVerificationRequest', (veriReqQuery) => {
      veriReqQuery.orderBy('created_at', 'desc').where('type', UserVerificationType.gomama).limit(1)
    })
  }

  @beforeFind()
  public static async getLatestSingpassVerificationRequestsFind(
    query: ModelQueryBuilderContract<typeof User>
  ) {
    query.preload('latestSingpassVerificationRequest', (veriReqQuery) => {
      veriReqQuery
        .orderBy('created_at', 'desc')
        .where('type', UserVerificationType.singpass)
        .limit(1)
    })
  }

  @beforeFetch()
  public static async getLatestGomamaVerificationRequestsFetch(
    query: ModelQueryBuilderContract<typeof User>
  ) {
    query.preload('latestGomamaVerificationRequest', (veriReqQuery) => {
      veriReqQuery.orderBy('created_at', 'desc').where('type', UserVerificationType.gomama).limit(1)
    })
  }

  @beforeFetch()
  public static async getLatestSingpassVerificationRequestsFetch(
    query: ModelQueryBuilderContract<typeof User>
  ) {
    query.preload('latestSingpassVerificationRequest', (veriReqQuery) => {
      veriReqQuery
        .orderBy('created_at', 'desc')
        .where('type', UserVerificationType.singpass)
        .limit(1)
    })
  }

  //TODO: TO BE REMOVED IN PROD
  @beforeSave()
  public static async hashPassword(user: User) {
    if (user.$dirty.password) {
      user.password = await Hash.make(user.password)
    }
  }

  @beforeCreate()
  public static async createUUID(user: User) {
    user.id = uuidv4()
  }

  // Get concated mobile number with country dial code value for comparison.
  public static queryComparefullPhoneNumber = scope((query, fullPhoneNumber: string) => {
    query.where(
      Database.raw("CONCAT('+',TRIM(country_dial_code),' ',TRIM(mobile_number))"),
      fullPhoneNumber
    )
  })
}

export const userFindFields = {
  fields: {
    pick: [
      'id',
      'username',
      'full_name',
      'email_address',
      'photo_url',
      'gender',
      'mobile_number',
      'full_mobile_number',
      'auth_provider',
      'birthday',
      'children_birthdays',
      'is_admin_verified',
      'is_email_address_verified',
      'is_mobile_number_verified',
      'is_gomama_verified',
      'is_singpass_verified',
      'latest_verify_selfie_fail_count',
    ],
  },
  relations: {
    favorite_listings: {
      fields: {
        pick: ['id'],
      },
    },
  },
}
