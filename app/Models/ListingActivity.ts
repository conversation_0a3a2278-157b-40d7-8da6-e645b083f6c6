import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, beforeCreate, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Activity from './Activity'
import Listing from './Listing'
import { v4 as uuidv4 } from 'uuid'

export default class ListingActivity extends BaseModel {
  @column({ isPrimary: true })
  public id: string

  @column()
  public activityId: string

  @column()
  public listingId: string

  @column({ consume: (value: boolean) => Boolean(value) })
  public isHidden: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Activity, { foreignKey: 'activityId', localKey: 'id' })
  public activity: BelongsTo<typeof Activity>

  @belongsTo(() => Listing, { foreignKey: 'listingId', localKey: 'id' })
  public listing: BelongsTo<typeof Listing>

  @beforeCreate()
  public static async createUUID(listingActivity: ListingActivity) {
    listingActivity.id = uuidv4()
  }
}
