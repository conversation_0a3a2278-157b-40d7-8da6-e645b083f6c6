import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
// import jwtDecode from 'jwt-decode'
// import jwt from 'jsonwebtoken'
import _ from 'lodash'
import Env from '@ioc:Adonis/Core/Env'

const requestHandler = (request: InternalAxiosRequestConfig) => {
  //   const apiKey = process.env.SYSTEM_ADMIN_API_KEY!

  //   if (apiKey !== null && typeof apiKey !== 'undefined') {
  //     if (request.headers) {
  //       request.headers['x-api-key'] = apiKey
  //     }
  //   }

  return request
}

const successHandler = (response: AxiosResponse) => {
  return response
}

const errorHandler = async (error: AxiosError<any>) => {
  //   const originalRequest = {
  //     _retry: false,
  //     ...error.config,
  //   }
  //   if (error?.response?.status === 403 && !originalRequest?._retry) {
  //     originalRequest._retry = true
  //     const access_token = await refreshAccessToken()
  //     axios.defaults.headers.common['Authorization'] = 'Bearer ' + access_token
  //     return backendClient(originalRequest)
  //   }

  return Promise.reject({
    message:
      error.response?.data?.errorCode ??
      error.response?.data?.message ??
      error.response?.data?.errors ??
      error.response?.data?.code ??
      error.response ??
      error,
  })
}

const meilisearchAdmin = axios.create({
  baseURL: Env.get('MEILISEARCH_BASE_URL'),
  headers: {
    Authorization: 'Bearer ' + process.env.MEILISEARCH_ADMIN_KEY,
  },
})

meilisearchAdmin.interceptors.request.use(requestHandler)
meilisearchAdmin.interceptors.response.use(
  (response) => successHandler(response),
  (error) => errorHandler(error)
)

export default meilisearchAdmin
