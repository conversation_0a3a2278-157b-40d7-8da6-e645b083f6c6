import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import _ from 'lodash'
import Env from '@ioc:Adonis/Core/Env'

const requestHandler = (request: InternalAxiosRequestConfig) => {
  return request
}

const successHandler = (response: AxiosResponse) => {
  return response
}

const errorHandler = async (error: AxiosError<any>) => {
  return Promise.reject({
    message:
      error.response?.data?.errorCode ??
      error.response?.data?.message ??
      error.response?.data?.errors ??
      error.response?.data?.code ??
      error.response ??
      error,
  })
}

const meilisearchClient = axios.create({
  baseURL: Env.get('MEILISEARCH_BASE_URL'),
  headers: {
    Authorization: 'Bearer ' + Env.get('MEILISEARCH_SEARCH_KEY'),
  },
})
meilisearchClient.interceptors.request.use(requestHandler)
meilisearchClient.interceptors.response.use(
  (response) => successHandler(response),
  (error) => errorHandler(error)
)

export default meilisearchClient
