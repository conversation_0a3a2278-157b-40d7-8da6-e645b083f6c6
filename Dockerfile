################## First Stage - Creating base #########################

# Created a variable to hold our node base image
ARG NODE_IMAGE=node:lts-alpine3.18

# Using the variable to create our base image
FROM $NODE_IMAGE AS base
# Running a command to install dumb-init to handle processes
RUN apk --no-cache add dumb-init
# Running a command to install python
# RUN apk --no-cache add python3 py3-pip
# Running a command to install utils
# RUN apk --no-cache add make gcc g++
# Running a command to install curl
RUN apk --no-cache add curl
# Creating folders and changing ownerships
RUN mkdir -p /home/<USER>/app/data && chown -R node:node /home/<USER>/app
# Setting the working directory
WORKDIR /home/<USER>/app
# Changing the current active user to "node"
USER node
# Creating a new folder "tmp"
RUN mkdir tmp

################## Second Stage - Installing dependencies ##########

# In this stage, we will start installing dependencies
FROM base AS dependencies
# We copy all package & lock files to the working directory
COPY --chown=node:node ./package.json ./
COPY --chown=node:node ./yarn.lock ./
# We run NPM CI to install the exact versions of dependencies
RUN yarn install --frozen-lockfile
# Lastly, we copy all the files with active user
COPY --chown=node:node . .

################## Third Stage - Building Stage ##############c#######

# In this stage, we will start building dependencies
FROM dependencies AS build
# We run "node ace build" to build the app for production
RUN node ace build --production --ignore-ts-errors

################## Final Stage - Production #########################

# In this final stage, we will start running the application
FROM base AS production
# Here, we include all the required environment variables
ENV NODE_ENV=production
# Copy all package & lock files to the working directory with active user
COPY --chown=node:node ./package.json ./
COPY --chown=node:node ./yarn.lock ./
# We run NPM CI to install the exact versions of dependencies
RUN yarn install --frozen-lockfile --production
# Copy files to the working directory from the build folder the user
COPY --chown=node:node --from=build /home/<USER>/app/build .
# Copy the Firebase service account key if it exists
COPY --chown=node:node ./serviceAccountKey.json ./serviceAccountKey.json
# Set environment variable for Firebase to find the service account key
ENV FIREBASE_SERVICE_ACCOUNT_PATH=/home/<USER>/app/serviceAccountKey.json
# Expose port
EXPOSE $PORT
# Run the command to start the server using "dumb-init"
CMD [ "dumb-init", "node", "server.js" ]

# command to build
# docker build --platform linux/amd64 -t gaincue/gomama-core .
# docker push gaincue/gomama-core
