name: Deployments
on:
  push:
    branches: [production, staging]

jobs:
  deploy-production:
    if: github.ref == 'refs/heads/production'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create Firebase Service Account Key
        run: |
          echo '${{ secrets.FIREBASE_SERVICE_ACCOUNT_KEY }}' > serviceAccountKey.json

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: gaincue/gomama-core
          tags: |
            type=raw,value=latest
            type=sha,prefix=,format=short # Adds short SHA tag for traceability

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Docker images
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: SSH and Deploy to Production
        uses: appleboy/ssh-action@master
        env:
          IMAGE_NAME: gaincue/gomama-core:latest
          COMPOSE_PATH: /home/<USER>/core
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            # Login to Docker Hub
            echo "${{ secrets.DOCKERHUB_TOKEN }}" | docker login --username "${{ secrets.DOCKERHUB_USERNAME }}" --password-stdin

            # Pull latest image
            docker pull "${{ env.IMAGE_NAME }}"

            # Navigate to compose directory
            cd "${{ env.COMPOSE_PATH }}"

            # Stop and remove existing containers
            docker compose down

            # Start containers
            docker compose up -d --remove-orphans

            # Run migrations
            docker compose exec -T gomama_core node ace migration:run --force

            # Clean up old images
            echo "Cleaning up old Docker images..."
            docker image prune -f --filter "until=24h" # Removes images older than 24h
            docker system prune -f --volumes # Cleans unused volumes and networks

  deploy-staging:
    if: github.ref == 'refs/heads/staging'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create Firebase Service Account Key
        run: |
          echo '${{ secrets.FIREBASE_SERVICE_ACCOUNT_KEY }}' > serviceAccountKey.json

      - name: Docker meta (staging)
        id: meta-staging
        uses: docker/metadata-action@v5
        with:
          images: gaincue/gomama-core
          tags: |
            type=raw,value=staging
            type=sha,prefix=,format=short

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Docker image (staging)
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          platforms: linux/arm64
          push: true
          tags: ${{ steps.meta-staging.outputs.tags }}
          labels: ${{ steps.meta-staging.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: SSH and Deploy to Staging
        uses: appleboy/ssh-action@master
        env:
          IMAGE_NAME: gaincue/gomama-core:staging
          COMPOSE_PATH: /home/<USER>/core # Assuming same path, adjust if needed
        with:
          host: ${{ secrets.SSH_HOST_STAGING }}
          username: ${{ secrets.SSH_USERNAME_STAGING }}
          key: ${{ secrets.SSH_KEY_STAGING }}
          script: |
            echo "${{ secrets.DOCKERHUB_TOKEN }}" | docker login --username "${{ secrets.DOCKERHUB_USERNAME }}" --password-stdin
            docker pull "${{ env.IMAGE_NAME }}"
            cd "${{ env.COMPOSE_PATH }}"
            docker compose down
            docker compose up -d --remove-orphans
            docker compose exec -T gomama_core node ace migration:run --force
            echo "Cleaning up old Docker images..."
            docker image prune -f --filter "until=24h"
            docker system prune -f --volumes
