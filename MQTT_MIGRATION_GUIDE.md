# 🚀 MQTT Migration Guide

## Overview

This guide walks you through migrating from WebSocket to MQTT for reliable real-time communication in the GoMama platform.

## 🏗️ Architecture Changes

### Before (WebSocket)
```
AdonisJS → Redis Pub/Sub → Realtime Layer → WebSocket → Flutter
```

### After (MQTT)
```
AdonisJS → MQ<PERSON> Broker → Flutter (direct)
         ↗              ↘
Realtime Layer ← MQTT Broker → Flutter
```

## 📋 Migration Steps

### Step 1: Start MQTT Infrastructure

```bash
# Use the provided startup script
./scripts/start-mqtt.sh

# Or manually with Docker Compose
docker-compose -f docker-compose.mqtt.yml up -d
```

**Services Started:**
- ✅ EMQX MQTT Broker (ports 1883, 8083, 18083)
- ✅ Redis (for data storage)
- ✅ Realtime service (updated for MQTT)

### Step 2: Configure EMQX Authentication

1. **Access EMQX Dashboard**: http://localhost:18083
   - Username: `admin`
   - Password: `gomama2024!`

2. **Create Authentication**:
   - Go to `Access Control` → `Authentication`
   - Create built-in database authentication
   - Add users for AdonisJS and Flutter clients

3. **Set Up Authorization**:
   - Go to `Access Control` → `Authorization`
   - Configure topic-based permissions

### Step 3: Update Environment Variables

```bash
# Copy and update environment file
cp .env.example .env
```

**Key MQTT Variables:**
```env
MQTT_BROKER_HOST=localhost
MQTT_BROKER_PORT=1883
MQTT_CLIENT_ID=gomama_realtime_server
MQTT_USERNAME=realtime_client
MQTT_PASSWORD=realtime_pass
```

### Step 4: Test MQTT Connection

```bash
# Install MQTT client for testing
npm install -g mqtt

# Test publish
mqtt pub -h localhost -p 1883 -t 'test/topic' -m 'Hello MQTT!'

# Test subscribe
mqtt sub -h localhost -p 1883 -t 'test/topic'
```

## 📡 MQTT Topic Structure

### Listing Topics
```
gomama/listings/status/{listing_id}        # QoS 1, Retained
gomama/listings/availability/{listing_id}  # QoS 1, Retained
```

### Session Topics
```
gomama/sessions/created/{session_id}       # QoS 1
gomama/sessions/updated/{session_id}       # QoS 1
gomama/sessions/ended/{session_id}         # QoS 1
gomama/sessions/cleanup/{session_id}       # QoS 1
```

### User Topics
```
gomama/users/notifications/{user_id}       # QoS 1
gomama/users/sessions/{user_id}            # QoS 1, Retained
```

### System Topics
```
gomama/system/health                       # QoS 0
gomama/system/metrics                      # QoS 0
```

## 🔧 Code Changes Required

### AdonisJS Backend Changes

1. **Install MQTT Client**:
```bash
npm install mqtt @types/mqtt
```

2. **Create MQTT Service**:
```typescript
// app/Services/MqttService.ts
import mqtt from 'mqtt'

export class MqttService {
  private client: mqtt.MqttClient
  
  constructor() {
    this.client = mqtt.connect('mqtt://localhost:1883', {
      clientId: 'gomama_adonisjs',
      username: 'adonisjs_client',
      password: 'adonisjs_pass'
    })
  }
  
  async publishSessionCreated(sessionId: string, sessionData: any) {
    const topic = `gomama/sessions/created/${sessionId}`
    const message = {
      timestamp: Date.now(),
      source: 'adonisjs',
      type: 'session_created',
      data: sessionData
    }
    
    this.client.publish(topic, JSON.stringify(message), { qos: 1 })
  }
  
  async publishListingStatus(listingId: string, status: string) {
    const topic = `gomama/listings/status/${listingId}`
    const message = {
      timestamp: Date.now(),
      source: 'adonisjs',
      type: 'status_update',
      data: { listingId, status }
    }
    
    this.client.publish(topic, JSON.stringify(message), { qos: 1, retain: true })
  }
}
```

3. **Update Controllers**:
```typescript
// Replace Redis pub/sub with MQTT
const mqttService = new MqttService()

// Instead of: Redis.publish('channel', data)
await mqttService.publishSessionCreated(sessionId, sessionData)
```

### Flutter App Changes

1. **Install MQTT Client**:
```yaml
# pubspec.yaml
dependencies:
  mqtt_client: ^10.0.0
```

2. **Create MQTT Service**:
```dart
// lib/services/mqtt_service.dart
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';

class MqttService {
  late MqttServerClient client;
  
  Future<void> connect() async {
    client = MqttServerClient('localhost', 'flutter_client');
    client.port = 1883;
    client.keepAlivePeriod = 60;
    client.autoReconnect = true;
    
    await client.connect('flutter_user', 'flutter_pass');
  }
  
  void subscribeToUserNotifications(String userId) {
    final topic = 'gomama/users/notifications/$userId';
    client.subscribe(topic, MqttQos.atLeastOnce);
  }
  
  void subscribeToListingStatus(String listingId) {
    final topic = 'gomama/listings/status/$listingId';
    client.subscribe(topic, MqttQos.atLeastOnce);
  }
}
```

3. **Replace WebSocket Logic**:
```dart
// Replace WebSocket connection with MQTT
final mqttService = MqttService();
await mqttService.connect();
mqttService.subscribeToUserNotifications(userId);
```

## 🔄 Migration Timeline

### Phase 1: Infrastructure Setup (Day 1)
- ✅ Deploy EMQX broker
- ✅ Update realtime service
- ✅ Configure authentication

### Phase 2: Backend Integration (Day 2-3)
- 🔄 Update AdonisJS to publish MQTT messages
- 🔄 Test MQTT message flow
- 🔄 Parallel run with existing Redis pub/sub

### Phase 3: Flutter Integration (Day 4-5)
- ⏳ Update Flutter app MQTT client
- ⏳ Test real-time functionality
- ⏳ Performance testing

### Phase 4: Cutover (Day 6)
- ⏳ Switch Flutter to MQTT
- ⏳ Disable WebSocket endpoints
- ⏳ Monitor and optimize

## 📊 Testing & Validation

### MQTT Broker Health
```bash
# Check EMQX status
curl http://localhost:18083/api/v5/status

# Check connected clients
curl -u admin:gomama2024! http://localhost:18083/api/v5/clients
```

### Message Flow Testing
```bash
# Test topic subscription
mqtt sub -h localhost -p 1883 -t 'gomama/+/+/+'

# Test message publishing
mqtt pub -h localhost -p 1883 -t 'gomama/test' -m '{"test": true}'
```

### Performance Monitoring
- Monitor EMQX dashboard for connection metrics
- Check message throughput and latency
- Monitor memory usage and connection counts

## 🚨 Rollback Plan

If issues occur during migration:

1. **Immediate Rollback**:
```bash
# Switch back to WebSocket
docker-compose -f docker-compose.yml up -d
```

2. **Gradual Rollback**:
- Keep MQTT running alongside WebSocket
- Switch Flutter clients back to WebSocket
- Disable MQTT publishing in AdonisJS

## 📈 Expected Benefits

- ✅ **Reliability**: QoS guarantees message delivery
- ✅ **Offline Support**: Retained messages for offline clients
- ✅ **Battery Life**: More efficient than WebSocket on mobile
- ✅ **Scalability**: MQTT broker handles connection management
- ✅ **Monitoring**: Better visibility into message flow

## 🎯 Success Metrics

- Message delivery rate > 99.9%
- Connection recovery time < 5 seconds
- Mobile battery usage reduction > 20%
- Reduced support tickets for "missed messages"

## 🆘 Troubleshooting

### Common Issues

1. **MQTT Connection Failed**:
   - Check broker is running: `docker ps`
   - Verify credentials in EMQX dashboard
   - Check firewall/network settings

2. **Messages Not Received**:
   - Verify topic subscription patterns
   - Check QoS levels match
   - Monitor EMQX logs

3. **High Memory Usage**:
   - Adjust retained message limits
   - Configure message expiry
   - Monitor connection counts

### Useful Commands

```bash
# View EMQX logs
docker-compose -f docker-compose.mqtt.yml logs emqx

# View realtime service logs
docker-compose -f docker-compose.mqtt.yml logs gomama_realtime

# Connect to EMQX container
docker-compose -f docker-compose.mqtt.yml exec emqx /bin/bash
```

## 📞 Support

For migration support:
1. Check EMQX documentation: https://www.emqx.io/docs/
2. Review MQTT client libraries documentation
3. Monitor system metrics during migration
4. Test thoroughly in staging environment first
