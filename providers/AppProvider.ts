import type { ApplicationContract } from '@ioc:Adonis/Core/Application'
import { setupFirebase } from 'App/Services/FirebaseMessageService'
import Env from '@ioc:Adonis/Core/Env'

export default class AppProvider {
  constructor(protected app: ApplicationContract) {}

  public register() {
    // Register your own bindings
  }

  public async boot() {
    // IoC container is ready
  }

  public async ready() {
    if (Env.get('NODE_ENV') === 'test') {
      return
    }

    // App is ready
    const { default: RedisService } = await import('App/Services/RedisService')
    new RedisService()

    setupFirebase()

    const { default: startBullMQWorkers } = await import('App/Services/BullMQService')
    await startBullMQWorkers()

    const scheduler = this.app.container.use('Adonis/Addons/Scheduler')
    scheduler.run()
  }

  public async shutdown() {
    // Cleanup, since app is going down
  }
}
