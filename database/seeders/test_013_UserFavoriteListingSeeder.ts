import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Listing from 'App/Models/Listing'
import User from 'App/Models/User'
import UserFavoriteListing from 'App/Models/UserFavoriteListing'

export default class extends BaseSeeder {
  public static environment = ['test']
  public async run() {
    const adminUser = await User.findByOrFail('email_address', '<EMAIL>')
    const listing1 = await Listing.findByOrFail('name', 'Go!mama Pod SKCC001')
    const listing2 = await Listing.findByOrFail('name', 'Go!mama Pod 106')

    await UserFavoriteListing.createMany([
      {
        listingId: listing1.id,
        userId: adminUser.id,
      },
      {
        listingId: listing2.id,
        userId: adminUser.id,
      },
    ])
  }
}
