import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Activity from 'App/Models/Activity'
import { dirName, importData } from 'App/utils/dataImport'

export default class extends BaseSeeder {
  public static environment = ['test']
  public async run() {
    const newRowArr: any[] = await importData(dirName.activity)

    await Activity.createMany(
      newRowArr.concat([
        {
          name: 'To be updated Activity 1',
          description: 'To be updated Activity 1 demostration',
          imageUrl: 'demo_update_activity_1',
          isHidden: false,
        },
        {
          name: 'To be updated Activity 2',
          description: 'To be updated Activity 2 demostration',
          imageUrl: 'demo_update_activity_2',
          isHidden: false,
        },
        {
          name: 'To be updated Activity 3',
          description: 'To be updated Activity 3 demostration',
          imageUrl: 'demo_update_activity_3',
          isHidden: false,
        },
        {
          name: 'To be updated Activity 4',
          description: 'To be updated Activity 4 demostration',
          imageUrl: 'demo_update_activity_4',
          isHidden: false,
        },
        {
          name: 'To be deleted Activity 1',
          description: 'To be deleted Activity 1 demostration',
          imageUrl: 'demo_delete_activity_1',
          isHidden: false,
        },
        {
          name: 'To be deleted Activity 2',
          description: 'To be deleted Activity 2 demostration',
          imageUrl: 'demo_delete_activity_2',
          isHidden: false,
        },
      ])
    )
  }
}
