import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Listing from 'App/Models/Listing'
import ListingFlag from 'App/Models/ListingFlag'
import User from 'App/Models/User'
import { ListingFlagAction, ListingFlagCategory } from 'Contracts/listing_flag_type'
import { DateTime } from 'luxon'
import Position from 'App/Models/Position'
import Region from 'App/Models/Region'
import { ListingType } from 'Contracts/listing_type'
import { v4 as uuidv4 } from 'uuid'
import * as ngeohash from 'ngeohash'
import Database from '@ioc:Adonis/Lucid/Database'

export default class extends BaseSeeder {
  public static environment = ['test']
  public async run() {
    const adminUser = await User.findByOrFail('emailAddress', '<EMAIL>')
    const normalUser = await User.findByOrFail('emailAddress', '<EMAIL>')
    const normalUser2 = await User.findByOrFail('emailAddress', '<EMAIL>')
    const adminUser2 = await User.findByOrFail('emailAddress', '<EMAIL>')

    const regions = await Region.query()

    // Position 1
    const lon1 = 105.7877
    const lat1 = 1.269739
    const geohash = ngeohash.encode(lon1, lat1, 20)
    const newPositionId = uuidv4()
    await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId, geohash, lon1, lat1]
    )
    const position1 = await Position.findOrFail(newPositionId)

    // Position 2
    const lon2 = 106.7877
    const lat2 = 1.367339
    const geohash2 = ngeohash.encode(lon2, lat2, 20)
    const newPositionId2 = uuidv4()
    await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId2, geohash2, lon2, lat2]
    )
    const position2 = await Position.findOrFail(newPositionId)

    // Position 3
    const lon3 = 102.7877
    const lat3 = 1.347899
    const geohash3 = ngeohash.encode(lon3, lat3, 20)
    const newPositionId3 = uuidv4()
    await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId3, geohash3, lon3, lat3]
    )
    const position3 = await Position.findOrFail(newPositionId)

    // Demo Listing
    await Listing.createMany([
      {
        positionId: position1.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Listing Flag Listing 1',
        listingType: ListingType.care,
        companyName: `Sentosa Development Corporation (SDC) Unverified`,
        addressName: 'Resorts World Station Level 1, Near Lift Unverified',
        description: 'Resorts World Station Level 1, Near Lift Unverified',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: ['care', 'go!mama', 'breastfeeding'],
        postalCode: '098138',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey: '',
        lockId: '',
        lockMasterPin: '',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: false,
        isHidden: false,
        firestoreId: 'flaggedListing1',
      },
      {
        positionId: position2.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Listing Flag Listing 2 - to be hidden',
        listingType: ListingType.care,
        companyName: `Sentosa Development Corporation (SDC) Unverified`,
        addressName: 'Resorts World Station Level 1, Near Lift Unverified',
        description: 'Resorts World Station Level 1, Near Lift Unverified',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: ['care', 'go!mama', 'breastfeeding'],
        postalCode: '098138',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey: '',
        lockId: '',
        lockMasterPin: '',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: false,
        isHidden: false,
        firestoreId: 'flaggedListing2',
      },
      {
        positionId: position3.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Listing Flag Listing 3 - to be updated with no action',
        listingType: ListingType.care,
        companyName: `Sentosa Development Corporation (SDC) Unverified`,
        addressName: 'Resorts World Station Level 1, Near Lift Unverified',
        description: 'Resorts World Station Level 1, Near Lift Unverified',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: ['care', 'go!mama', 'breastfeeding'],
        postalCode: '098138',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey: '',
        lockId: '',
        lockMasterPin: '',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: false,
        isHidden: false,
        firestoreId: 'flaggedListing3',
      },
    ])

    const listing1 = await Listing.findByOrFail('name', 'Listing Flag Listing 1')
    const listing2 = await Listing.findByOrFail('name', 'Listing Flag Listing 2 - to be hidden')
    const listing3 = await Listing.findByOrFail(
      'name',
      'Listing Flag Listing 3 - to be updated with no action'
    )

    // Listing Flag
    await ListingFlag.createMany([
      {
        listingId: listing1.id,
        userId: normalUser.id,
        reason: 'Demo listing flag 1',
        category: ListingFlagCategory.safetyHazard,
      },
      // Demo Update Listing Flag 2 (hide listing action)
      {
        listingId: listing2.id,
        userId: normalUser.id,
        reason:
          'Demo listing flag update for listing 2 - will be updated with action hide listing 1',
        category: ListingFlagCategory.safetyHazard,
      },
      {
        listingId: listing2.id,
        userId: normalUser2.id,
        reason:
          'Demo listing flag update for listing 2 - will be updated with action hide listing and generic reason 2',
        category: ListingFlagCategory.safetyHazard,
      },
      {
        listingId: listing2.id,
        userId: adminUser2.id,
        reason:
          'Demo listing flag update for listing 2 - will be updated with action hide listing and generic reason 3',
        category: ListingFlagCategory.safetyHazard,
      },
      {
        listingId: listing2.id,
        userId: normalUser.id,
        reason:
          'Demo listing flag update for listing 2 - should remain unchanged, after newer unreviewed flags with same listing has been reviewed',
        category: ListingFlagCategory.safetyHazard,
        reviewedAt: DateTime.now(),
        action: ListingFlagAction.noAction,
        actionReason: 'Demo listing flag 2 reviewed and action reason',
        actionBy: adminUser.id,
      },

      // Demo Update Listing Flag 3 (no action)
      {
        listingId: listing3.id,
        userId: normalUser.id,
        reason: 'Demo listing flag update 3 - will be updated with no action',
        category: ListingFlagCategory.safetyHazard,
      },
      {
        listingId: listing3.id,
        userId: normalUser2.id,
        reason:
          'Demo listing flag update 3 - should remain unchanged, because there is no action for other unreviewed flags with same listing(not hidden)',
        category: ListingFlagCategory.safetyHazard,
      },

      // Demo already reviewed listing flags
      {
        listingId: listing1.id,
        userId: normalUser.id,
        reason:
          'Demo already reviewed listing flags - should not be able to submit another review again.',
        category: ListingFlagCategory.safetyHazard,
        reviewedAt: DateTime.now(),
        action: ListingFlagAction.noAction,
        actionReason: 'Demo already reviewed listing flags',
        actionBy: adminUser.id,
      },

      // Demo delete listing flag
      {
        listingId: listing1.id,
        userId: normalUser.id,
        reason: 'Demo delete listing flag.',
        category: ListingFlagCategory.safetyHazard,
        reviewedAt: DateTime.now(),
        action: ListingFlagAction.noAction,
        actionReason: 'Demo delete listing flag.',
        actionBy: adminUser.id,
      },

      // Demo already has an existing unreviewed listing flag for a listing
      {
        listingId: listing1.id,
        userId: normalUser.id,
        reason: 'Demo already has an existing unreviewed listing flag for a listing',
        category: ListingFlagCategory.safetyHazard,
      },
    ])
  }
}
