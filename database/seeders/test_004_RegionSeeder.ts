import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Region from 'App/Models/Region'
import { dirName, importData } from 'App/utils/dataImport'

export default class extends BaseSeeder {
  public static environment = ['test']
  public async run() {
    const newRowArr: any[] = await importData(dirName.regions)
    const regions = [
      'to delete region',
      'to delete region slug',
      'to update region',
      'to update region slug',
      'middle',
    ]
    const demoRegions = regions.map((region) => {
      return { name: region, imageUrl: `${region}.jpg`, description: region + ''.toLowerCase() }
    })

    await Region.createMany(newRowArr.concat(demoRegions))
  }
}
