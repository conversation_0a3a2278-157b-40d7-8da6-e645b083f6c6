import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import User from 'App/Models/User'
import { UserType } from 'Contracts/user_type'

export default class extends BaseSeeder {
  public static environment = ['development', 'test']
  public async run() {
    await User.firstOrCreate(
      {
        emailAddress: '<EMAIL>',
      },
      {
        emailAddress: '<EMAIL>',
        password: 'abcd1234',
        userType: UserType.admin,
      }
    )
  }
}