import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Listing from 'App/Models/Listing'
import User from 'App/Models/User'
import { DateTime } from 'luxon'
import Position from 'App/Models/Position'
import Region from 'App/Models/Region'
import { ListingType } from 'Contracts/listing_type'
import { v4 as uuidv4 } from 'uuid'
import * as ngeohash from 'ngeohash'
import Database from '@ioc:Adonis/Lucid/Database'
import Session from 'App/Models/Session'

export default class extends BaseSeeder {
  public static environment = ['test']
  public async run() {
    const adminUser = await User.findByOrFail('emailAddress', '<EMAIL>')
    const normalUser = await User.findByOrFail('emailAddress', '<EMAIL>')
    const normalUser2 = await User.findByOrFail('emailAddress', '<EMAIL>')
    // const normalUser3 = await User.findByOrFail('emailAddress', '<EMAIL>')
    const normalUser4 = await User.findByOrFail('emailAddress', '<EMAIL>')
    const normalUser5 = await User.findByOrFail('emailAddress', '<EMAIL>')
    const normalUser6 = await User.findByOrFail('emailAddress', '<EMAIL>')

    const regions = await Region.query()

    // Position 1
    const lon1 = 110.7567
    const lat1 = 1.278739
    const geohash = ngeohash.encode(lon1, lat1, 20)
    const newPositionId = uuidv4()
    await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId, geohash, lon1, lat1]
    )
    const position1 = await Position.findOrFail(newPositionId)

    // Position 2
    const lon2 = 115.7567
    const lat2 = 1.573239
    const geohash2 = ngeohash.encode(lon2, lat2, 20)
    const newPositionId2 = uuidv4()
    await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId2, geohash2, lon2, lat2]
    )
    const position2 = await Position.findOrFail(newPositionId)

    // Position 3
    const lon3 = 112.7877
    const lat3 = 1.547669
    const geohash3 = ngeohash.encode(lon3, lat3, 20)
    const newPositionId3 = uuidv4()
    await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId3, geohash3, lon3, lat3]
    )
    const position3 = await Position.findOrFail(newPositionId)

    // Demo Listing
    await Listing.createMany([
      {
        positionId: position1.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Listing With No Lock',
        listingType: ListingType.gomama,
        companyName: `Sentosa Development Corporation (SDC) Unverified`,
        addressName: 'Resorts World Station Level 1, Near Lift Unverified',
        description: 'Resorts World Station Level 1, Near Lift Unverified',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: ['care', 'go!mama', 'breastfeeding'],
        postalCode: '098138',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey: '',
        lockId: '',
        lockMasterPin: '',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: false,
        isHidden: false,
        firestoreId: 'listingWithNoLock',
      },
      {
        positionId: position2.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Demo Session Listing With Lock Id',
        listingType: ListingType.gomama,
        companyName: `Sentosa Development Corporation (SDC) Unverified`,
        addressName: 'Resorts World Station Level 1, Near Lift Unverified',
        description: 'Resorts World Station Level 1, Near Lift Unverified',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: ['care', 'go!mama', 'breastfeeding'],
        postalCode: '098138',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey:
          'dNbUvh58GZ1y7FbL53eAOFMP2UOiyBOQap3BiCSeTzRjAkAnot8mz2D6ihZ9nsVZBcAmwxhHwuwNMt9ivGaUhRGycCf5IT815Vln7I1xiUFtfpcqS6WWFvEMTcbUmZwL',
        lockId: 'IGB407f66a51',
        lockMasterPin: '11856068',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: false,
        isHidden: false,
        firestoreId: 'demoSessionListingWithLock',
      },
      {
        positionId: position3.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Non-gomama Listing Type',
        listingType: ListingType.care,
        companyName: `Sentosa Development Corporation (SDC) Unverified`,
        addressName: 'Resorts World Station Level 1, Near Lift Unverified',
        description: 'Resorts World Station Level 1, Near Lift Unverified',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: ['care', 'go!mama', 'breastfeeding'],
        postalCode: '098138',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey: '',
        lockId: '',
        lockMasterPin: '',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: false,
        isHidden: false,
        firestoreId: 'NongomamaListingType',
      },
    ])

    const listing1 = await Listing.findByOrFail('name', 'Go!mama Pod SKCC001')
    const listing2 = await Listing.findByOrFail('name', 'Go!mama Pod 106')
    const listing3 = await Listing.findByOrFail('name', 'Demo Update Listing 1')
    const listing4 = await Listing.findByOrFail('name', 'Listing with 0 extension')

    // Listing Session
    await Session.createMany([
      // Demo normal user 2 already has an on-going session, is
      {
        listingId: listing2.id,
        userId: normalUser2.id,
        lockBluetoothGuestKey: 'demo',
        lockCustomPin: 'custompin',
        lockDailyPin: 'demo',
        lockHourlyPin: 'demo',
        lockOneTimePin: 'demo',
        startedAt: DateTime.now(),
        expectedEndedAt: DateTime.now().plus({ minutes: 30 }),
      },
      {
        listingId: listing1.id,
        userId: normalUser4.id,
        lockBluetoothGuestKey: 'demo',
        lockCustomPin: 'custompin',
        lockDailyPin: 'demo',
        lockHourlyPin: 'demo',
        lockOneTimePin: 'demo',
        startedAt: DateTime.now(),
        expectedEndedAt: DateTime.now().plus({ minutes: 30 }),
      },
      {
        listingId: listing3.id,
        userId: normalUser5.id,
        lockBluetoothGuestKey: 'demo',
        lockCustomPin: 'custompin',
        lockDailyPin: 'demo',
        lockHourlyPin: 'demo',
        lockOneTimePin: 'demo',
        startedAt: DateTime.now(),
        expectedEndedAt: DateTime.now().plus({ minutes: 30 }),
      },
      {
        listingId: listing4.id,
        userId: normalUser6.id,
        lockBluetoothGuestKey: 'demo',
        lockCustomPin: 'custompin',
        lockDailyPin: 'demo',
        lockHourlyPin: 'demo',
        lockOneTimePin: 'demo',
        startedAt: DateTime.now(),
        expectedEndedAt: DateTime.now().plus({ minutes: 30 }),
      },

      // Demo already ended session
      {
        listingId: listing2.id,
        userId: normalUser2.id,
        lockBluetoothGuestKey: 'demo',
        lockCustomPin: 'custompin',
        lockDailyPin: 'demo',
        lockHourlyPin: 'demo',
        lockOneTimePin: 'demo',
        startedAt: DateTime.now(),
        expectedEndedAt: DateTime.now(),
        actualEndedAt: DateTime.now(),
      },

      // Demo to be hidden session
      {
        listingId: listing2.id,
        userId: normalUser2.id,
        lockBluetoothGuestKey: 'demo',
        lockCustomPin: 'custompin',
        lockDailyPin: 'demo',
        lockHourlyPin: 'demo',
        lockOneTimePin: 'demo',
        startedAt: DateTime.now(),
        expectedEndedAt: DateTime.now(),
        actualEndedAt: DateTime.now(),
      },
    ])
  }
}
