import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Listing from 'App/Models/Listing'
import MeilisearchService from 'App/Services/meilisearch'
import { AxiosResponse } from 'axios'

export default class extends BaseSeeder {
  public static environment = ['development']
  public async run() {
    // Write your database queries inside the run method
    const listings = await Listing.query()
      .preload('amenities')
      .preload('position')
      .preload('listingRatings')
      .withAggregate('listingRatings', (query) => {
        query.avg('experience_rating').as('average_experience_ratings')
      })

    // await MeilisearchService.admin.deleteIndex('listings') // to delete the listing index, needed when decided to repopulate meili data stored.
    await MeilisearchService.admin.createIndex({ uid: `listings`, primaryKey: 'id' })

    // Update Meili filterable attributes
    await MeilisearchService.admin.updateFilterableAttributes(`listings`, [
      'id',
      'listing_name',
      'listing_type',
      'listing_keywords',
      'listing_is_verified',
      'listing_is_hidden',
      'listing_status',
      'listing_description',
      'amenities',
      '_geo',
      'listing_firestore_id',
    ])

    // Update Meili sortable attributes
    await MeilisearchService.admin.updateSortableAttributes(`listings`, [
      'listing_name',
      'listing_created_at',
      'average_experience_ratings',
      '_geo',
    ])

    let promises: Promise<AxiosResponse<any, any>>[] = []

    // Add/Update a document within an index, Meili auto detect if a document(row) already exist.
    for (let listing of listings) {
      const searchDocuments = [
        {
          id: listing.id,
          listing_name: listing.name,
          listing_created_at: listing.createdAt,
          listing_type: listing.listingType,
          listing_keywords: listing.keywords,
          listing_is_verified: listing.isVerified,
          listing_is_hidden: listing.isHidden,
          listing_status: listing.status ?? null,
          listing_description: listing.description ?? null,
          amenities: listing.amenities.map((amenity) => amenity.id),
          average_experience_ratings: listing.$extras.average_experience_ratings,
          // _geo is a reserved field. If you include it in your documents, Meilisearch expects its value to conform to a specific format.
          _geo: listing.position
            ? {
                lat: listing.position.coordinate.y,
                lng: listing.position.coordinate.x,
              }
            : null,
          listing_firestore_id: listing.firestoreId == '' ? null : listing.firestoreId,
        },
      ]
      promises.push(MeilisearchService.admin.addOrUpdateDocument(`listings`, searchDocuments))
    }

    // Submit create/update documents request to Meili
    await Promise.all(promises)
  }
}
