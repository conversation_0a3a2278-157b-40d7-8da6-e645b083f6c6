import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import AppVersionControl from 'App/Models/AppVersionControl'
import User from 'App/Models/User'
import { DateTime } from 'luxon'

export default class extends BaseSeeder {
  public static environment = ['test']
  public async run() {
    const adminUser = await User.findByOrFail('email_address', '<EMAIL>')
    await AppVersionControl.createMany([
      {
        addedBy: adminUser.id,
        description: 'v1 release',
        publishedAt: DateTime.now(),
        publishedBy: adminUser.id,
        version: '1.0.0',
      },
      {
        addedBy: adminUser.id,
        description: 'v2 release',
        publishedAt: DateTime.now(),
        publishedBy: adminUser.id,
        version: '2.0.0',
      },
      {
        addedBy: adminUser.id,
        description: 'v3 release',
        publishedAt: DateTime.now(),
        publishedBy: adminUser.id,
        version: '3.0.0',
      },
      {
        addedBy: adminUser.id,
        description: 'v4 release',
        publishedAt: DateTime.now(),
        publishedBy: adminUser.id,
        version: '4.0.0',
      },
      {
        addedBy: adminUser.id,
        description: 'v5 release',
        publishedAt: DateTime.now(),
        publishedBy: adminUser.id,
        version: '5.0.0',
      },
      {
        addedBy: adminUser.id,
        description: 'v6 release',
        version: '6.0.0',
      },
    ])
  }
}
