import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Amenity from 'App/Models/Amenity'
import { dirName, importData } from 'App/utils/dataImport'

export default class extends BaseSeeder {
  public static environment = ['test']
  public async run() {
    const newRowArr: any[] = await importData(dirName.amenity)

    await Amenity.createMany(
      newRowArr.concat([
        {
          name: 'To be updated Amenity 1',
          description: 'To be updated Amenity 1 demostration',
          fontIconName: 'demo_update_amenity_1',
          isHidden: false,
        },
        {
          name: 'To be updated Amenity 2',
          description: 'To be updated Amenity 2 demostration',
          fontIconName: 'demo_update_amenity_2',
          isHidden: false,
        },
        {
          name: 'To be updated Amenity 3',
          description: 'To be updated Amenity 3 demostration',
          fontIconName: 'demo_update_amenity_3',
          isHidden: false,
        },
        {
          name: 'To be updated Amenity 4',
          description: 'To be updated Amenity 4 demostration',
          fontIconName: 'demo_update_amenity_4',
          isHidden: false,
        },
        {
          name: 'To be deleted Amenity 1',
          description: 'To be deleted Amenity 1 demostration',
          fontIconName: 'demo_delete_amenity_1',
          isHidden: false,
        },
        {
          name: 'To be deleted Amenity 2',
          description: 'To be deleted Amenity 2 demostration',
          fontIconName: 'demo_delete_amenity_2',
          isHidden: false,
        },
      ])
    )
  }
}
