import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import UserFavoriteListing from 'App/Models/UserFavoriteListing'
import { dirName, importData } from 'App/utils/dataImport'

export default class extends BaseSeeder {
  public static environment = ['development']
  public async run() {
    const newRowArr: any[] = await importData(dirName.userListings)

    await UserFavoriteListing.createMany(newRowArr)
  }
}
