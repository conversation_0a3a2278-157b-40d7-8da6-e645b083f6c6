import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Listing from 'App/Models/Listing'
import ListingFlag from 'App/Models/ListingFlag'
import User from 'App/Models/User'
import { ListingFlagAction, ListingFlagCategory } from 'Contracts/listing_flag_type'
import { DateTime } from 'luxon'

export default class extends BaseSeeder {
  public static environment = ['development']
  public async run() {
    const anyListing = await Listing.query().first()
    const anyUser = await User.query().where('user_type', 'user').first()
    const anyAdmin = await User.query().where('user_type', 'admin').first()

    await ListingFlag.createMany([
      {
        listingId: anyListing?.id, // This listing is hidden, this is used to mock
        userId: anyUser?.id, // Assume this user submitted the flag
        category: ListingFlagCategory.safetyHazard,
        reason: 'For unknown reason, the power socket is exposed in the pod.',
        reviewedAt: DateTime.now(),
        action: ListingFlagAction.hideListing, // assume user decide to hide the listing
        actionReason: 'After live inspection, the report is proven to be true.',
        actionBy: anyAdmin?.id, // Is one of the gomama staff
      },
    ])
  }
}
