import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'profiles'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.enum('social_type', ['google', 'facebook', 'apple', 'shopify']).alter()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.enum('social_type', ['google', 'facebook', 'apple']).alter()
    })
  }
}
