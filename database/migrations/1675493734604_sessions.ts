import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'sessions'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      table.uuid('listing_id').references('id').inTable('listings').onDelete('SET NULL')
      table.uuid('user_id').references('id').inTable('users').onDelete('SET NULL')
      table.text('lock_bluetooth_guest_key')
      table.string('lock_custom_pin')
      table.string('lock_daily_pin')
      table.string('lock_hourly_pin')
      table.string('lock_one_time_pin')
      table.timestamp('started_at').nullable()
      table.timestamp('expected_ended_at').nullable()
      table.timestamp('actual_ended_at').nullable()
      table.integer('number_of_usage_extensions').defaultTo(0)
      table.boolean('is_hidden').defaultTo(false)
      table.string('firestore_id').unique()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true }).defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
