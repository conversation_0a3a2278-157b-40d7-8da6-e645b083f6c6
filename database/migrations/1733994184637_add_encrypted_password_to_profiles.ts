import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'profiles'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.text('encrypted_password').nullable()
      table.string('encryption_iv', 32).nullable() // Store IV for decryption
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('encrypted_password')
      table.dropColumn('encryption_iv')
    })
  }
}
