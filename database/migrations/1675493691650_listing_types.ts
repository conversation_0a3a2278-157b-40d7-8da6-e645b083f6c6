import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'listing_types'

  public async up() {
    // this.schema.createTable(this.tableName, (table) => {
    //   table.uuid('id').primary()
    //   table.string('name').unique()
    //   table.string('description')
    //   table.integer('index').unique()
    //   table.boolean('is_hidden').defaultTo(false)
    //   /**
    //    * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
    //    */
    //   table.timestamp('created_at', { useTz: true })
    //   table.timestamp('updated_at', { useTz: true }).nullable()
    // })
  }

  public async down() {
    // this.schema.dropTable(this.tableName)
  }
}
