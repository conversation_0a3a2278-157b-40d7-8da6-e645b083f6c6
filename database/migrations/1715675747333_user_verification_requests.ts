import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'user_verification_requests'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      table.string('type').notNullable()
      table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE')
      table.string('gomama_user_verify_photo_url').nullable()
      table.string('gomama_first_name').nullable()
      table.string('gomama_last_name').nullable()
      table.timestamp('gomama_birthday', { useTz: true }).nullable()
      table.json('gomama_children_birthday').nullable()
      table.string('gomama_mobile_number').nullable()
      table.string('gomama_email').nullable()
      table.string('gomama_gender').nullable()
      table.string('singpass_mobile_number').nullable()
      table.string('singpass_email').nullable()
      table.string('singpass_birthday').nullable()
      table.string('singpass_nric').nullable()
      table.string('singpass_full_name').nullable()
      table.string('singpass_gender').nullable()
      table.json('singpass_children_birthday').nullable()
      table.timestamp('reviewed_at').nullable()
      table.string('status').defaultTo('pending')
      table.string('reason').nullable()
      table.uuid('review_by').references('id').inTable('users').onDelete('SET NULL')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
