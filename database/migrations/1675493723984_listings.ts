import BaseSchema from '@ioc:Adonis/Lucid/Schema'
import Drive from '@ioc:Adonis/Core/Drive'
import fs from 'fs'

export default class extends BaseSchema {
  protected tableName = 'listings'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      table.uuid('position_id').references('id').inTable('positions').onDelete('SET NULL')
      table.uuid('region_id').references('id').inTable('regions').onDelete('SET NULL')
      // table.uuid('type_id').references('id').inTable('listing_types').onDelete('SET NULL')
      table.uuid('verified_by').references('id').inTable('users').onDelete('SET NULL').nullable()
      table.uuid('suggested_by').references('id').inTable('users').onDelete('SET NULL').nullable()
      table.string('name')
      table.string('listing_type')
      table.string('company_name')
      table.string('address_name')
      table.string('description')
      table.string('full_address')
      table.string('contact_number')
      table.json('keywords')
      table.string('postal_code')
      table.json('usage_durations')
      table.json('usage_extension_durations')
      table.integer('max_number_of_usage_extensions')
      table.integer('number_of_private_feeding_rooms')
      table.integer('number_of_diaper_changing_mats')
      table.string('diaper_changing_mat_type')
      table.string('country_dial_code')
      table.decimal('humidity').defaultTo(0.0)
      table.decimal('temperature').defaultTo(0.0)
      table.string('opening_hours').defaultTo('Pending verification')
      table.string('pi_id')
      table.timestamp('pi_last_updated')
      table.string('api_key')
      table.string('lock_id')
      table.string('lock_master_pin')
      table.string('lock_bluetooth_admin_key')
      table.boolean('door_is_lockable').defaultTo(false)
      table.string('status').defaultTo('idle')
      table.boolean('is_usage_extendable').defaultTo(false)
      table.boolean('is_verified').defaultTo(false)
      table.boolean('is_hidden').defaultTo(true)
      table.string('firestore_id').unique()
      table.string('note').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true }).defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  public async down() {
    // Delete all old listing image created from import
    const listingImgPath = './image/listing'
    fs.readdir('./tmp/uploads/image/listing', async (_, files) => {
      files?.map(async (file) => {
        await Drive.use('local').delete(listingImgPath + '/' + file)
      })
    })

    this.schema.dropTable(this.tableName)
  }
}
