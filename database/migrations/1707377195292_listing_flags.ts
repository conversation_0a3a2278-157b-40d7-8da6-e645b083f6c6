import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'listing_flags'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      table.uuid('listing_id').references('id').inTable('listings').onDelete('CASCADE')
      table.uuid('user_id').references('id').inTable('users').onDelete('SET NULL')
      table.string('reason')
      table.string('category')
      table.timestamp('reviewed_at').nullable()
      table.string('action')
      table.string('action_reason')
      table.uuid('action_by').references('id').inTable('users').onDelete('SET NULL')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true }).defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
