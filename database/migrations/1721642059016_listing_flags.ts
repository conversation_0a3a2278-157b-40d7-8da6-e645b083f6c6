import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'listing_flags'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.json('reference_images').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('reference_images')
    })
  }
}
