import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'users'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropUnique(['mobile_number'])
      table.unique(['mobile_number', 'country_dial_code'])
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.unique(['mobile_number'])
      table.dropUnique(['mobile_number', 'country_dial_code'])
    })
  }
}
