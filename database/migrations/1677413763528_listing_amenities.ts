import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'listing_amenities'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      table.uuid('amenity_id').references('id').inTable('amenities').onDelete('CASCADE')
      table.uuid('listing_id').references('id').inTable('listings').onDelete('CASCADE')
      table.boolean('is_hidden').defaultTo(false)
      table.unique(['listing_id', 'amenity_id'])

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true }).defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
