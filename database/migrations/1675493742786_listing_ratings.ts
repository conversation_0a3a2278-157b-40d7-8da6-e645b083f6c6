import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'listing_ratings'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      table.uuid('listing_id').references('id').inTable('listings').onDelete('SET NULL').nullable()
      table
        .uuid('session_id')
        .references('id')
        .inTable('sessions')
        .onDelete('SET NULL')
        .nullable()
        .unique()
      table.decimal('app_rating')
      table.decimal('experience_rating')
      table.decimal('listing_rating')
      table.text('review')
      table.boolean('is_hidden').defaultTo(false)

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
