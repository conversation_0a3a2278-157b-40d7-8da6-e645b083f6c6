import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'listing_files'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      table.uuid('listing_id').references('id').inTable('listings').onDelete('SET NULL')
      table.uuid('uploaded_by').references('id').inTable('users').onDelete('SET NULL')
      table.string('image_url')
      table.boolean('is_main').defaultTo(false) // Show as first image
      table.boolean('is_approved').defaultTo(false)
      table.boolean('is_hidden').defaultTo(true)
      table.uuid('reviewed_by').references('id').inTable('users').onDelete('SET NULL').nullable() // reviewed by which admin
      table.string('not_approved_reason').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true }).defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
