import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'user_favorite_listings'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.unique(['listing_id', 'user_id'])
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign(['listing_id'])
      table.dropForeign(['user_id'])
      table.dropUnique(['listing_id', 'user_id'])
      table.foreign('listing_id').references('id').inTable('listings').onDelete('CASCADE')
      table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE')
    })
  }
}
