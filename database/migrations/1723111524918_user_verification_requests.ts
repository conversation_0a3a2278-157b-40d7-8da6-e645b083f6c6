import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'user_verification_requests'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('singpass_full_name')
      table.string('gomama_fin_or_passport').nullable()
      table.string('singpass_first_name').nullable()
      table.string('singpass_last_name').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('gomama_fin_or_passport')
    })
  }
}
