import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'amenities'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('image_url')
      table.string('font_icon_name')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('font_icon_name')
    })
  }
}
