import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'app_version_controls'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.boolean('force_update').defaultTo(false).notNullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('force_update')
    })
  }
}
