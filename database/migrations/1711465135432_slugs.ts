import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableNames = ['activities', 'amenities', 'regions', 'user_types']

  public async up() {
    this.tableNames.forEach((tableName) => {
      this.schema.alterTable(tableName, (table) => {
        table.string('slug')
      })
    })
  }

  public async down() {
    this.tableNames.forEach((tableName) => {
      this.schema.alterTable(tableName, (table) => {
        table.dropColumn('slug')
      })
    })
  }
}
