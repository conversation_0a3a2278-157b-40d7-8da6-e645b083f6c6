import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'sessions'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropUnique([], 'sessions_firestore_id_unique')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('firestore_id').alter().unique()
    })
  }
}
