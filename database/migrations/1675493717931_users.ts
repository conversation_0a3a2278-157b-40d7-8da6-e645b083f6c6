import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'users'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      // table.uuid('type_id').references('id').inTable('user_types').onDelete('SET NULL')
      table.string('user_type')
      table.uuid('position_id').references('id').inTable('positions').onDelete('SET NULL')
      table.string('username').unique()
      table.string('email_address')
      table.string('first_name')
      table.string('last_name')
      table.string('photo_url')
      table.string('share_code')
      table.string('gender')
      table.string('birthday')
      table.json('children_birthdays')
      table.string('mobile_number').unique()
      table.string('company_name')
      table.string('country_name')
      table.string('country_code')
      table.string('country_dial_code')
      table.string('nationality')
      table.string('cipher_secret')
      table.string('cipher_iv')
      table.string('code_verifier')
      table.string('timezone')
      table.string('auth_provider')
      table.boolean('is_admin_verified')
      table.boolean('is_email_address_verified')
      table.boolean('is_mobile_number_verified')
      table.boolean('is_singpass_verified')
      table.string('passport_number')
      table.boolean('is_passport_verified')
      table.boolean('is_gomama_verified')
      table.boolean('is_hidden').defaultTo(false)
      table.string('firestore_id').unique() // TODO: To be removed
      table.string('singpass_nonce').nullable()
      table.string('singpass_state').nullable()
      table.string('singpass_secret').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true }).nullable()
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
