{"typescript": true, "commands": ["./commands", "@adonisjs/core/build/commands/index.js", "@adonisjs/repl/build/commands", "@adonisjs/lucid/build/commands", "adonis-lucid-filter/build/commands", "adonis5-scheduler/build/commands"], "exceptionHandlerNamespace": "App/Exceptions/Handler", "aliases": {"App": "app", "Config": "config", "Database": "database", "Contracts": "contracts"}, "preloads": ["./start/routes", "./start/kernel", "./start/events"], "providers": ["./providers/AppProvider", "@adonisjs/core", "@adonisjs/lucid", "@adonisjs/auth", "adonis-lucid-filter", "@adonisjs/lucid-slugify", "@adonisjs/session", "@adonisjs/view", "@adonisjs/drive-s3", "@adonisjs/ally", "@bitkidd/adonis-ally-apple", "@adonisjs/redis", "adonis5-scheduler"], "aceProviders": ["@adonisjs/repl"], "tests": {"suites": [{"name": "functional", "files": ["tests/functional/**/*.spec(.ts|.js)"], "timeout": 60000}]}, "testProviders": ["@japa/preset-adonis/TestsProvider"], "metaFiles": [".adonisrc.json", {"pattern": "public/**", "reloadServer": false}, {"pattern": "production/**", "reloadServer": false}, {"pattern": "resources/views/**/*.edge", "reloadServer": false}]}