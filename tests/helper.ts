export function logOnFailure(fn, debugInfo) {
  try {
    return fn()
  } catch (error) {
    console.error('=== TEST FAILURE DEBUG INFO ===')
    console.error('Response status:', debugInfo.status)
    console.error('Response body:', JSON.stringify(debugInfo.body, null, 2))
    if (debugInfo.headers) {
      console.error('Response headers:', debugInfo.headers)
    }
    console.error('Error:', error.message)
    console.error('===============================')
    throw error
  }
}
