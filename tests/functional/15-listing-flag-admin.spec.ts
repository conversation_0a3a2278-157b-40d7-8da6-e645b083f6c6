import { test } from '@japa/runner'
import { baseUrlAdmin } from 'App/utils'
import User from 'App/Models/User'
import ListingFlag from 'App/Models/ListingFlag'
import { UserType } from 'Contracts/user_type'
import { ListingFlagAction } from 'Contracts/listing_flag_type'
import Listing from 'App/Models/Listing'
import Database from '@ioc:Adonis/Lucid/Database'

test.group('Listing Flag Controller Admin', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1 retrieveAllListingFlagsAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrlAdmin}/listing-flags`).guard('web').loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'listing_id',
        'user_id',
        'reason',
        'category',
        'reviewed_at',
        'action',
        'action_reason',
        'action_by',
        'created_at',
        'updated_at',
      ])
    }
  })

  test('2.1 retrieveListingFlagAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingflag = await ListingFlag.findByOrFail('reason', 'Demo listing flag 1')
    const response = await client
      .get(`${baseUrlAdmin}/listing-flags/${listingflag.id}`)
      .guard('web')
      .loginAs(user!)
    const data = response.body().data
    assert.properties(data, [
      'id',
      'listing_id',
      'user_id',
      'reason',
      'category',
      'reviewed_at',
      'action',
      'action_reason',
      'action_by',
      'created_at',
      'updated_at',
    ])
  })

  test('2.2 retrieveListingFlagAdmin - listing flag not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrlAdmin}/listing-flags/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Listing Flag not found',
    })
  })

  test('3.1 updateListingFlagAdmin - success (hide listing, should hide the mentioned listing and update other unreviewed listing flags with the same mentioned listing with same action, generic reason, and review admin id)', async ({
    client,
    assert,
  }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const reporter = await User.query()
      .whereNot('id', user.id)
      .andWhereNot('user_type', UserType.admin)
      .first()
    const toBeReviewListingflag = await ListingFlag.findByOrFail(
      'reason',
      'Demo listing flag update for listing 2 - will be updated with action hide listing 1'
    )

    const reviewedListingFlagWithSameListingBeforeUpdate = await ListingFlag.findByOrFail(
      'reason',
      'Demo listing flag update for listing 2 - should remain unchanged, after newer unreviewed flags with same listing has been reviewed'
    )

    const unreviewedListingFlagsWithSameListingBeforeUpdate: ListingFlag[] =
      await ListingFlag.query()
        .where('listing_id', toBeReviewListingflag.listingId)
        .andWhereNull('reviewed_at')
        .andWhere('id', '!=', toBeReviewListingflag.id)

    const response = await client
      .put(`${baseUrlAdmin}/listing-flags/${toBeReviewListingflag.id}`)
      .guard('web')
      .loginAs(user!)
      .json({
        user_id: reporter?.id,
        listing_id: toBeReviewListingflag.listingId,
        category: toBeReviewListingflag.category,
        reason:
          'Demo listing flag update 2 - will be update with action hide listing 1 (has been reviewed)',
        action: ListingFlagAction.hideListing,
        action_reason: 'Demo Hide listing after review this flag',
      })

    response.assertBodyContains({
      success: true,
      message: 'Listing flag updated successfully',
    })

    const associatedListing = await Listing.findOrFail(toBeReviewListingflag.listingId)
    /* Match if the mentioned listing has been hidden because of this flag review leads to hidden action */
    assert.isTrue(associatedListing.isHidden)

    /* Match if the older reviewed listing is remain the same, since we don't want to update earlier reviewed flags' value */
    const reviewedListingFlagWithSameListingAfterUpdate = await ListingFlag.findByOrFail(
      'reason',
      'Demo listing flag update for listing 2 - should remain unchanged, after newer unreviewed flags with same listing has been reviewed'
    )

    Object.keys(reviewedListingFlagWithSameListingAfterUpdate.$attributes).forEach((key) => {
      if (!['createdAt', 'updatedAt', 'reviewedAt'].includes(key)) {
        assert.isTrue(
          reviewedListingFlagWithSameListingAfterUpdate.$attributes[key as string] ==
            reviewedListingFlagWithSameListingBeforeUpdate.$attributes[key as string]
        )
      }
    })

    /*
        Match if all unreviewed listing flags with the same listing has
        been updated because the of the hide listing made to
        earlier listing flag with same listing,
        hence other unreviewed listing flags with same listing are updated with same action with generic reason.
    */
    const unreviewedListingFlagsWithSameListingAfterUpdate: ListingFlag[] =
      await ListingFlag.query().whereIn(
        'id',
        unreviewedListingFlagsWithSameListingBeforeUpdate.map((flag) => flag.id)
      )

    unreviewedListingFlagsWithSameListingAfterUpdate.forEach((flag) => {
      assert.isTrue(flag.$attributes['action'] == ListingFlagAction.hideListing)
      assert.isTrue(
        flag.$attributes['actionReason'] ==
          `This listing flag has not been reviewed, but the mentioned listing is already hidden based on flag report id: ${toBeReviewListingflag.id}`
      )
    })
  })

  test('3.2 updateListingFlagAdmin - success (no action, should not do any update to other unreviewed listing flags with the same listing)', async ({
    client,
    assert,
  }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const reporter = await User.query()
      .whereNot('id', user.id)
      .andWhereNot('user_type', UserType.admin)
      .first()
    const listingflag = await ListingFlag.findByOrFail(
      'reason',
      'Demo listing flag update 3 - will be updated with no action'
    )
    const unreviewedListingFlagWithSameListingBeforeUpdate = await ListingFlag.findByOrFail(
      'reason',
      'Demo listing flag update 3 - should remain unchanged, because there is no action for other unreviewed flags with same listing(not hidden)'
    )

    const response = await client
      .put(`${baseUrlAdmin}/listing-flags/${listingflag.id}`)
      .guard('web')
      .loginAs(user!)
      .json({
        user_id: reporter?.id,
        listing_id: listingflag.listingId,
        category: listingflag.category,
        reason: 'Demo listing flag 3 (has been reviewed)',
        action: ListingFlagAction.noAction,
        action_reason:
          'Demo no action towards the listing after review this flag, but it will not update other unreviewed listing flags with the same listing.',
      })

    response.assertBodyContains({
      success: true,
      message: 'Listing flag updated successfully',
    })

    const unreviewedListingFlagWithSameListingAfterUpdate = await ListingFlag.findByOrFail(
      'reason',
      'Demo listing flag update 3 - should remain unchanged, because there is no action for other unreviewed flags with same listing(not hidden)'
    )

    /*
        Match if other unreviewed listing flag is remain unchanged because this review has no action,
        hence we don't want to update other unreviewed flags,
        admin will still need to review other unreviewed flags.
    */
    Object.keys(unreviewedListingFlagWithSameListingAfterUpdate.$attributes).forEach((key) => {
      if (!['createdAt', 'updatedAt', 'reviewedAt'].includes(key)) {
        assert.isTrue(
          unreviewedListingFlagWithSameListingAfterUpdate.$attributes[key as string] ==
            unreviewedListingFlagWithSameListingBeforeUpdate.$attributes[key as string]
        )
      }
    })
  })

  test('3.3 updateListingFlagAdmin - listing flag has been reviewed', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingflag = await ListingFlag.findByOrFail(
      'reason',
      'Demo already reviewed listing flags - should not be able to submit another review again.'
    )

    const response = await client
      .put(`${baseUrlAdmin}/listing-flags/${listingflag.id}`)
      .guard('web')
      .loginAs(user!)
      .json({
        action: ListingFlagAction.noAction,
        action_reason: 'Not sufficient evidence after live investigation',
      })

    response.assertBodyContains({
      success: false,
      message: 'The Listing Flag has been reviewed, cannot be updated',
    })
  })

  test('3.4 updateListingFlagAdmin - listing flag not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .put(`${baseUrlAdmin}/listing-flags/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Listing flag not found',
    })
  })

  test('4.1 deleteListingFlag - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingflag = await ListingFlag.findByOrFail('reason', 'Demo delete listing flag.')
    const response = await client
      .delete(`${baseUrlAdmin}/listing-flags/${listingflag.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Listing flag deleted successfully',
    })
  })

  test('4.2 deleteListingFlag - listing flag not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .delete(`${baseUrlAdmin}/listing-flags/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Listing flag not found',
    })
  })
})
