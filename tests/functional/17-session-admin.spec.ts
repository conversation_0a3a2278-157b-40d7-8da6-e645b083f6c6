import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import Listing from 'App/Models/Listing'
import Session from 'App/Models/Session'
import User from 'App/Models/User'
import { baseUrlAdmin } from 'App/utils'

test.group('Session Controller Admin', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 createSessionAdmin - success', async ({ assert, client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const userNormal = await User.findByOrFail('email_address', '<EMAIL>')
    const listingChosen = await Listing.findByOrFail('name', 'Demo Session Listing With Lock Id')

    const response = await client
      .post(`${baseUrlAdmin}/sessions`)
      .guard('web')
      .loginAs(userAdmin!)
      .json({
        user_id: userNormal.id,
        listing_id: listingChosen.id,
        lock_custom_pin: 'abcd1234',
      })

    const data = response.body().data

    assert.properties(data, [
      'listing_id',
      'user_id',
      'lock_bluetooth_guest_key',
      'lock_custom_pin',
      'lock_daily_pin',
      'lock_hourly_pin',
      'lock_one_time_pin',
      'started_at',
      'expected_ended_at',
      'id',
      'created_at',
      'updated_at',
      'expected_usage_duration',
    ])
  })

  test('1.2 createSessionAdmin - user currently has an on-going session', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const userNormal = await User.findByOrFail('email_address', '<EMAIL>')
    const listingChosen = await Listing.findByOrFail('name', 'Demo Update Listing 1')

    const response = await client
      .post(`${baseUrlAdmin}/sessions`)
      .guard('web')
      .loginAs(userAdmin!)
      .json({
        user_id: userNormal.id,
        listing_id: listingChosen.id,
      })

    response.assertBodyContains({
      success: false,
      message: 'User currently has an on-going session',
      code: 'ERR_SESSION_ONGOING',
    })
  })

  test('1.3 createSessionAdmin - the listing are currently occupied by other user (has an on-going session for the listing)', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const userNormal = await User.findByOrFail('email_address', '<EMAIL>')
    const listingChosen = await Listing.findByOrFail('name', 'Go!mama Pod 106')

    const response = await client
      .post(`${baseUrlAdmin}/sessions`)
      .guard('web')
      .loginAs(userAdmin!)
      .json({
        user_id: userNormal.id,
        listing_id: listingChosen.id,
      })

    response.assertBodyContains({
      success: false,
      message:
        'There is an existing on-going session for the listing, a new session can only be created after its last session has ended',
      code: 'ERR_SESSION_EXISTING',
    })
  })

  test('1.4 createSessionAdmin - listing does not have a lock id', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const userNormal = await User.findByOrFail('email_address', '<EMAIL>')
    const listingChosen = await Listing.findByOrFail('name', 'Listing With No Lock')

    const response = await client
      .post(`${baseUrlAdmin}/sessions`)
      .guard('web')
      .loginAs(userAdmin!)
      .json({
        user_id: userNormal.id,
        listing_id: listingChosen.id,
      })

    response.assertBodyContains({
      success: false,
      message: 'Listing without lock cannot create session',
      code: 'ERR_SESSION_NO_LOCK',
    })
  })

  test('2 findSessionsAdmin - success', async ({ assert, client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrlAdmin}/sessions`).guard('web').loginAs(userAdmin!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'listing_id',
        'user_id',
        'lock_bluetooth_guest_key',
        'lock_custom_pin',
        'lock_daily_pin',
        'lock_hourly_pin',
        'lock_one_time_pin',
        'started_at',
        'expected_ended_at',
        'actual_ended_at',
        'number_of_usage_extensions',
        'is_hidden',
        'firestore_id',
        'created_at',
        'updated_at',
        'expected_usage_duration',
      ])
    }
  })

  test('3.1 findSessionAdmin - success', async ({ assert, client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const sessionChosen = await Session.query()
      .whereNotNull('actual_ended_at')
      .orderBy('created_at', 'desc')
      .firstOrFail()
    const response = await client
      .get(`${baseUrlAdmin}/sessions/${sessionChosen.id}`)
      .guard('web')
      .loginAs(userAdmin!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'listing_id',
      'user_id',
      'lock_bluetooth_guest_key',
      'lock_custom_pin',
      'lock_daily_pin',
      'lock_hourly_pin',
      'lock_one_time_pin',
      'started_at',
      'expected_ended_at',
      'actual_ended_at',
      'number_of_usage_extensions',
      'is_hidden',
      'firestore_id',
      'created_at',
      'updated_at',
      'listing',
      'actual_usage_duration',
      'expected_usage_duration',
      'listing_rating',
    ])
  })

  test('3.2 findSessionAdmin - session not found', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrlAdmin}/sessions/${'not_existing_id'}`)
      .guard('web')
      .loginAs(userAdmin!)

    response.assertBodyContains({
      success: false,
      message: 'Session not found',
    })
  })

  test('4 findSessionsByUserIdAdmin - success', async ({ assert, client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const userNormal = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrlAdmin}/sessions/search-user/${userNormal.id}`)
      .guard('web')
      .loginAs(userAdmin!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'listing_id',
        'user_id',
        'lock_bluetooth_guest_key',
        'lock_custom_pin',
        'lock_daily_pin',
        'lock_hourly_pin',
        'lock_one_time_pin',
        'started_at',
        'expected_ended_at',
        'actual_ended_at',
        'number_of_usage_extensions',
        'is_hidden',
        'firestore_id',
        'created_at',
        'updated_at',
        'expected_usage_duration',
      ])
    }
  })

  test('5.1 updateSessionLockPins - success', async ({ assert, client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const sessionToUpdate = await Session.query().whereNull('actual_ended_at').firstOrFail()
    const response = await client
      .put(`${baseUrlAdmin}/sessions/lock-pins/${sessionToUpdate.id}`)
      .json({
        lock_bluetooth_guestkey_update: true,
        lock_daily_pin_update: true,
        lock_hourly_pin_update: true,
        lock_one_time_pin_update: true,
        lock_custom_pin: 'abcd5588',
      })
      .guard('web')
      .loginAs(userAdmin!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'listing_id',
      'user_id',
      'lock_bluetooth_guest_key',
      'lock_custom_pin',
      'lock_daily_pin',
      'lock_hourly_pin',
      'lock_one_time_pin',
      'started_at',
      'expected_ended_at',
      'actual_ended_at',
      'number_of_usage_extensions',
      'is_hidden',
      'firestore_id',
      'created_at',
      'updated_at',
      'expected_usage_duration',
    ])
  })

  test('5.2 updateSessionLockPins - session not found', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .put(`${baseUrlAdmin}/sessions/lock-pins/${'not_existing_id'}`)
      .json({
        lock_bluetooth_guestkey_update: true,
        lock_daily_pin_update: true,
        lock_hourly_pin_update: true,
        lock_one_time_pin_update: true,
        lock_custom_pin: true,
      })
      .guard('web')
      .loginAs(userAdmin!)

    response.assertBodyContains({ success: false, message: 'Session not found' })
  })

  test('5.3 updateSessionLockPins - session already ended', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const endedSession = await Session.query().whereNotNull('actual_ended_at').firstOrFail()
    const response = await client
      .put(`${baseUrlAdmin}/sessions/lock-pins/${endedSession.id}`)
      .json({
        lock_bluetooth_guestkey_update: true,
        lock_daily_pin_update: true,
        lock_hourly_pin_update: true,
        lock_one_time_pin_update: true,
        lock_custom_pin: true,
      })
      .guard('web')
      .loginAs(userAdmin!)

    response.assertBodyContains({ success: false, message: 'Session has already been ended' })
  })

  test('6.1 endSessionAdmin - success', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const listingChosen = await Listing.findByOrFail('name', 'Go!mama Pod 106')
    const sessionToUpdate = await Session.query()
      .where('listing_id', listingChosen.id)
      .andWhereNull('actual_ended_at')
      .orderBy('created_at', 'desc')
      .firstOrFail()
    const response = await client
      .put(`${baseUrlAdmin}/sessions/end-session/${sessionToUpdate.id}`)
      .guard('web')
      .loginAs(userAdmin!)

    response.assertBodyContains({ success: true, message: 'Session has been ended successfully' })
  })

  test('6.2 endSessionAdmin - session not found', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .put(`${baseUrlAdmin}/sessions/end-session/${'not_existing_id'}`)
      .guard('web')
      .loginAs(userAdmin!)

    response.assertBodyContains({ success: false, message: 'Session not found' })
  })

  test('6.3 endSessionAdmin - session has already been ended', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const endedSession = await Session.query().whereNotNull('actual_ended_at').firstOrFail()
    const response = await client
      .put(`${baseUrlAdmin}/sessions/end-session/${endedSession.id}`)
      .guard('web')
      .loginAs(userAdmin!)

    response.assertBodyContains({ success: false, message: 'Session has already been ended' })
  })

  test('7.1 hideNUnhideSessionAdmin - success', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const listingChosen = await Listing.findByOrFail('name', 'Go!mama Pod 106')
    const sessionToUpdate = await Session.query()
      .where('listing_id', listingChosen.id)
      .andWhere('is_hidden', false)
      .andWhereNotNull('actual_ended_at')
      .orderBy('created_at', 'desc')
      .firstOrFail()
    const response = await client
      .put(`${baseUrlAdmin}/sessions/hide-unhide-session/${sessionToUpdate.id}`)
      .guard('web')
      .loginAs(userAdmin!)

    response.assertBodyContains({
      success: true,
      message: `Session has been ${sessionToUpdate.isHidden ? 'unhidden' : 'hidden'} successfully`,
    })
  })

  test('7.2 hideNUnhideSessionAdmin - session not found', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .put(`${baseUrlAdmin}/sessions/hide-unhide-session/${'not_existing_id'}`)
      .guard('web')
      .loginAs(userAdmin!)

    response.assertBodyContains({ success: false, message: 'Session not found' })
  })

  test('7.3 hideNUnhideSessionAdmin - The session is not ended yet', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const notEndedSession = await Session.query().whereNull('actual_ended_at').firstOrFail()
    const response = await client
      .put(`${baseUrlAdmin}/sessions/hide-unhide-session/${notEndedSession.id}`)
      .guard('web')
      .loginAs(userAdmin!)

    response.assertBodyContains({
      success: false,
      message: 'The session is not ended yet, cannot be hidden',
    })
  })

  test('8.1 deleteSession - success', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const sessionToUpdate = await Session.query()
      .whereNotNull('actual_ended_at')
      .orderBy('created_at', 'desc')
      .firstOrFail()
    const response = await client
      .delete(`${baseUrlAdmin}/sessions/${sessionToUpdate.id}`)
      .guard('web')
      .loginAs(userAdmin!)

    response.assertBodyContains({
      success: true,
      message: 'Deleted session successfully',
    })
  })

  test('8.2 deleteSession - session not found', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .delete(`${baseUrlAdmin}/sessions/${'not_existing_id'}`)
      .guard('web')
      .loginAs(userAdmin!)

    response.assertBodyContains({ success: false, message: 'Session not found' })
  })

  test('8.3 deleteSession - cannot delete an on-going session', async ({ client }) => {
    const userAdmin = await User.findByOrFail('email_address', '<EMAIL>')
    const notEndedSession = await Session.query().whereNull('actual_ended_at').firstOrFail()
    const response = await client
      .delete(`${baseUrlAdmin}/sessions/${notEndedSession.id}`)
      .guard('web')
      .loginAs(userAdmin!)

    response.assertBodyContains({
      success: false,
      message: 'Cannot delete an on-going session',
    })
  })
})
