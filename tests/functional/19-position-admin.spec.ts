import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import Position from 'App/Models/Position'
import User from 'App/Models/User'
import { baseUrlAdmin } from 'App/utils'

test.group('Position Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 createPosition - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const lon = Math.random() * (180 - -180 + -180)
    const lat = Math.random() * (90 - -90 + -90)
    const response = await client
      .post(`${baseUrlAdmin}/positions`)
      .json({ longitude: lon, latitude: lat })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully created new position',
    })
  })

  test('1.2 createPosition - position already exist', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const existingPosition = await Position.query().firstOrFail()
    const lon = existingPosition.coordinate.x
    const lat = existingPosition.coordinate.y
    const response = await client
      .post(`${baseUrlAdmin}/positions`)
      .json({ longitude: lon, latitude: lat })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Position already exist.',
    })
  })

  test('2 findPositionsAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrlAdmin}/positions`).guard('web').loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'geo_hash',
        'coordinate',
        'is_hidden',
        'created_at',
        'updated_at',
      ])
    }
  })

  test('3.1 findPositionAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const existingPosition = await Position.query().firstOrFail()
    const response = await client
      .get(`${baseUrlAdmin}/positions/${existingPosition.id}`)
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'geo_hash',
      'coordinate',
      'is_hidden',
      'created_at',
      'updated_at',
    ])
  })

  test('3.2 findPositionAdmin - position not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrlAdmin}/positions/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Position not found' })
  })

  test('4.1 updatePosition - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const lon = Math.random() * (180 - -180 + -180)
    const lat = Math.random() * (90 - -90 + -90)
    const existingPosition = await Position.query().firstOrFail()
    const response = await client
      .put(`${baseUrlAdmin}/positions/${existingPosition.id}`)
      .json({ longitude: lon, latitude: lat })
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'geo_hash',
      'coordinate',
      'is_hidden',
      'created_at',
      'updated_at',
    ])
  })

  test('4.2 updatePosition - position already exist', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const existingPosition = await Position.query().firstOrFail()
    const existingPosition2 = await Position.query()
      .where('id', '!=', existingPosition.id)
      .firstOrFail()
    const response = await client
      .put(`${baseUrlAdmin}/positions/${existingPosition.id}`)
      .json({ longitude: existingPosition2.coordinate.x, latitude: existingPosition2.coordinate.y })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Position already exist.' })
  })

  test('5.1 deletePosition - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const existingPosition = await Position.query().firstOrFail()
    const response = await client
      .delete(`${baseUrlAdmin}/positions/${existingPosition.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully deleted the position',
    })
  })

  test('5.2 deletePosition - position not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .delete(`${baseUrlAdmin}/positions/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Position not found' })
  })
})
