import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import User from 'App/Models/User'
import UserVerificationRequest from 'App/Models/UserVerificationRequest'
import { UserType } from 'Contracts/user_type'
import { UserVerificationStatus } from 'Contracts/user_verification_request_type'

test.group('User Controller Admin', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 createUserAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.post('/api/v1/admin/users').guard('web').loginAs(user!).json({
      new_account: '<EMAIL>',
      password: 'abcd1234',
      passwordConfirm: 'abcd1234',
      first_name: 'He<PERSON>',
      last_name: '<PERSON>',
      longitude: '100.234',
      latitude: '55.33',
      user_type: UserType.admin,
    })

    const data = response.body().data

    assert.properties(response.body(), ['success', 'message', 'data'])

    assert.properties(data, [
      'email_address',
      'position_id',
      'first_name',
      'last_name',
      'user_type',
      'share_code',
      'id',
      'created_at',
      'updated_at',
      'full_name',
    ])
  })

  test('1.2 createUserAdmin - account not a phone or an email', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.post('/api/v1/admin/users').guard('web').loginAs(user!).json({
      new_account: 'not_phone_or_email',
      password: 'abcd1234',
      passwordConfirm: 'abcd1234',
      first_name: 'Heng',
      last_name: 'Jack',
      longitude: '100.234',
      latitude: '55.33',
      user_type: UserType.admin,
    })

    response.assertBodyContains({
      success: false,
      message: 'Please provide either phone or email',
    })
  })

  test('1.3 createUserAdmin - admin account must use email', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.post('/api/v1/admin/users').guard('web').loginAs(user!).json({
      new_account: '********',
      password: 'abcd1234',
      passwordConfirm: 'abcd1234',
      first_name: 'Heng',
      last_name: 'Jack',
      longitude: '100.234',
      latitude: '55.33',
      user_type: UserType.admin,
    })

    response.assertBodyContains({
      success: false,
      message: 'Admin user must be registered using an email',
    })
  })

  test('1.4 createUserAdmin - confirm password not match', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.post('/api/v1/admin/users').guard('web').loginAs(user!).json({
      new_account: '<EMAIL>',
      password: 'abcd1234',
      passwordConfirm: 'not_same_as_password',
      first_name: 'Heng',
      last_name: 'Jack',
      longitude: '100.234',
      latitude: '55.33',
      user_type: UserType.admin,
    })

    response.assertBodyContains({
      success: false,
      message: 'Password and confirm password is not matched',
    })
  })

  test('1.5 createUserAdmin - email already exist', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.post('/api/v1/admin/users').guard('web').loginAs(user!).json({
      new_account: '<EMAIL>',
      password: 'abcd1234',
      passwordConfirm: 'abcd1234',
      first_name: 'Heng',
      last_name: 'Jack',
      longitude: '100.234',
      latitude: '55.33',
      user_type: UserType.admin,
    })

    response.assertBodyContains({
      success: false,
      message: `Email address already exist`,
    })
  })

  test('1.6 createUserAdmin - phone number already exist', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.post('/api/v1/admin/users').guard('web').loginAs(user!).json({
      new_account: '********',
      password: 'abcd1234',
      passwordConfirm: 'abcd1234',
      first_name: 'Heng',
      last_name: 'Jack',
      longitude: '100.234',
      latitude: '55.33',
      user_type: UserType.user,
    })

    response.assertBodyContains({
      success: false,
      message: `Phone number already exist`,
    })
  })

  test('2.1 findUsersAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get('/api/v1/admin/users').guard('web').loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'user_type',
        'position_id',
        'username',
        'email_address',
        'first_name',
        'last_name',
        'photo_url',
        'share_code',
        'gender',
        'birthday',
        'children_birthdays',
        'mobile_number',
        'company_name',
        'country_name',
        'country_code',
        'country_dial_code',
        'nationality',
        'timezone',
        'auth_provider',
        'is_admin_verified',
        'is_email_address_verified',
        'is_mobile_number_verified',
        'is_singpass_verified',
        'passport_number',
        'is_passport_verified',
        'is_gomama_verified',
        'is_hidden',
        'firestore_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'full_name',
        'full_mobile_number',
        'number_of_sessions',
      ])
    }
  })

  test('2.2 findUsersAdmin - valid user type filter param', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const userType = Object.values(UserType)[0]
    const response = await client
      .get(`/api/v1/admin/users?type=${userType}`)
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'user_type',
        'position_id',
        'username',
        'email_address',
        'first_name',
        'last_name',
        'photo_url',
        'share_code',
        'gender',
        'birthday',
        'children_birthdays',
        'mobile_number',
        'company_name',
        'country_name',
        'country_code',
        'country_dial_code',
        'nationality',
        'timezone',
        'auth_provider',
        'is_admin_verified',
        'is_email_address_verified',
        'is_mobile_number_verified',
        'is_singpass_verified',
        'passport_number',
        'is_passport_verified',
        'is_gomama_verified',
        'is_hidden',
        'firestore_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'full_name',
        'full_mobile_number',
        'number_of_sessions',
      ])
    }
  })

  test('2.3 findUsersAdmin - invalid user type filter param', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const type = 'NOT_EXIST_USER_TYPE'
    const response = await client
      .get(`/api/v1/admin/users?type=${type}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: `Invalid user type: ${type}, valid user type: ${Object.values(UserType)}`,
    })
  })

  test('3.1 findUserAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const searchUser = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`/api/v1/admin/users/${searchUser.id}`)
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'user_type',
      'position_id',
      'username',
      'email_address',
      'first_name',
      'last_name',
      'photo_url',
      'share_code',
      'gender',
      'birthday',
      'children_birthdays',
      'mobile_number',
      'company_name',
      'country_name',
      'country_code',
      'country_dial_code',
      'nationality',
      'timezone',
      'auth_provider',
      'is_admin_verified',
      'is_email_address_verified',
      'is_mobile_number_verified',
      'is_singpass_verified',
      'passport_number',
      'is_passport_verified',
      'is_gomama_verified',
      'is_hidden',
      'firestore_id',
      'created_at',
      'updated_at',
      'deleted_at',
      'full_name',
      'full_mobile_number',
      'number_of_sessions',
    ])
  })

  test('3.2 findUserAdmin - user not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`/api/v1/admin/users/${'not_exist_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'User not found',
    })
  })

  test('4.1 findUserByUsernameAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const searchUser = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`/api/v1/admin/users/search-username/${searchUser.username}`)
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'user_type',
      'position_id',
      'username',
      'email_address',
      'first_name',
      'last_name',
      'photo_url',
      'share_code',
      'gender',
      'birthday',
      'children_birthdays',
      'mobile_number',
      'company_name',
      'country_name',
      'country_code',
      'country_dial_code',
      'nationality',
      'timezone',
      'auth_provider',
      'is_admin_verified',
      'is_email_address_verified',
      'is_mobile_number_verified',
      'is_singpass_verified',
      'passport_number',
      'is_passport_verified',
      'is_gomama_verified',
      'is_hidden',
      'firestore_id',
      'created_at',
      'updated_at',
      'deleted_at',
      'full_name',
      'full_mobile_number',
      'number_of_sessions',
    ])
  })

  test('4.2 findUserByUsernameAdmin - user not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`/api/v1/admin/users/search-username/${'not_exist_username'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'User not found',
    })
  })

  test('5 updateUserAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const searchUser = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .put(`/api/v1/admin/users/${searchUser.id}`)
      .json({
        is_hidden: false,
        is_admin_verified: true,
      })
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'user_type',
      'position_id',
      'username',
      'email_address',
      'first_name',
      'last_name',
      'photo_url',
      'share_code',
      'gender',
      'birthday',
      'children_birthdays',
      'mobile_number',
      'company_name',
      'country_name',
      'country_code',
      'country_dial_code',
      'nationality',
      'timezone',
      'auth_provider',
      'is_admin_verified',
      'is_email_address_verified',
      'is_mobile_number_verified',
      'is_singpass_verified',
      'passport_number',
      'is_passport_verified',
      'is_gomama_verified',
      'is_hidden',
      'firestore_id',
      'created_at',
      'updated_at',
      'deleted_at',
      'full_name',
      'full_mobile_number',
      'number_of_sessions',
    ])
  })

  test('6.1 restoreSoftDeleteAccount - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const searchUser = await User.findByOrFail(
      'email_address', '<EMAIL>'
    )
    const response = await client
      .put(`/api/v1/admin/users/restore/${searchUser.id}`)
      .json({
        is_hidden: false,
        is_admin_verified: true,
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully restored the user account.',
    })
  })

  test('6.2 restoreSoftDeleteAccount - user is not soft deleted', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const searchUser = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .put(`/api/v1/admin/users/restore/${searchUser.id}`)
      .json({
        is_hidden: false,
        is_admin_verified: true,
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'User is not soft deleted, hence cannot be restored.',
    })
  })

  test('7.1 reviewVerification - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const sendRequestUser = await User.findByOrFail('email_address', '<EMAIL>')
    const veriRequest = await UserVerificationRequest.findByOrFail('user_id', sendRequestUser.id)

    const response = await client
      .post(`/api/v1/admin/users/review-verification-request/${veriRequest.id}`)
      .json({
        status: UserVerificationStatus.approve,
        reason: 'Verified using Government software.',
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Review verification request success',
    })
  })

  test('7.2 reviewVerification - verification request not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .post(`/api/v1/admin/users/review-verification-request/${'not_exist_id'}`)
      .json({
        status: UserVerificationStatus.approve,
        reason: 'Verified using Government software.',
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Verification request not found',
    })
  })

  test('8.1 deleteUserAdmin - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const searchUser = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .delete(`/api/v1/admin/users/${searchUser.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully deleted a user.',
    })
  })

  test('8.2 deleteUserAdmin - user not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .delete(`/api/v1/admin/users/${'not_exist_id_because_deleted'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'User not found.',
    })
  })
})
