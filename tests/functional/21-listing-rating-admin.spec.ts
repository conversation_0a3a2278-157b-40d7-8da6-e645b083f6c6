import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import ListingRating from 'App/Models/ListingRating'
import User from 'App/Models/User'
import { baseUrlAdmin } from 'App/utils'

test.group('Position Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1 findListingRatingsAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrlAdmin}/listing-ratings`).guard('web').loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'listing_id',
        'session_id',
        'app_rating',
        'experience_rating',
        'listing_rating',
        'review',
        'is_hidden',
        'created_at',
        'updated_at',
        'listing',
      ])

      assert.properties(data[0]['listing'], [
        'id',
        'position_id',
        'region_id',
        'verified_by',
        'suggested_by',
        'name',
        'listing_type',
        'company_name',
        'address_name',
        'description',
        'full_address',
        'contact_number',
        'keywords',
        'postal_code',
        'usage_durations',
        'usage_extension_durations',
        'max_number_of_usage_extensions',
        'number_of_private_feeding_rooms',
        'number_of_diaper_changing_mats',
        'diaper_changing_mat_type',
        'country_dial_code',
        'humidity',
        'temperature',
        'opening_hours',
        'pi_id',
        'pi_last_updated',
        'api_key',
        'lock_id',
        'lock_master_pin',
        'lock_bluetooth_admin_key',
        'door_is_lockable',
        'status',
        'is_usage_extendable',
        'is_verified',
        'is_hidden',
        'firestore_id',
        'note',
        'created_at',
        'updated_at',
        'position',
        'full_contact_number',
        'average_experience_ratings',
        'total_experience_ratings',
        'total_sessions',
        'five_stars_count',
        'four_stars_count',
        'three_stars_count',
        'two_stars_count',
        'one_star_count',
        'distance',
      ])
    }
  })

  test('2.1 findListingRatingAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingRating = await ListingRating.query().firstOrFail()

    const response = await client
      .get(`${baseUrlAdmin}/listing-ratings/${listingRating.id}`)
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'listing_id',
      'session_id',
      'app_rating',
      'experience_rating',
      'listing_rating',
      'review',
      'is_hidden',
      'created_at',
      'updated_at',
      'listing',
    ])

    assert.properties(data['listing'], [
      'id',
      'position_id',
      'region_id',
      'verified_by',
      'suggested_by',
      'name',
      'listing_type',
      'company_name',
      'address_name',
      'description',
      'full_address',
      'contact_number',
      'keywords',
      'postal_code',
      'usage_durations',
      'usage_extension_durations',
      'max_number_of_usage_extensions',
      'number_of_private_feeding_rooms',
      'number_of_diaper_changing_mats',
      'diaper_changing_mat_type',
      'country_dial_code',
      'humidity',
      'temperature',
      'opening_hours',
      'pi_id',
      'pi_last_updated',
      'api_key',
      'lock_id',
      'lock_master_pin',
      'lock_bluetooth_admin_key',
      'door_is_lockable',
      'status',
      'is_usage_extendable',
      'is_verified',
      'is_hidden',
      'firestore_id',
      'note',
      'created_at',
      'updated_at',
      'position',
      'full_contact_number',
      'average_experience_ratings',
      'total_experience_ratings',
      'total_sessions',
      'five_stars_count',
      'four_stars_count',
      'three_stars_count',
      'two_stars_count',
      'one_star_count',
      'distance',
    ])
  })

  test('2.2 findListingRatingAdmin - listing rating not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .get(`${baseUrlAdmin}/listing-ratings/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Listing rating not found' })
  })

  test('3.1 updateListingRating - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingRating = await ListingRating.query().firstOrFail()

    const response = await client
      .put(`${baseUrlAdmin}/listing-ratings/${listingRating.id}`)
      .json({
        app_rating: Math.random() * (5 - 0 + 0),
        experience_rating: Math.random() * (5 - 0 + 0),
        listing_rating: Math.random() * (5 - 0 + 0),
        review: `Updated review for this session's listing rating`,
        is_hidden: false,
      })
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'listing_id',
      'session_id',
      'app_rating',
      'experience_rating',
      'listing_rating',
      'review',
      'is_hidden',
      'created_at',
      'updated_at',
    ])
  })

  test('3.2 updateListingRating - listing rating not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .put(`${baseUrlAdmin}/listing-ratings/${'not_existing_id'}`)
      .json({
        app_rating: Math.random() * (5 - 0 + 0),
        experience_rating: Math.random() * (5 - 0 + 0),
        listing_rating: Math.random() * (5 - 0 + 0),
        review: `Updated review for this session's listing rating`,
        is_hidden: false,
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Listing rating not found' })
  })

  test('4.1 updateListingRating - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingRatingToDelete = await ListingRating.query()
      .where('review', 'Demo listing rating DELETE for a session 4.')
      .firstOrFail()

    const response = await client
      .delete(`${baseUrlAdmin}/listing-ratings/${listingRatingToDelete.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: true, message: 'Deleted listing rating successfully' })
  })

  test('4.2 updateListingRating - listing rating not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .delete(`${baseUrlAdmin}/listing-ratings/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Listing rating not found' })
  })
})
