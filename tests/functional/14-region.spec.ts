import { test } from '@japa/runner'
import { baseUrl } from 'App/utils'
import User from 'App/Models/User'
import Region from 'App/Models/Region'
import Database from '@ioc:Adonis/Lucid/Database'

test.group('Region Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1 findRegions - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrl}/regions`).loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'name',
        'description',
        'slug',
        'id',
        'created_at',
        'updated_at',
        'image_url',
      ])
    }
  })

  test('2.1 findRegion - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const region = await Region.findByOrFail('slug', 'central')
    const response = await client.get(`${baseUrl}/regions/${region.id}`).loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'name',
      'description',
      'slug',
      'id',
      'created_at',
      'updated_at',
      'image_url',
    ])
  })

  test('2.1 findRegion - region not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrl}/regions/${'not_existing_id'}`).loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Region not found' })
  })

  test('3.1 findRegionWithSlug - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const region = await Region.findByOrFail('slug', 'central')
    const response = await client.get(`${baseUrl}/regions/slug/${region.slug}`).loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'name',
      'description',
      'slug',
      'id',
      'created_at',
      'updated_at',
      'image_url',
    ])
  })

  test('3.2 findRegionWithSlug - region not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrl}/regions/slug/${'not_existing_slug'}`)
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Region not found' })
  })
})
