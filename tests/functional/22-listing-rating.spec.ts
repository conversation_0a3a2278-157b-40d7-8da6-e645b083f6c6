import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import Listing from 'App/Models/Listing'
import ListingRating from 'App/Models/ListingRating'
import Session from 'App/Models/Session'
import User from 'App/Models/User'
import { baseUrl } from 'App/utils'

test.group('Position Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 createListingRating - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const session = await Session.findByOrFail('lockBluetoothGuestKey', 'demo5NotRated')

    const response = await client
      .post(`${baseUrl}/listing-ratings`)
      .json({
        session_id: session.id,
        app_rating: 5,
        experience_rating: 4,
        listing_rating: 4,
        review: 'Overall the experience is great',
      })
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'session_id',
      'app_rating',
      'experience_rating',
      'listing_rating',
      'listing_id',
      'review',
      'id',
      'created_at',
      'updated_at',
      'listing',
    ])

    assert.properties(data['listing'], [
      'id',
      'position_id',
      'region_id',
      'verified_by',
      'suggested_by',
      'name',
      'listing_type',
      'company_name',
      'address_name',
      'description',
      'full_address',
      'contact_number',
      'keywords',
      'postal_code',
      'usage_durations',
      'usage_extension_durations',
      'max_number_of_usage_extensions',
      'number_of_private_feeding_rooms',
      'number_of_diaper_changing_mats',
      'diaper_changing_mat_type',
      'country_dial_code',
      'humidity',
      'temperature',
      'opening_hours',
      'pi_id',
      'pi_last_updated',
      'api_key',
      'lock_id',
      'lock_master_pin',
      'lock_bluetooth_admin_key',
      'door_is_lockable',
      'status',
      'is_usage_extendable',
      'is_verified',
      'is_hidden',
      'firestore_id',
      'note',
      'created_at',
      'updated_at',
      'position',
      'full_contact_number',
      'average_experience_ratings',
      'total_experience_ratings',
      'total_sessions',
      'five_stars_count',
      'four_stars_count',
      'three_stars_count',
      'two_stars_count',
      'one_star_count',
      'distance',
    ])
  })

  test('1.2 createListingRating - listing session does not belongs to user', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const session = await Session.findByOrFail('lockBluetoothGuestKey', 'demo5NotRated')

    const response = await client
      .post(`${baseUrl}/listing-ratings`)
      .json({
        session_id: session.id,
        app_rating: 5,
        experience_rating: 4,
        listing_rating: 4,
        review: 'Overall the experience is great',
      })
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'The session does not belongs to user' })
  })

  test('1.3 createListingRating - listing session is not ended', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const session = await Session.findByOrFail('lockBluetoothGuestKey', 'demo6NotEnded')

    const response = await client
      .post(`${baseUrl}/listing-ratings`)
      .json({
        session_id: session.id,
        app_rating: 5,
        experience_rating: 4,
        listing_rating: 4,
        review: 'Overall the experience is great',
      })
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'The session is not ended yet' })
  })

  test('1.4 createListingRating - already has a rating for the session', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const session = await Session.findByOrFail('lockBluetoothGuestKey', 'demo1')

    const response = await client
      .post(`${baseUrl}/listing-ratings`)
      .json({
        session_id: session.id,
        app_rating: 5,
        experience_rating: 4,
        listing_rating: 4,
        review: 'Overall the experience is great',
      })
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'An existing rating is already assigned to the session',
    })
  })

  test('2.1 findListingRatingSummary - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.findByOrFail('name', 'Demo Listing Rating 1')

    const response = await client
      .get(`${baseUrl}/listings/${listing.id}/listing-rating-summary`)
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'position_id',
      'region_id',
      'verified_by',
      'suggested_by',
      'name',
      'listing_type',
      'company_name',
      'address_name',
      'description',
      'full_address',
      'contact_number',
      'keywords',
      'postal_code',
      'usage_durations',
      'usage_extension_durations',
      'max_number_of_usage_extensions',
      'number_of_private_feeding_rooms',
      'number_of_diaper_changing_mats',
      'diaper_changing_mat_type',
      'country_dial_code',
      'humidity',
      'temperature',
      'opening_hours',
      'pi_id',
      'pi_last_updated',
      'api_key',
      'lock_id',
      'lock_master_pin',
      'lock_bluetooth_admin_key',
      'door_is_lockable',
      'status',
      'is_usage_extendable',
      'is_verified',
      'is_hidden',
      'firestore_id',
      'note',
      'created_at',
      'updated_at',
      'position',
      'full_contact_number',
      'average_experience_ratings',
      'total_experience_ratings',
      'total_sessions',
      'five_stars_count',
      'four_stars_count',
      'three_stars_count',
      'two_stars_count',
      'one_star_count',
      'distance',
    ])
  })

  test('2.2 findListingRatingSummary - listing not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .get(`${baseUrl}/listings/${'not_existing_id'}/listing-rating-summary`)
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Listing not found',
    })
  })

  test('3.1 findListingRatings - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.findByOrFail('name', 'Demo Listing Rating 1')

    const response = await client
      .get(`${baseUrl}/listings/${listing.id}/listing-ratings`)
      .loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'session_id',
        'app_rating',
        'experience_rating',
        'listing_rating',
        'listing_id',
        'review',
        'id',
        'created_at',
        'updated_at',
        'listing',
      ])

      assert.properties(data[0]['listing'], [
        'id',
        'position_id',
        'region_id',
        'verified_by',
        'suggested_by',
        'name',
        'listing_type',
        'company_name',
        'address_name',
        'description',
        'full_address',
        'contact_number',
        'keywords',
        'postal_code',
        'usage_durations',
        'usage_extension_durations',
        'max_number_of_usage_extensions',
        'number_of_private_feeding_rooms',
        'number_of_diaper_changing_mats',
        'diaper_changing_mat_type',
        'country_dial_code',
        'humidity',
        'temperature',
        'opening_hours',
        'pi_id',
        'pi_last_updated',
        'api_key',
        'lock_id',
        'lock_master_pin',
        'lock_bluetooth_admin_key',
        'door_is_lockable',
        'status',
        'is_usage_extendable',
        'is_verified',
        'is_hidden',
        'firestore_id',
        'note',
        'created_at',
        'updated_at',
        'position',
        'full_contact_number',
        'average_experience_ratings',
        'total_experience_ratings',
        'total_sessions',
        'five_stars_count',
        'four_stars_count',
        'three_stars_count',
        'two_stars_count',
        'one_star_count',
        'distance',
      ])
    }
  })

  test('3.2 findListingRatings - listing not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .get(`${baseUrl}/listings/${'not_existing_id'}/listing-ratings`)
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Listing not found' })
  })

  test('4.1 findListingRating - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingRating = await ListingRating.findByOrFail(
      'review',
      'Demo listing rating for a session 1.'
    )

    const response = await client
      .get(`${baseUrl}/listing-ratings/${listingRating.id}`)
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'session_id',
      'app_rating',
      'experience_rating',
      'listing_rating',
      'listing_id',
      'review',
      'id',
      'created_at',
      'updated_at',
      'listing',
    ])

    assert.properties(data['listing'], [
      'id',
      'position_id',
      'region_id',
      'verified_by',
      'suggested_by',
      'name',
      'listing_type',
      'company_name',
      'address_name',
      'description',
      'full_address',
      'contact_number',
      'keywords',
      'postal_code',
      'usage_durations',
      'usage_extension_durations',
      'max_number_of_usage_extensions',
      'number_of_private_feeding_rooms',
      'number_of_diaper_changing_mats',
      'diaper_changing_mat_type',
      'country_dial_code',
      'humidity',
      'temperature',
      'opening_hours',
      'pi_id',
      'pi_last_updated',
      'api_key',
      'lock_id',
      'lock_master_pin',
      'lock_bluetooth_admin_key',
      'door_is_lockable',
      'status',
      'is_usage_extendable',
      'is_verified',
      'is_hidden',
      'firestore_id',
      'note',
      'created_at',
      'updated_at',
      'position',
      'full_contact_number',
      'average_experience_ratings',
      'total_experience_ratings',
      'total_sessions',
      'five_stars_count',
      'four_stars_count',
      'three_stars_count',
      'two_stars_count',
      'one_star_count',
      'distance',
    ])
  })

  test('4.2 findListingRating - listing not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .get(`${baseUrl}/listing-ratings/${'not_existing_id'}`)
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Listing rating not found' })
  })
})
