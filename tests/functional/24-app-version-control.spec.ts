import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import AppVersionControl from 'App/Models/AppVersionControl'
import User from 'App/Models/User'
import { baseUrl } from 'App/utils'

test.group('App Version Control Controller', async (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 retrieveLatestPublishedVersion - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrl}/app-version-controls/latest`).loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'version',
      'description',
      'added_by',
      'published_by',
      'published_at',
      'id',
      'created_at',
      'updated_at',
    ])
  })

  test('1.2 retrieveLatestPublishedVersion - not published version', async ({ client }) => {
    const allAppVersions = await AppVersionControl.query()

    allAppVersions.forEach(async (appversion) => await appversion.delete())

    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrl}/app-version-controls/latest`).loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Currently there is no published version',
    })
  })
})
