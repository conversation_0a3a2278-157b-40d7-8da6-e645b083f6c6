import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import Position from 'App/Models/Position'
import User from 'App/Models/User'
import { baseUrl } from 'App/utils'

test.group('Position Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1 findPositions - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrl}/positions`).loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'geo_hash',
        'coordinate',
        'is_hidden',
        'created_at',
        'updated_at',
      ])
    }
  })

  test('2.1 findPosition - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const existingPosition = await Position.query().firstOrFail()
    const response = await client.get(`${baseUrl}/positions/${existingPosition.id}`).loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'geo_hash',
      'coordinate',
      'is_hidden',
      'created_at',
      'updated_at',
    ])
  })

  test('2.2 findPosition - position not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrl}/positions/${'not_existing_id'}`).loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Position not found' })
  })
})
