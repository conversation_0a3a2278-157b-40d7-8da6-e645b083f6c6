import { test } from '@japa/runner'
import Drive from '@ioc:Adonis/Core/Drive'
import { file } from '@ioc:Adonis/Core/Helpers'
import { baseUrlAdmin, remotePathListing } from 'App/utils'
import { ListingType } from 'Contracts/listing_type'
import User from 'App/Models/User'
import Listing from 'App/Models/Listing'
import { DateTime } from 'luxon'
import ListingFile from 'App/Models/ListingFile'
import Database from '@ioc:Adonis/Lucid/Database'

test.group('Listing Controller Admin', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1 suggestListingAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const fakeDrive = Drive.fake()
    const fakeImgMain = await file.generatePng('1mb')
    const fakeImgSub1 = await file.generatePng('1mb')
    const fakeImgSub2 = await file.generatePng('1mb')

    const response = await client
      .post(`${baseUrlAdmin}/listings`)
      .fields({
        listing_type: ListingType.gomama,
        region: 'central',
        name: 'IOI Mall Demo Test Case',
        company_name: 'Mandai Wildlife Group 2',
        address_name: 'Available near Birdz of Play area 2',
        description: 'Available near Birdz of Play area 2',
        full_address: '2 Jurong Hill, Singapore 628925 2',
        latitude: 90,
        longitude: 55,
        contact_number: '62693411',
        country_dial_code: '65',
        keywords: ['jurong bird park', 'care'],
        postal_code: '039593',
        diaper_changing_mat_type: 'Padded-ex',
        opening_hours: '10am-10pm',
        usage_durations: [30, 45], // Note: Unknown bug by Adonis, fields() will convert 1 element array to non array value, hence filled with 2 elements.
        usage_extension_durations: [15, 15], // Note: Unknown Adonis bug , fields() will convert 1 element array to non array value, hence filled with 2 elements.
        max_number_of_usage_extensions: 1,
        number_of_private_feeding_rooms: 2,
        number_of_diaper_changing_mats: 1,
        main_image_url:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT64aK-O3Vmcbamks7bvRvw-w8RUrEWdP3YSw&usqp=CAU',
        sub_image_urls: [
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2110132144503830465%2FWhatsApp%20Image%202022-03-23%20at%202.13.21%20PM%20-%20Eunice%20Lim.jpeg?alt=media&token=f06402cb-a091-43d4-8d16-22f98930b172',
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT7AVONzbQmk5S29wXhZ2gnZEnrHDPhC_BM2g&usqp=CAU',
        ],
        activities: ['indoor-play', 'breastfeeding'],
        amenities: ['power-socket', 'wash-basin'],
        api_key: 'API-KEY-TEST',
        pi_id: 'RASPBERRY-PI-CHIP-ID',
        pi_last_updated: DateTime.now().toISO(),
        lock_id: 'IGLOOHOME-LOCK-ID',
        lock_master_pin: 'LOCK-MASTER-PIN',
        lock_bluetooth_admin_key: 'LOCK-BLUETOOTH-ADMIN-KEY',
        note: 'for testing demo',
      })
      .file('main_image_file', fakeImgMain.contents, { filename: fakeImgMain.name })
      .file('sub_image_files.[0]', fakeImgSub1.contents, { filename: fakeImgSub1.name })
      .file('sub_image_files.[1]', fakeImgSub2.contents, { filename: fakeImgSub2.name })
      .guard('web')
      .loginAs(user!)

    assert.isTrue(await fakeDrive.exists(remotePathListing + '/' + fakeImgMain.name))
    assert.isTrue(await fakeDrive.exists(remotePathListing + '/' + fakeImgSub1.name))
    assert.isTrue(await fakeDrive.exists(remotePathListing + '/' + fakeImgSub2.name))

    Drive.restore()

    const data = response.body().data

    assert.properties(data, [
      'id',
      'position_id',
      'region_id',
      'verified_by',
      'suggested_by',
      'name',
      'listing_type',
      'company_name',
      'address_name',
      'description',
      'full_address',
      'contact_number',
      'keywords',
      'postal_code',
      'usage_durations',
      'usage_extension_durations',
      'max_number_of_usage_extensions',
      'number_of_private_feeding_rooms',
      'number_of_diaper_changing_mats',
      'diaper_changing_mat_type',
      'country_dial_code',
      'humidity',
      'temperature',
      'opening_hours',
      'pi_id',
      'pi_last_updated',
      'api_key',
      'lock_id',
      'lock_master_pin',
      'lock_bluetooth_admin_key',
      'door_is_lockable',
      'status',
      'is_usage_extendable',
      'is_verified',
      'is_hidden',
      'firestore_id',
      'created_at',
      'updated_at',
      'listing_files',
      'position',
      'activities',
      'amenities',
      'full_contact_number',
      'average_experience_ratings',
      'total_experience_ratings',
      'total_sessions',
      'five_stars_count',
      'four_stars_count',
      'three_stars_count',
      'two_stars_count',
      'one_star_count',
      'distance',
    ])

    assert.properties(
      data['listing_files'][0],
      data['listing_files'].length > 0
        ? [
            'id',
            'listing_id',
            'uploaded_by',
            'image_url',
            'is_main',
            'is_approved',
            'is_hidden',
            'reviewed_by',
            'not_approved_reason',
            'created_at',
            'updated_at',
          ]
        : []
    )

    assert.properties(
      data['amenities'][0],
      data['amenities'].length > 0
        ? [
            'id',
            'name',
            'description',
            'image_url',
            'is_hidden',
            'created_at',
            'updated_at',
            'slug',
          ]
        : []
    )

    assert.properties(data['position'], [
      'id',
      'geo_hash',
      'coordinate',
      'is_hidden',
      'created_at',
      'updated_at',
    ])
  })

  test('2.1 findListingsAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrlAdmin}/listings`).guard('web').loginAs(user!)
    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'position_id',
        'region_id',
        'verified_by',
        'suggested_by',
        'name',
        'listing_type',
        'company_name',
        'address_name',
        'description',
        'full_address',
        'contact_number',
        'keywords',
        'postal_code',
        'usage_durations',
        'usage_extension_durations',
        'max_number_of_usage_extensions',
        'number_of_private_feeding_rooms',
        'number_of_diaper_changing_mats',
        'diaper_changing_mat_type',
        'country_dial_code',
        'humidity',
        'temperature',
        'opening_hours',
        'pi_id',
        'pi_last_updated',
        'api_key',
        'lock_id',
        'lock_master_pin',
        'lock_bluetooth_admin_key',
        'door_is_lockable',
        'status',
        'is_usage_extendable',
        'is_verified',
        'is_hidden',
        'firestore_id',
        'note',
        'created_at',
        'updated_at',
        'listing_files',
        'activities',
        'amenities',
        'position',
        'full_contact_number',
        'average_experience_ratings',
        'total_experience_ratings',
        'total_sessions',
        'five_stars_count',
        'four_stars_count',
        'three_stars_count',
        'two_stars_count',
        'one_star_count',
        'distance',
      ])
    }
  })

  test('2.2 findListingsAdmin - with listing type query param', async ({ client, assert }) => {
    const type = ListingType.gomama
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrlAdmin}/listings?type=${type}`)
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'position_id',
        'region_id',
        'verified_by',
        'suggested_by',
        'name',
        'listing_type',
        'company_name',
        'address_name',
        'description',
        'full_address',
        'contact_number',
        'keywords',
        'postal_code',
        'usage_durations',
        'usage_extension_durations',
        'max_number_of_usage_extensions',
        'number_of_private_feeding_rooms',
        'number_of_diaper_changing_mats',
        'diaper_changing_mat_type',
        'country_dial_code',
        'humidity',
        'temperature',
        'opening_hours',
        'pi_id',
        'pi_last_updated',
        'api_key',
        'lock_id',
        'lock_master_pin',
        'lock_bluetooth_admin_key',
        'door_is_lockable',
        'status',
        'is_usage_extendable',
        'is_verified',
        'is_hidden',
        'firestore_id',
        'note',
        'created_at',
        'updated_at',
        'listing_files',
        'activities',
        'amenities',
        'position',
        'full_contact_number',
        'average_experience_ratings',
        'total_experience_ratings',
        'total_sessions',
        'five_stars_count',
        'four_stars_count',
        'three_stars_count',
        'two_stars_count',
        'one_star_count',
        'distance',
      ])
    }
  })

  test('2.3 findListingsAdmin - invalid listing type', async ({ client }) => {
    const type = 'not_existing_listing_type'
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrlAdmin}/listings?type=${type}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: `Invalid listing type: ${type}, valid listing type: ${Object.values(ListingType)}`,
    })
  })

  test('3.1 findListingAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().limit(1).first()
    const response = await client
      .get(`${baseUrlAdmin}/listings/${listing?.id}`)
      .guard('web')
      .loginAs(user!)
    const data = response.body().data

    assert.properties(data, [
      'activities',
      'address_name',
      'amenities',
      'api_key',
      'average_experience_ratings',
      'company_name',
      'contact_number',
      'country_dial_code',
      'created_at',
      'description',
      'diaper_changing_mat_type',
      'distance',
      'door_is_lockable',
      'firestore_id',
      'five_stars_count',
      'four_stars_count',
      'full_address',
      'full_contact_number',
      'humidity',
      'id',
      'is_hidden',
      'is_usage_extendable',
      'is_verified',
      'keywords',
      'listing_files',
      'listing_ratings',
      'listing_type',
      'lock_bluetooth_admin_key',
      'lock_id',
      'lock_master_pin',
      'max_number_of_usage_extensions',
      'name',
      'note',
      'number_of_diaper_changing_mats',
      'number_of_private_feeding_rooms',
      'one_star_count',
      'opening_hours',
      'pi_id',
      'pi_last_updated',
      'position',
      'position_id',
      'postal_code',
      'region_id',
      'status',
      'suggested_by',
      'temperature',
      'three_stars_count',
      'total_experience_ratings',
      'total_sessions',
      'two_stars_count',
      'updated_at',
      'usage_durations',
      'usage_extension_durations',
      'verified_by',
    ])
  })

  test('3.2 findListingAdmin - listing not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrlAdmin}/listings/${'not_exist_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Listing not found',
    })
  })

  test('4 findListingsByPositionAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().limit(1).first()
    const response = await client
      .get(
        `${baseUrlAdmin}/listings/search-position?lat=${listing?.position.coordinate.y}&lon=${listing?.position.coordinate.x}`
      )
      .guard('web')
      .loginAs(user!)
    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'position_id',
        'region_id',
        'verified_by',
        'suggested_by',
        'name',
        'listing_type',
        'company_name',
        'address_name',
        'description',
        'full_address',
        'contact_number',
        'keywords',
        'postal_code',
        'usage_durations',
        'usage_extension_durations',
        'max_number_of_usage_extensions',
        'number_of_private_feeding_rooms',
        'number_of_diaper_changing_mats',
        'diaper_changing_mat_type',
        'country_dial_code',
        'humidity',
        'temperature',
        'opening_hours',
        'pi_id',
        'pi_last_updated',
        'api_key',
        'lock_id',
        'lock_master_pin',
        'lock_bluetooth_admin_key',
        'door_is_lockable',
        'status',
        'is_usage_extendable',
        'is_verified',
        'is_hidden',
        'firestore_id',
        'note',
        'created_at',
        'updated_at',
        'listing_files',
        'activities',
        'amenities',
        'position',
        'full_contact_number',
        'average_experience_ratings',
        'total_experience_ratings',
        'total_sessions',
        'five_stars_count',
        'four_stars_count',
        'three_stars_count',
        'two_stars_count',
        'one_star_count',
        'distance',
      ])
    }
  })

  test('5 findListingsByKeywordsAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().limit(1).first()
    const response = await client
      .get(`${baseUrlAdmin}/listings/search-keywords?keywords=${listing?.keywords.join(',')}`)
      .guard('web')
      .loginAs(user!)
    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'name',
        'full_address',
        'opening_hours',
        'created_at',
        'updated_at',
        'position',
        'listing_files',
        'average_experience_ratings',
        'total_experience_ratings',
        'total_sessions',
        'five_stars_count',
        'four_stars_count',
        'three_stars_count',
        'two_stars_count',
        'one_star_count',
        'distance',
      ])
    }
  })

  test('6.1a updateListingAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().where('name', 'Demo Update Listing 1').firstOrFail()
    const mainImage = await ListingFile.query()
      .where('listing_id', listing?.id)
      .andWhere('is_main', true)
      .first()
    const subImages = await ListingFile.query()
      .where('listing_id', listing?.id)
      .andWhere('is_main', false)

    console.log

    const response = await client
      .put(`${baseUrlAdmin}/listings/${listing?.id}`)
      .json({
        type: ListingType.gomama,
        region: 'central',
        name: 'Go!mama DENR @ Imbiah Station',
        company_name: 'Mandai Wildlife Group 2',
        address_name: 'Available near Birdz of Play area 2',
        description: 'Available near Birdz of Play area 2',
        full_address: '2 Jurong Hill, Singapore 628925 2',
        latitude: 50,
        longitude: 69,
        contact_number: '62693411',
        country_dial_code: '65',
        keywords: ['jurong bird park', 'care'],
        postal_code: '039569',
        diaper_changing_mat_type: 'Padded-ex',
        opening_hours: '10am-10pm',
        usage_durations: [30],
        usage_extension_durations: [15],
        max_number_of_usage_extensions: 1,
        number_of_private_feeding_rooms: 2,
        number_of_diaper_changing_mats: 1,
        main_image_id: mainImage?.id as string,
        main_image_url:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT64aK-O3Vmcbamks7bvRvw-w8RUrEWdP3YSw&usqp=CAU',
        sub_image_ids: subImages.map((subImage) => subImage.id),
        sub_image_urls: subImages.map((_, index) => `image URL ${index}`),
        activities: [
          {
            is_hidden: false,
            activity_slug: 'indoor-play',
          },
          {
            is_hidden: false,
            activity_slug: 'breastfeeding',
          },
        ],
        amenities: [
          {
            is_hidden: false,
            amenity_slug: 'wash-basin',
          },
          {
            is_hidden: false,
            amenity_slug: 'diaper-changing',
          },
        ],
        api_key: 'API-KEY-TEST',
        pi_id: 'RASPBERRY-PI-CHIP-ID',
        pi_last_updated: DateTime.now().toISO(),
        lock_id: 'IGLOOHOME-LOCK-ID',
        lock_master_pin: 'LOCK-MASTER-PIN',
        lock_bluetooth_admin_key: 'LOCK-BLUETOOTH-ADMIN-KEY',
        is_hidden: false,
        is_verified: true,
        is_usage_extendable: true,
        status: 'idle',
        door_is_lockable: true,
        humidity: 10,
        temperature: 26,
        note: 'the listing has 2 locks which is very confusing to user.',
      })
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'position_id',
      'region_id',
      'verified_by',
      'suggested_by',
      'name',
      'listing_type',
      'company_name',
      'address_name',
      'description',
      'full_address',
      'contact_number',
      'keywords',
      'postal_code',
      'usage_durations',
      'usage_extension_durations',
      'max_number_of_usage_extensions',
      'number_of_private_feeding_rooms',
      'number_of_diaper_changing_mats',
      'diaper_changing_mat_type',
      'country_dial_code',
      'humidity',
      'temperature',
      'opening_hours',
      'pi_id',
      'pi_last_updated',
      'api_key',
      'lock_id',
      'lock_master_pin',
      'lock_bluetooth_admin_key',
      'door_is_lockable',
      'status',
      'is_usage_extendable',
      'is_verified',
      'is_hidden',
      'firestore_id',
      'note',
      'created_at',
      'updated_at',
      'listing_files',
      'activities',
      'amenities',
      'position',
      'full_contact_number',
      'average_experience_ratings',
      'total_experience_ratings',
      'total_sessions',
      'five_stars_count',
      'four_stars_count',
      'three_stars_count',
      'two_stars_count',
      'one_star_count',
      'distance',
    ])
  })

  test('6.1b updateListingAdmin - success (update image only)', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().where('name', 'Demo Update Listing 2').firstOrFail()
    const mainImage = await ListingFile.query()
      .where('listing_id', listing?.id)
      .andWhere('is_main', true)
      .first()
    const subImages = await ListingFile.query()
      .where('listing_id', listing?.id)
      .andWhere('is_main', false)

    const fakeDrive = Drive.fake()
    const fakeImgMain = await file.generatePng('1mb')
    const fakeImgSub1 = await file.generatePng('1mb')
    const fakeImgSub2 = await file.generatePng('1mb')
    const fakeImgSub3 = await file.generatePng('1mb')
    await client
      .put(`${baseUrlAdmin}/listings/${listing.id}`)
      .fields({
        main_image_id: mainImage?.id as string,
        sub_image_ids: subImages.map((subImage) => subImage.id),
      })
      .file('main_image_file', fakeImgMain.contents, { filename: fakeImgMain.name })
      .file('sub_image_files.[0]', fakeImgSub1.contents, { filename: fakeImgSub1.name })
      .file('sub_image_files.[1]', fakeImgSub2.contents, { filename: fakeImgSub2.name })
      .file('sub_image_files.[2]', fakeImgSub3.contents, { filename: fakeImgSub3.name })
      .guard('web')
      .loginAs(user!)

    assert.isTrue(await fakeDrive.exists(remotePathListing + '/' + fakeImgMain.name))
    assert.isTrue(await fakeDrive.exists(remotePathListing + '/' + fakeImgSub1.name))
    assert.isTrue(await fakeDrive.exists(remotePathListing + '/' + fakeImgSub2.name))
    assert.isTrue(await fakeDrive.exists(remotePathListing + '/' + fakeImgSub3.name))

    Drive.restore()
  })

  test('6.2 updateListingAdmin - listing not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .put(`${baseUrlAdmin}/listings/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Listing not found',
    })
  })

  test('6.3 updateListingAdmin - main image id does not belongs to the listing', async ({
    client,
  }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const fakeImgMain = await file.generatePng('1mb')
    const fakeImgSub1 = await file.generatePng('1mb')
    const fakeImgSub2 = await file.generatePng('1mb')
    const fakeImgSub3 = await file.generatePng('1mb')

    const listing = await Listing.query().where('name', 'Demo Update Listing 1').first()

    const subImages = await ListingFile.query()
      .where('listing_id', listing?.id ?? '')
      .andWhere('is_main', false)

    const otherListingMainImage = await ListingFile.query()
      .whereNot('listing_id', listing?.id ?? '')
      .andWhere('is_main', true)
      .limit(1)
      .first()

    const response = await client
      .put(`${baseUrlAdmin}/listings/${listing?.id}`)
      .fields({
        main_image_id: otherListingMainImage?.id as string,
        sub_image_ids: subImages.map((subImage) => subImage.id),
      })
      .file('main_image_file', fakeImgMain.contents, { filename: fakeImgMain.name })
      .file('sub_image_files.[0]', fakeImgSub1.contents, { filename: fakeImgSub1.name })
      .file('sub_image_files.[1]', fakeImgSub2.contents, { filename: fakeImgSub2.name })
      .file('sub_image_files.[2]', fakeImgSub3.contents, { filename: fakeImgSub3.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message:
        'Please provide the main image file id that are associated with the mentioned listing only. If the mentioned listing does not have any main image yet, this field is not needed',
    })
  })

  test('6.4 updateListingAdmin - main image id provided but without image source for update', async ({
    client,
  }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const listing = await Listing.query().where('name', 'Demo Update Listing 1').first()

    const mainImage = await ListingFile.query()
      .where('listing_id', listing?.id ?? '')
      .andWhere('is_main', true)
      .first()
    const subImages = await ListingFile.query()
      .where('listing_id', listing?.id ?? '')
      .andWhere('is_main', false)

    const response = await client
      .put(`${baseUrlAdmin}/listings/${listing?.id}`)
      .fields({
        main_image_id: mainImage?.id ?? '',
        sub_image_ids: subImages.map((subImage) => subImage.id),
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Please provide a main image file or main image url for updates',
    })
  })

  test('6.5 updateListingAdmin - already has existing main image, but the main image id is not provided', async ({
    client,
  }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const fakeImgMain = await file.generatePng('1mb')

    const listing = await Listing.query().where('name', 'Demo Update Listing 1').first()

    const response = await client
      .put(`${baseUrlAdmin}/listings/${listing?.id}`)
      .file('main_image_file', fakeImgMain.contents, { filename: fakeImgMain.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message:
        'The mentioned listing already has a main image, please provide the main image id for updates',
    })
  })

  test('6.6 updateListingAdmin - sub image id provided but without image source for updates', async ({
    client,
  }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const listing = await Listing.query().where('name', 'Demo Update Listing 1').first()

    const subImages = await ListingFile.query()
      .where('listing_id', listing?.id ?? '')
      .andWhere('is_main', false)

    const response = await client
      .put(`${baseUrlAdmin}/listings/${listing?.id}`)
      .fields({
        sub_image_ids: subImages.map((subImage) => subImage.id),
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Please provide either sub_image_files or sub_image_urls to update sub image',
    })
  })

  test('6.7 updateListingAdmin - 3 sub image id provided but only 1 image source, not enough image source', async ({
    client,
  }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const fakeImgSub1 = await file.generatePng('1mb')

    const listing = await Listing.query().where('name', 'Demo Update Listing 1').first()

    const subImages = await ListingFile.query()
      .where('listing_id', listing?.id ?? '')
      .andWhere('is_main', false)

    const response = await client
      .put(`${baseUrlAdmin}/listings/${listing?.id}`)
      .fields({
        sub_image_ids: subImages.map((subImage) => subImage.id), // there is 2 sub image id here
      })
      .file('sub_image_files.[0]', fakeImgSub1.contents, { filename: fakeImgSub1.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message:
        'The total number of image from both sub_image_urls and sub_image_files are lesser than the sub_image_ids to be updated, please provide the sufficient image source to update each sub image, extra image source will be treated as new image to be created',
    })
  })

  test('6.8 updateListingAdmin - One of the sub image id does not belongs to a listing', async ({
    client,
  }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const fakeImgSub1 = await file.generatePng('1mb')
    const fakeImgSub2 = await file.generatePng('1mb')
    const fakeImgSub3 = await file.generatePng('1mb')

    const listing = await Listing.query().where('name', 'Demo Update Listing 1').first()
    const anotherListing = await Listing.query().where('name', 'Demo Update Listing 2').first()

    const subImagesNotBelongsToListing = await ListingFile.query()
      .where('listing_id', anotherListing?.id ?? '')
      .andWhere('is_main', false)

    const response = await client
      .put(`${baseUrlAdmin}/listings/${listing?.id}`)
      .fields({
        sub_image_ids: subImagesNotBelongsToListing.map((subImage) => subImage.id),
      })
      .file('sub_image_files.[0]', fakeImgSub1.contents, { filename: fakeImgSub1.name })
      .file('sub_image_files.[1]', fakeImgSub2.contents, { filename: fakeImgSub2.name })
      .file('sub_image_files.[2]', fakeImgSub3.contents, { filename: fakeImgSub3.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message:
        'Please provide the sub image file id that are associated with the mentioned listing only at index: 0',
    })
  })

  test('7.1 updateListingImageAdmin - success', async ({ assert, client }) => {
    const fakeDrive = Drive.fake()
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().where('name', 'Demo Update Listing 1').first()
    const listingFile = await ListingFile.query()
      .where('listing_id', listing?.id ?? '')
      .andWhere('is_main', true)
      .first()
    const fakeImg1 = await file.generatePng('1mb')

    const response = await client
      .put(`${baseUrlAdmin}/listings/update-image/${listingFile?.id}`)
      .fields({
        image_url:
          'https://onecms-res.cloudinary.com/image/upload/s--UXYVuxrp--/f_auto,q_auto/v1/mediacorp/cna/image/2021/12/08/rmf05822-2.jpg?itok=NZvX_9oe',
        is_approved: true,
        is_hidden: false,
        is_main: true,
      })
      .file('image_file', fakeImg1.contents, { filename: fakeImg1.name })
      .guard('web')
      .loginAs(user!)

    assert.isTrue(await fakeDrive.exists(remotePathListing + '/' + fakeImg1.name))

    response.assertBodyContains({
      success: true,
      message: 'Updated image successfully',
    })

    Drive.restore()
  })

  test('7.2 updateListingImageAdmin - delete sub image success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().where('name', 'Demo Update Listing 1').first()
    const listingFile = await ListingFile.query()
      .where('listing_id', listing?.id ?? '')
      .andWhere('is_main', false)
      .first()

    const response = await client
      .put(`${baseUrlAdmin}/listings/update-image/${listingFile?.id}`)
      .fields({
        to_delete: true,
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Deleted image successfully',
    })

    Drive.restore()
  })

  test('7.3 updateListingImageAdmin - Unable to delete main image, each listing must have atleast one main image', async ({
    client,
  }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().where('name', 'Demo Update Listing 1').first()
    const listingFile = await ListingFile.query()
      .where('listing_id', listing?.id ?? '')
      .andWhere('is_main', true)
      .first()

    const response = await client
      .put(`${baseUrlAdmin}/listings/update-image/${listingFile?.id}`)
      .fields({
        to_delete: true,
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message:
        'Unable to delete image, each listing must have atleast one main image, please upload/update a new main image before delete this image',
    })

    Drive.restore()
  })

  test('7.4 updateListingImageAdmin - update new main image (automatically replace old main image with current submitted image)', async ({
    client,
  }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().where('name', 'Demo Update Listing 1').first()
    const listingFile = await ListingFile.query()
      .where('listing_id', listing?.id ?? '')
      .andWhere('is_main', false)
      .first()

    const response = await client
      .put(`${baseUrlAdmin}/listings/update-image/${listingFile?.id}`)
      .fields({
        is_main: true,
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Updated image successfully',
    })

    Drive.restore()
  })

  test('7.5 updateListingImageAdmin - should not update a main image to become a sub image (should update sub image to main image or upload a new main image first)', async ({
    client,
  }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().where('name', 'Demo Update Listing 1').first()
    const listingFile = await ListingFile.query()
      .where('listing_id', listing?.id ?? '')
      .andWhere('is_main', true)
      .first()

    const response = await client
      .put(`${baseUrlAdmin}/listings/update-image/${listingFile?.id}`)
      .fields({
        is_main: false,
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Each listing must have atleast one main image',
    })

    Drive.restore()
  })

  test('8.1 deleteListing - success,', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.findByOrFail('name', 'Demo Delete Listing 1')

    const response = await client
      .delete(`${baseUrlAdmin}/listings/${listing.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully deleted the listing',
    })
  })

  test('8.2 deleteListing - listing not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .delete(`${baseUrlAdmin}/listings/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Listing not found',
    })
  })
})
