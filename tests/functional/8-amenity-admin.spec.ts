import { test } from '@japa/runner'
import User from 'App/Models/User'
import Drive from '@ioc:Adonis/Core/Drive'
import { file } from '@ioc:Adonis/Core/Helpers'
import Amenity from 'App/Models/Amenity'
import Database from '@ioc:Adonis/Lucid/Database'

test.group('Amenity Controller Admin', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 createAmenity - success', async ({ assert, client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .post('/api/v1/admin/amenities')
      .fields({
        name: 'Book Shelf',
        description: 'Book Shelf to put stuff',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, ['name', 'description', 'id', 'created_at', 'updated_at', 'image_url'])

    Drive.restore()
  })

  test('1.2 createAmenity - amenity already exist', async ({ client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenityName = 'Wash Basin'
    const response = await client
      .post('/api/v1/admin/amenities')
      .fields({
        name: amenityName,
        description: 'sink',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: `The amenity: ${amenityName} already exist`,
    })

    Drive.restore()
  })

  test('2 findAmenitiesAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get('/api/v1/admin/amenities').guard('web').loginAs(user!)
    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'name',
        'description',
        'image_url',
        'is_hidden',
        'created_at',
        'updated_at',
        'slug',
      ])
    }
  })

  test('3.1 findAmenityAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenity = await Amenity.findByOrFail('slug', 'wash-basin')
    const response = await client
      .get(`/api/v1/admin/amenities/${amenity.id}`)
      .guard('web')
      .loginAs(user!)
    const data = response.body().data

    assert.properties(data, [
      'id',
      'name',
      'description',
      'image_url',
      'is_hidden',
      'created_at',
      'updated_at',
      'slug',
    ])
  })

  test('3.2 findAmenityAdmin - amenity not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`/api/v1/admin/amenities/${'not_exist_amenity_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Amenity not found',
    })
  })

  test('4.1 findAmenityWithSlugAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenity = await Amenity.query().whereNotNull('slug').first()
    const response = await client
      .get(`/api/v1/admin/amenities/search-slug/${amenity?.slug}`)
      .guard('web')
      .loginAs(user!)
    const data = response.body().data

    assert.properties(data, [
      'id',
      'name',
      'description',
      'image_url',
      'is_hidden',
      'created_at',
      'updated_at',
      'slug',
    ])
  })

  test('4.2 findAmenityWithSlugAdmin - amenity not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`/api/v1/admin/amenities/search-slug/${'not_exist_slug'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Amenity not found',
    })
  })

  test('5.1 updateAmenity - success', async ({ assert, client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenity = await Amenity.findByOrFail('name', 'To be updated Amenity 1')
    const amenityName = 'To be updated Amenity 1 - update'
    const response = await client
      .put(`/api/v1/admin/amenities/${amenity.id}`)
      .fields({
        name: amenityName,
        description: 'Demo amenity updated description',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    const data = response.body().data
    assert.properties(data, [
      'name',
      'description',
      'id',
      'created_at',
      'updated_at',
      'image_url',
      'is_hidden',
    ])
    Drive.restore()
  })

  test('5.2 updateAmenity - amenity name already exist', async ({ client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenity = await Amenity.findByOrFail('name', 'To be updated Amenity 3')
    const amenityName = 'Wash Basin'
    const response = await client
      .put(`/api/v1/admin/amenities/${amenity.id}`)
      .fields({
        name: amenityName,
        description: 'Demo amenity updated description',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: `The amenity: ${amenityName} already exist`,
    })

    Drive.restore()
  })

  test('5.3 updateAmenity - amenity not found', async ({ client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenityName = 'Wash Basin'
    const response = await client
      .put(`/api/v1/admin/amenities/${'not_exist_id'}`)
      .fields({
        name: amenityName,
        description: 'Demo amenity updated description',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Amenity not found',
    })

    Drive.restore()
  })

  test('6.1 updateAmenityWithSlug - success', async ({ assert, client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenity = await Amenity.findByOrFail('name', 'To be updated Amenity 2')
    const amenityName = 'Demo Amenity Test Case'
    const response = await client
      .put(`/api/v1/admin/amenities/slug/${amenity.slug}`)
      .fields({
        name: amenityName,
        description: 'Demo amenity updated description with slug',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    const data = response.body().data
    assert.properties(data, [
      'name',
      'description',
      'id',
      'created_at',
      'updated_at',
      'image_url',
      'is_hidden',
    ])
    Drive.restore()
  })

  test('6.2 updateAmenityWithSlug - amenity name already exist', async ({ client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenity = await Amenity.findByOrFail('name', 'To be updated Amenity 4')
    const amenityName = 'Wash Basin'
    const response = await client
      .put(`/api/v1/admin/amenities/slug/${amenity.slug}`)
      .fields({
        name: amenityName,
        description: 'Demo amenity updated description with slug',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: `The amenity: ${amenityName} already exist`,
    })
    Drive.restore()
  })

  test('6.3 updateAmenityWithSlug - amenity not found', async ({ client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenityName = 'Breastfeeding'
    const response = await client
      .put(`/api/v1/admin/amenities/slug/${'not-exist-slug'}`)
      .fields({
        name: amenityName,
        description: 'Demo amenity updated description with slug',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Amenity not found',
    })
    Drive.restore()
  })

  test('7.1 deleteAmenity - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenity = await Amenity.findByOrFail('name', 'To be deleted Amenity 1')
    const response = await client
      .delete(`/api/v1/admin/amenities/${amenity.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully deleted an amenity',
    })
  })

  test('7.2 deleteAmenity - amenity not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .delete(`/api/v1/admin/amenities/${'not_exist_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Amenity not found',
    })
  })

  test('8.1 deleteAmenityWithSlug - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenity = await Amenity.findByOrFail('name', 'To be deleted Amenity 2')
    const response = await client
      .delete(`/api/v1/admin/amenities/slug/${amenity.slug}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully deleted an amenity',
    })
  })

  test('8.2 deleteAmenityWithSlug - amenity not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .delete(`/api/v1/admin/amenities/slug/${'not_exist_slug'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Amenity not found',
    })
  })
})
