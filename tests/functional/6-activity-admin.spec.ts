import { test } from '@japa/runner'
import User from 'App/Models/User'
import Drive from '@ioc:Adonis/Core/Drive'
import { file } from '@ioc:Adonis/Core/Helpers'
import Activity from 'App/Models/Activity'
import Database from '@ioc:Adonis/Lucid/Database'

test.group('Activity Controller Admin', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 createActivity - success', async ({ assert, client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .post('/api/v1/admin/activities')
      .fields({
        name: 'Book Reading',
        description: 'Book for Mama to read',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, ['name', 'description', 'id', 'created_at', 'updated_at', 'image_url'])

    Drive.restore()
  })

  test('1.2 createActivity - activity already exist', async ({ client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activityName = 'Breastfeeding'
    const response = await client
      .post('/api/v1/admin/activities')
      .fields({
        name: activityName,
        description: 'breastfeeding platform',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: `The activity: ${activityName} already exist`,
    })

    Drive.restore()
  })

  test('2 findActivitiesAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get('/api/v1/admin/activities').guard('web').loginAs(user!)
    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'name',
        'description',
        'image_url',
        'is_hidden',
        'created_at',
        'updated_at',
        'slug',
      ])
    }
  })

  test('3.1 findActivityAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activity = await Activity.findByOrFail('slug', 'breastfeeding')
    const response = await client
      .get(`/api/v1/admin/activities/${activity.id}`)
      .guard('web')
      .loginAs(user!)
    const data = response.body().data

    assert.properties(data, [
      'id',
      'name',
      'description',
      'image_url',
      'is_hidden',
      'created_at',
      'updated_at',
      'slug',
    ])
  })

  test('3.2 findActivityAdmin - activity not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`/api/v1/admin/activities/${'not_exist_activity_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Activity not found',
    })
  })

  test('4.1 findActivityWithSlugAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activity = await Activity.query().whereNotNull('slug').first()
    const response = await client
      .get(`/api/v1/admin/activities/search-slug/${activity?.slug}`)
      .guard('web')
      .loginAs(user!)
    const data = response.body().data

    assert.properties(data, [
      'id',
      'name',
      'description',
      'image_url',
      'is_hidden',
      'created_at',
      'updated_at',
      'slug',
    ])
  })

  test('4.2 findActivityWithSlugAdmin - activity not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`/api/v1/admin/activities/search-slug/${'not_exist_slug'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Activity not found',
    })
  })

  test('5.1 updateActivity - success', async ({ assert, client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activity = await Activity.findByOrFail('name', 'To be updated Activity 1')
    const activityName = 'To be updated Activity 1 - updated'
    const response = await client
      .put(`/api/v1/admin/activities/${activity.id}`)
      .fields({
        name: activityName,
        description: 'Demo activity updated description',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    const data = response.body().data
    assert.properties(data, [
      'name',
      'description',
      'id',
      'created_at',
      'updated_at',
      'image_url',
      'is_hidden',
    ])
    Drive.restore()
  })

  test('5.2 updateActivity - activity name already exist', async ({ client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activity = await Activity.findByOrFail('name', 'To be updated Activity 3')
    const activityName = 'Breastfeeding'
    const response = await client
      .put(`/api/v1/admin/activities/${activity.id}`)
      .fields({
        name: activityName,
        description: 'Demo activity updated description',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: `The activity: ${activityName} already exist`,
    })

    Drive.restore()
  })

  test('5.3 updateActivity - activity not found', async ({ client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activityName = 'Breastfeeding'
    const response = await client
      .put(`/api/v1/admin/activities/${'not_exist_id'}`)
      .fields({
        name: activityName,
        description: 'Demo activity updated description',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Activity not found',
    })

    Drive.restore()
  })

  test('6.1 updateActivityWithSlug - success', async ({ assert, client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activity = await Activity.findByOrFail('name', 'To be updated Activity 2')
    const activityName = 'To be updated Activity 2 - updated'
    const response = await client
      .put(`/api/v1/admin/activities/slug/${activity.slug}`)
      .fields({
        name: activityName,
        description: 'Demo activity updated description with slug',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    const data = response.body().data
    assert.properties(data, [
      'name',
      'description',
      'id',
      'created_at',
      'updated_at',
      'image_url',
      'is_hidden',
    ])
    Drive.restore()
  })

  test('6.2 updateActivityWithSlug - activity name already exist', async ({ client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activity = await Activity.findByOrFail('name', 'To be updated Activity 4')
    const activityName = 'Breastfeeding'
    const response = await client
      .put(`/api/v1/admin/activities/slug/${activity.slug}`)
      .fields({
        name: activityName,
        description: 'Demo activity updated description with slug',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: `The activity: ${activityName} already exist`,
    })
    Drive.restore()
  })

  test('6.3 updateActivityWithSlug - activity not found', async ({ client }) => {
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activityName = 'Breastfeeding'
    const response = await client
      .put(`/api/v1/admin/activities/slug/${'not-exist-slug'}`)
      .fields({
        name: activityName,
        description: 'Demo activity updated description with slug',
        image_url:
          'https://static.vecteezy.com/system/resources/previews/024/349/357/non_2x/shower-icon-logo-illustration-design-template-vector.jpg',
      })
      .file('image_file', fakeImg.contents, { filename: fakeImg.name })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Activity not found',
    })
    Drive.restore()
  })

  test('7.1 deleteActivity - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activity = await Activity.findByOrFail('name', 'To be deleted Activity 1')
    const response = await client
      .delete(`/api/v1/admin/activities/${activity.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully deleted an activity',
    })
  })

  test('7.2 deleteActivity - activity not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .delete(`/api/v1/admin/activities/${'not_exist_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Activity not found',
    })
  })

  test('8.1 deleteActivityWithSlug - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activity = await Activity.findByOrFail('name', 'To be deleted Activity 2')
    const response = await client
      .delete(`/api/v1/admin/activities/slug/${activity.slug}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully deleted an activity',
    })
  })

  test('8.2 deleteActivityWithSlug - activity not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .delete(`/api/v1/admin/activities/slug/${'not_exist_slug'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Activity not found',
    })
  })
})
