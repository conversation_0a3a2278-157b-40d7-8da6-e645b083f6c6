import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import Amenity from 'App/Models/Amenity'
import User from 'App/Models/User'

test.group('Amenity Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1 findAmenities - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get('/api/v1/amenities').loginAs(user!)
    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'name',
        'description',
        'image_url',
        'is_hidden',
        'created_at',
        'updated_at',
        'slug',
      ])
    }
  })

  test('2.1 findAmenity - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenity = await Amenity.findByOrFail('slug', 'wash-basin')
    const response = await client.get(`/api/v1/amenities/${amenity.id}`).loginAs(user!)
    const data = response.body().data

    assert.properties(data, [
      'id',
      'name',
      'description',
      'image_url',
      'is_hidden',
      'created_at',
      'updated_at',
      'slug',
    ])
  })

  test('2.2 findAmenity - amenity not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`/api/v1/amenities/${'not_exist_amenity_id'}`).loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Amenity not found',
    })
  })

  test('3.1 findAmenityWithSlug - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const amenity = await Amenity.query().whereNotNull('slug').first()
    const response = await client
      .get(`/api/v1/amenities/search-slug/${amenity?.slug}`)
      .loginAs(user!)
    const data = response.body().data

    assert.properties(data, [
      'id',
      'name',
      'description',
      'image_url',
      'is_hidden',
      'created_at',
      'updated_at',
      'slug',
    ])
  })

  test('3.2 findAmenityWithSlug - amenity not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`/api/v1/amenities/search-slug/${'not_exist_slug'}`)
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Amenity not found',
    })
  })
})
