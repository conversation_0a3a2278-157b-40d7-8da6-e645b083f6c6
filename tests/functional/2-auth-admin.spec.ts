import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import User from 'App/Models/User'

test.group('Auth Controller Admin', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 adminLogin - success', async ({ assert, client }) => {
    const response = await client
      .post('/api/v1/admin/login')
      .json({ email: '<EMAIL>', password: 'abcd1234' })
    const data = response.body().data

    assert.properties(data, ['user'])

    assert.properties(data.user, [
      'id',
      'user_type',
      'position_id',
      'username',
      'email_address',
      'first_name',
      'last_name',
      'photo_url',
      'share_code',
      'gender',
      'birthday',
      'children_birthdays',
      'mobile_number',
      'company_name',
      'country_name',
      'country_code',
      'country_dial_code',
      'nationality',
      'timezone',
      'auth_provider',
      'is_admin_verified',
      'is_email_address_verified',
      'is_mobile_number_verified',
      'is_singpass_verified',
      'passport_number',
      'is_passport_verified',
      'is_gomama_verified',
      'is_hidden',
      'firestore_id',
      'deleted_at',
      'created_at',
      'updated_at',
      'full_name',
      'full_mobile_number',
      'number_of_sessions',
    ])
  })

  test('1.2 adminLogin - not admin', async ({ client }) => {
    const response = await client
      .post('/api/v1/admin/login')
      .json({ email: '<EMAIL>', password: 'abcd1234' })

    response.assertBodyContains({
      success: false,
      message: 'Only admin user can login admin panel',
    })
  })

  test('1.3 adminLogin - invalid password', async ({ client }) => {
    const response = await client
      .post('/api/v1/admin/login')
      .json({ email: '<EMAIL>', password: 'invalid_password_12345678' })

    response.assertBodyContains({
      guard: 'web',
      responseText: 'E_INVALID_AUTH_PASSWORD: Password mis-match',
    })
  })

  test('1.4 adminLogin - user not exist', async ({ client }) => {
    const response = await client
      .post('/api/v1/admin/login')
      .json({ email: '<EMAIL>', password: 'invalid_password_12345678' })

    response.assertBodyContains({
      success: false,
      message: 'User not exist',
    })
  })

  test('1.5 adminLogin - existing admin user but hidden', async ({ client }) => {
    const response = await client
      .post('/api/v1/admin/login')
      .json({ email: '<EMAIL>', password: 'abcd1234' })

    response.assertBodyContains({
      success: false,
      message: 'User not exist',
    })
  })

  test('1.6 adminLogin - existing admin user but soft deleted', async ({ client }) => {
    const response = await client
      .post('/api/v1/admin/login')
      .json({ email: '<EMAIL>', password: 'abcd1234' })

    response.assertBodyContains({
      success: false,
      message: 'User not exist',
    })
  })

  test('2 logoutAdmin - success', async ({ client }) => {
    const user = await User.findBy('email_address', '<EMAIL>')
    const response = await client.post('/api/v1/me/logout').guard('api').loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully logout',
    })
  })
})
