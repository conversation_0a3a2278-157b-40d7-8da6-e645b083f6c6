import { test } from '@japa/runner'
import Drive from '@ioc:Adonis/Core/Drive'
import { file } from '@ioc:Adonis/Core/Helpers'
import Region from 'App/Models/Region'
import { baseUrl, remotePathListing } from 'App/utils'
import { ListingType } from 'Contracts/listing_type'
import User from 'App/Models/User'
import Listing from 'App/Models/Listing'
import Database from '@ioc:Adonis/Lucid/Database'

test.group('Listing Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1 suggestListing - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const fakeDrive = Drive.fake()
    const fakeImgMain = await file.generatePng('1mb')
    const fakeImgSub1 = await file.generatePng('1mb')
    const fakeImgSub2 = await file.generatePng('1mb')

    const northRegion = await Region.findByOrFail('slug', 'north')
    const response = await client
      .post(`${baseUrl}/listings`)
      .loginAs(user!)
      .fields({
        listing_type: ListingType.care,
        region: northRegion.slug,
        full_address: '23, Jalan Limau Manis 6, Johor',
        longitude: 100,
        latitude: 55,
        name: 'IOI Mall Demo Test Case normal user suggested',
        address_name: 'IOI Mall Mother Caring room',
        note: 'A recommendation for adult who are carrying toddler or infant',
        description:
          'The room has various facilities such as diaper changing platform, water dispenser, sink, toilet paper, sofa, etc.',
        main_image_url:
          'https://makchic.com/wp-content/uploads/2014/08/paradigm-1-e1661003697788-768x1024.jpg',
        sub_image_urls: [
          'https://makchic.com/wp-content/uploads/2014/08/Tropicana-Gardens-Nursing-Station.jpg',
          'https://makchic.com/wp-content/uploads/2014/08/Tropicana-Gardens-Nursing-Station.jpg',
        ],
        keywords: ['diaper_changing', 'ioi_mall_johor'],
        amenities: [
          'wash-basin',
          'diaper-changing',
          'hot-and-cold-water-dispenser',
          'seat',
          'automated-disinfection',
        ],
        company_name: 'IOI Group',
        postal_code: '50400',
        contact_number: '65123456',
        country_dial_code: '65',
        opening_hours: '10am to 10pm',
      })
      .file('main_image_file', fakeImgMain.contents, { filename: fakeImgMain.name })
      .file('sub_image_files', fakeImgSub1.contents, { filename: fakeImgSub1.name })
      .file('sub_image_files', fakeImgSub2.contents, { filename: fakeImgSub2.name })

    assert.isTrue(await fakeDrive.exists(remotePathListing + '/' + fakeImgMain.name))
    assert.isTrue(await fakeDrive.exists(remotePathListing + '/' + fakeImgSub1.name))
    assert.isTrue(await fakeDrive.exists(remotePathListing + '/' + fakeImgSub2.name))

    Drive.restore()

    const data = response.body().data

    assert.properties(data, [
      'id',
      'position_id',
      'region_id',
      'verified_by',
      'suggested_by',
      'name',
      'listing_type',
      'company_name',
      'address_name',
      'description',
      'full_address',
      'contact_number',
      'keywords',
      'postal_code',
      'usage_durations',
      'usage_extension_durations',
      'max_number_of_usage_extensions',
      'number_of_private_feeding_rooms',
      'number_of_diaper_changing_mats',
      'diaper_changing_mat_type',
      'country_dial_code',
      'humidity',
      'temperature',
      'opening_hours',
      'pi_id',
      'pi_last_updated',
      'api_key',
      'lock_id',
      'lock_master_pin',
      'lock_bluetooth_admin_key',
      'door_is_lockable',
      'status',
      'is_usage_extendable',
      'is_verified',
      'is_hidden',
      'firestore_id',
      'created_at',
      'updated_at',
      'listing_files',
      'amenities',
      'activities',
      'position',
      'full_contact_number',
      'average_experience_ratings',
      'total_experience_ratings',
      'total_sessions',
      'five_stars_count',
      'four_stars_count',
      'three_stars_count',
      'two_stars_count',
      'one_star_count',
      'distance',
    ])

    assert.properties(
      data['listing_files'][0],
      data['listing_files'].length > 0
        ? [
            'id',
            'listing_id',
            'uploaded_by',
            'image_url',
            'is_main',
            'is_approved',
            'is_hidden',
            'reviewed_by',
            'not_approved_reason',
            'created_at',
            'updated_at',
          ]
        : []
    )

    assert.properties(
      data['amenities'][0],
      data['amenities'].length > 0
        ? [
            'id',
            'name',
            'description',
            'image_url',
            'is_hidden',
            'created_at',
            'updated_at',
            'slug',
          ]
        : []
    )

    assert.properties(data['position'], [
      'id',
      'geo_hash',
      'coordinate',
      'is_hidden',
      'created_at',
      'updated_at',
    ])
  })

  test('2.1 findListings - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrl}/listings`).loginAs(user!)
    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'name',
        'full_address',
        'opening_hours',
        'created_at',
        'updated_at',
        'listing_files',
        'position',
        'average_experience_ratings',
        'total_experience_ratings',
        'total_sessions',
        'five_stars_count',
        'four_stars_count',
        'three_stars_count',
        'two_stars_count',
        'one_star_count',
        'distance',
      ])
    }
  })

  test('2.2 findListings - invalid listing type', async ({ client }) => {
    const type = 'not_existing_listing_type'
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrl}/listings?type=${type}`).loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: `Invalid listing type: ${type}, valid listing type: ${Object.values(ListingType)}`,
    })
  })

  test('3.1 findListing - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query()
      .where('is_hidden', false)
      .andWhere('is_verified', true)
      .firstOrFail()
    const response = await client.get(`${baseUrl}/listings/${listing.id}`).loginAs(user!)
    const data = response.body().data

    assert.properties(data, [
      'id',
      'name',
      'listing_type',
      'company_name',
      'full_address',
      'opening_hours',
      'status',
      'created_at',
      'updated_at',
      'listing_files',
      'activities',
      'amenities',
      'position',
      'listing_ratings',
      'average_experience_ratings',
      'total_experience_ratings',
      'total_sessions',
      'five_stars_count',
      'four_stars_count',
      'three_stars_count',
      'two_stars_count',
      'one_star_count',
      'distance',
    ])
  })

  test('3.2 findListing - listing is not verified', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query()
      .where('is_hidden', false)
      .andWhere('is_verified', false)
      .firstOrFail()
    const response = await client.get(`${baseUrl}/listings/${listing.id}`).loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Listing not found',
    })
  })

  test('3.3 findListing - listing not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrl}/listings/${'not_exist_id'}`).loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Listing not found',
    })
  })

  test('4 findListingsByPosition - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().limit(1).first()
    const response = await client
      .get(
        `${baseUrl}/listings/search-position?lat=${listing?.position.coordinate.y}&lon=${listing?.position.coordinate.x}`
      )
      .loginAs(user!)
    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'name',
        'full_address',
        'opening_hours',
        'created_at',
        'updated_at',
        'listing_files',
        'position',
        'average_experience_ratings',
        'total_experience_ratings',
        'total_sessions',
        'five_stars_count',
        'four_stars_count',
        'three_stars_count',
        'two_stars_count',
        'one_star_count',
        'distance',
      ])
    }
  })

  test('5 findListingsByKeywords - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().limit(1).first()
    const response = await client
      .get(`${baseUrl}/listings/search-keywords?keywords=${listing?.keywords.join(',')}`)
      .loginAs(user!)
    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'name',
        'full_address',
        'opening_hours',
        'created_at',
        'updated_at',
        'position',
        'listing_files',
        'average_experience_ratings',
        'total_experience_ratings',
        'total_sessions',
        'five_stars_count',
        'four_stars_count',
        'three_stars_count',
        'two_stars_count',
        'one_star_count',
        'distance',
      ])
    }
  })

  test('6 findSuggestedListings - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrl}/listings/search-own-suggested`).loginAs(user!)
    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'name',
        'full_address',
        'opening_hours',
        'created_at',
        'updated_at',
        'position',
        'listing_files',
        'average_experience_ratings',
        'total_experience_ratings',
        'total_sessions',
        'five_stars_count',
        'four_stars_count',
        'three_stars_count',
        'two_stars_count',
        'one_star_count',
        'distance',
      ])
    }
  })
})
