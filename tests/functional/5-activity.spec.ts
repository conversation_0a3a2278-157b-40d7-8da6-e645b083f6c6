import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import Activity from 'App/Models/Activity'
import User from 'App/Models/User'

test.group('Activity Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1 findActivities - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get('/api/v1/activities').loginAs(user!)
    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'name',
        'description',
        'image_url',
        'is_hidden',
        'created_at',
        'updated_at',
        'slug',
      ])
    }
  })

  test('2.1 findActivity - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activity = await Activity.findByOrFail('slug', 'breastfeeding')
    const response = await client.get(`/api/v1/activities/${activity.id}`).loginAs(user!)
    const data = response.body().data

    assert.properties(data, [
      'id',
      'name',
      'description',
      'image_url',
      'is_hidden',
      'created_at',
      'updated_at',
      'slug',
    ])
  })

  test('2.2 findActivity - activity not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`/api/v1/activities/${'not_exist_activity_id'}`)
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Activity not found',
    })
  })

  test('3.1 findActivityWithSlug - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const activity = await Activity.query().whereNotNull('slug').first()
    const response = await client
      .get(`/api/v1/activities/search-slug/${activity?.slug}`)
      .loginAs(user!)
    const data = response.body().data

    assert.properties(data, [
      'id',
      'name',
      'description',
      'image_url',
      'is_hidden',
      'created_at',
      'updated_at',
      'slug',
    ])
  })

  test('3.2 findActivityWithSlug - activity not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`/api/v1/activities/search-slug/${'not_exist_slug'}`)
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Activity not found',
    })
  })
})
