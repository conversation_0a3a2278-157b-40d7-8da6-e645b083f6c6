import { test } from '@japa/runner'
import { baseUrl, remotePathListingFlag } from 'App/utils'
import User from 'App/Models/User'
import { ListingFlagCategory } from 'Contracts/listing_flag_type'
import Listing from 'App/Models/Listing'
import ListingFlag from 'App/Models/ListingFlag'
import Database from '@ioc:Adonis/Lucid/Database'
import Drive from '@ioc:Adonis/Core/Drive'
import { file } from '@ioc:Adonis/Core/Helpers'

test.group('Listing Flag Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 createListingFlag - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listing = await Listing.query().where('is_hidden', false)
    const fakeDrive = Drive.fake()
    const fakeImgRef = await file.generatePng('1mb')
    const fakeImgRef2 = await file.generatePng('1mb')

    const response = await client
      .post(`${baseUrl}/listing-flags/${listing[2].id}`)
      .loginAs(user!)
      .fields({
        category: ListingFlagCategory.other,
        reason: 'The aircond is not working.',
      })
      .file('reference_image_files.[0]', fakeImgRef.contents, { filename: fakeImgRef.name })
      .file('reference_image_files.[1]', fakeImgRef2.contents, { filename: fakeImgRef2.name })

    const data = response.body().data

    assert.isTrue(await fakeDrive.exists(remotePathListingFlag + '/' + fakeImgRef.name))
    assert.isTrue(await fakeDrive.exists(remotePathListingFlag + '/' + fakeImgRef2.name))

    Drive.restore()

    assert.properties(data, [
      'category',
      'reason',
      'listing_id',
      'user_id',
      'id',
      'reference_images',
      'created_at',
      'updated_at',
    ])
  })

  test('1.2 createListingFlag - listing not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .post(`${baseUrl}/listing-flags/${'not_exist_id'}`)
      .loginAs(user!)
      .json({
        category: ListingFlagCategory.other,
        reason: 'The aircond is not working.',
      })

    response.assertBodyContains({
      success: false,
      message: 'Listing not found',
    })
  })

  test('1.3 createListingFlag - already has existing not reviewed listing flag for the listing', async ({
    client,
  }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingFlag = await ListingFlag.query()
      .where('reason', 'Demo already has an existing unreviewed listing flag for a listing')
      .andWhere('user_id', user.id)
      .andWhereNull('reviewed_at')
      .firstOrFail()
    const response = await client
      .post(`${baseUrl}/listing-flags/${listingFlag.listingId}`)
      .loginAs(user!)
      .json({
        category: ListingFlagCategory.other,
        reason: 'Listing Flag by ricky user.',
      })

    response.assertBodyContains({
      success: false,
      message:
        'A flag for this listing already exist and is not yet reviewed by an admin, can only raise one flag for a listing at a time.',
    })
  })

  test('2.1 retrieveListingFlag - success', async ({ assert, client }) => {
    const listingflag = await ListingFlag.findByOrFail(
      'reason',
      'Demo already has an existing unreviewed listing flag for a listing'
    )
    const user = await User.findByOrFail('id', listingflag.userId)
    const response = await client.get(`${baseUrl}/listing-flags/${listingflag.id}`).loginAs(user!)
    const data = response.body().data
    assert.properties(data, [
      'id',
      'listing_id',
      'user_id',
      'reason',
      'category',
      'reviewed_at',
      'action',
      'action_reason',
      'action_by',
      'created_at',
      'updated_at',
    ])
  })

  test('2.2 retrieveListingFlag - listing flag not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrl}/listing-flags/${'not_existing_id'}`)
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Listing Flag not found',
    })
  })

  test('3 retrieveAllListingFlagsAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrl}/listing-flags`).loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'listing_id',
        'user_id',
        'reason',
        'category',
        'reviewed_at',
        'action',
        'action_reason',
        'action_by',
        'created_at',
        'updated_at',
      ])
    }
  })
})
