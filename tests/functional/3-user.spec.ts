import { test } from '@japa/runner'
import User from 'App/Models/User'
import { UserGender } from 'Contracts/users'
import Drive from '@ioc:Adonis/Core/Drive'
import { file } from '@ioc:Adonis/Core/Helpers'
import Listing from 'App/Models/Listing'
import { UserType } from 'Contracts/user_type'
import { remotePathUserProfile } from 'App/utils'
import Database from '@ioc:Adonis/Lucid/Database'
import UserFavoriteListing from 'App/Models/UserFavoriteListing'
import { logOnFailure } from '../helper'

test.group('User Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 createUser - success email', async ({ assert, client }) => {
    const response = await client.post('/api/v1/users').json({
      new_email: '<EMAIL>',
      password: '********',
      passwordConfirm: '********',
      first_name: 'He<PERSON>',
      last_name: 'Jack',
      longitude: 100.234,
      latitude: 55.33,
      username: 'newUser123',
    })

    const responseBody = response.body()
    const debugInfo = {
      status: response.status(),
      body: responseBody,
      headers: response.headers(),
    }

    logOnFailure(() => {
      assert.properties(responseBody.data.user, [
        'email_address',
        'favorite_listings',
        'full_name',
        'id',
        'username',
      ])
    }, debugInfo)
  })

  test('1.2 createUser - success mobile number', async ({ assert, client }) => {
    const response = await client.post('/api/v1/users').json({
      new_mobile_number: '88123456',
      country_dial_code: '+65',
      password: '********',
      passwordConfirm: '********',
      first_name: 'Heng',
      last_name: 'Jack',
      longitude: 100.234,
      latitude: 55.33,
      username: 'newUser6588',
    })

    response.assertBodyContains({
      success: true,
      data: {
        user: {}, // Just check user object exists
      },
      message: 'User created successfully',
    })

    const responseBody = response.body()
    const debugInfo = {
      status: response.status(),
      body: responseBody,
      headers: response.headers(),
    }

    logOnFailure(() => {
      assert.properties(responseBody.data.user, [
        'favorite_listings',
        'full_name',
        'id',
        'username',
        'mobile_number',
      ])
    }, debugInfo)
  })

  test('1.3 createUser - email already exist', async ({ client }) => {
    const response = await client.post('/api/v1/users').json({
      new_email: '<EMAIL>',
      password: '********',
      passwordConfirm: '********',
      first_name: 'Heng',
      last_name: 'Jack',
      longitude: 100.234,
      latitude: 55.33,
      username: 'YUsername',
    })

    response.assertBodyContains({
      success: false,
      message: 'Email address already exist',
    })
  })

  test('1.4 createUser - phone already exist', async ({ client }) => {
    const response = await client.post('/api/v1/users').json({
      new_mobile_number: '********',
      country_dial_code: '65',
      password: '********',
      passwordConfirm: '********',
      first_name: 'Heng',
      last_name: 'Jack',
      longitude: 100.234,
      latitude: 55.33,
      username: 'XUsername',
    })

    response.assertBodyContains({
      success: false,
      message: 'Phone number already exist',
    })
  })

  test('1.5 createUser - not phone or email as register account', async ({ client }) => {
    const response = await client.post('/api/v1/users').json({
      new_email: 'Not_email_or_phone',
      password: '********',
      passwordConfirm: '********',
      first_name: 'Heng',
      last_name: 'Jack',
      longitude: 100.234,
      latitude: 55.33,
      username: 'Not_email_or',
    })

    response.assertBodyContains({
      success: false,
      message: 'Please provide either phone or email',
    })
  })

  test('2.1 findUsers - success', async ({ assert, client }) => {
    const user = await User.findBy('email_address', '<EMAIL>')

    const response = await client.get('/api/v1/users').loginAs(user!)

    const responseBody = response.body()
    const debugInfo = {
      status: response.status(),
      body: responseBody,
      headers: response.headers(),
    }

    logOnFailure(() => {
      assert.properties(responseBody, ['meta', 'data'])
      assert.notEmpty(response.body().data)
    }, debugInfo)
  })

  test('2.2 findUsers - valid user type filter param', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const userType = Object.values(UserType)[0]
    const response = await client.get(`/api/v1/users?type=${userType}`).loginAs(user!)

    const responseBody = response.body()
    const debugInfo = {
      status: response.status(),
      body: responseBody,
      headers: response.headers(),
    }

    logOnFailure(() => {
      assert.properties(responseBody, ['meta', 'data'])
      assert.notEmpty(response.body().data)
    }, debugInfo)
  })

  test('2.3 findUsers - invalid user type filter param', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const type = 'NOT_EXIST_USER_TYPE'
    const response = await client.get(`/api/v1/users?type=${type}`).loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: `Invalid user type: ${type}, valid user type: ${Object.values(UserType)}`,
    })
  })

  test('3.1 findUser - success', async ({ assert, client }) => {
    const user = await User.findBy('email_address', '<EMAIL>')
    const findUser = await User.findBy('email_address', '<EMAIL>')

    const response = await client.get(`/api/v1/users/${findUser?.id}`).loginAs(user!)
    const data = response.body().data
    assert.properties(data, [
      'id',
      'user_type',
      'position_id',
      'username',
      'email_address',
      'first_name',
      'last_name',
      'photo_url',
      'share_code',
      'gender',
      'birthday',
      'children_birthdays',
      'mobile_number',
      'company_name',
      'country_name',
      'country_code',
      'country_dial_code',
      'nationality',
      'timezone',
      'auth_provider',
      'is_admin_verified',
      'is_email_address_verified',
      'is_mobile_number_verified',
      'is_singpass_verified',
      'passport_number',
      'is_passport_verified',
      'is_gomama_verified',
      'is_hidden',
      'firestore_id',
      'deleted_at',
      'created_at',
      'updated_at',
      'full_name',
      'full_mobile_number',
      'number_of_sessions',
    ])
  })

  test('3.2 findUser - user not exist', async ({ client }) => {
    const user = await User.findBy('email_address', '<EMAIL>')

    const response = await client.get(`/api/v1/users/${'not_exist'}`).loginAs(user!)
    response.assertBodyContains({
      success: false,
      message: 'User not found',
    })
  })

  test('3.3 findUser - user exist but is hidden', async ({ client }) => {
    const user = await User.findBy('email_address', '<EMAIL>')
    const hiddenUser = await User.query().where('is_hidden', true).first()

    const response = await client.get(`/api/v1/users/${hiddenUser?.id}`).loginAs(user!)
    response.assertBodyContains({
      success: false,
      message: 'User not found',
    })
  })

  test('4.1 findUserByUsername - success', async ({ client }) => {
    const user = await User.findBy('email_address', '<EMAIL>')
    const userWithUsername = await User.query()
      .where('username', '!=', '')
      .andWhereNotNull('username')
      .andWhere('is_hidden', false)
      .firstOrFail()

    const response = await client
      .get(`/api/v1/users/search-username/${userWithUsername.username}`)
      .loginAs(user!)
    response.assertBodyContains({
      success: true,
    })
  })

  test('4.2 findUserByUsername - user not exist', async ({ client }) => {
    const user = await User.findBy('email_address', '<EMAIL>')

    const response = await client.get(`/api/v1/users/search-username/${'not_exist'}`).loginAs(user!)
    response.assertBodyContains({
      success: false,
    })
  })

  test('4.3 findUserByUsername - user exist but is hidden', async ({ client }) => {
    const user = await User.findBy('email_address', '<EMAIL>')
    const hiddenUser = await User.query()
      .where('is_hidden', true)
      .andWhere('username', '!=', '')
      .andWhereNotNull('username')
      .first()
    const response = await client
      .get(`/api/v1/users/search-username/${hiddenUser?.username}`)
      .loginAs(user!)
    response.assertBodyContains({
      success: false,
    })
  })

  test('5 findMyFavoriteListings - success', async ({ client, assert }) => {
    const user = await User.findBy('email_address', '<EMAIL>')

    const response = await client.get(`/api/v1/me/favorite-listings`).loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'name',
        'listing_type',
        'company_name',
        'address_name',
        'description',
        'full_address',
        'contact_number',
        'number_of_private_feeding_rooms',
        'opening_hours',
        'status',
        'created_at',
        'updated_at',
        'listing_files',
        'position',
        'average_experience_ratings',
        'total_experience_ratings',
        'total_sessions',
        'five_stars_count',
        'four_stars_count',
        'three_stars_count',
        'two_stars_count',
        'one_star_count',
        'distance',
      ])
    }
  })

  test('6.1 updateUser - success', async ({ assert, client }) => {
    const fakeDrive = Drive.fake()
    const fakeImg = await file.generatePng('1mb')
    const user = await User.findBy('email_address', '<EMAIL>')

    // console.log('Reminder: User update Image is not included by test case 5.1.')

    const response = await client
      .put(`/api/v1/users`)
      .loginAs(user!)
      .fields({
        longitude: 99.23,
        latitude: 43.45,
        username: 'Heng123',
        first_name: 'Heng',
        last_name: 'Hensen',
        gender: 'male',
        birthday: '2000-02-29',
        children_birthdays: ['2025-03-13', '2025-01-11'], // Note: Unknown bug by Adonis, fields() will convert 1 element array to non array value, hence filled with 2 elements.
        mobile_number: '0168985360',
        company_name: 'Gaincue Sdn. Bhd.',
        country_name: 'Malaysia',
        country_code: 'my',
        country_dial_code: '+60',
        nationality: 'Malaysian',
        password: '123456',
        reset_password_otp: 'reset1',
      })
      .file('profile_image', fakeImg.contents, { filename: fakeImg.name })

    const data = response.body().data

    assert.exists(await fakeDrive.exists(remotePathUserProfile + '/' + fakeImg.name))
    Drive.restore()

    assert.properties(data, [
      'id',
      'username',
      'email_address',
      'photo_url',
      'gender',
      'birthday',
      'children_birthdays',
      'mobile_number',
      'auth_provider',
      'is_admin_verified',
      'is_email_address_verified',
      'is_mobile_number_verified',
      'is_singpass_verified',
      'is_gomama_verified',
      'full_name',
      'full_mobile_number',
    ])
  })

  test('6.2 updateUser - existing mobile number', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const otherUserPhone = await User.query()
      .where('mobile_number', '!=', '')
      .andWhereNotNull('mobile_number')
      .andWhere('id', '!=', user.id)
      .first()

    const response = await client.put(`/api/v1/users`).loginAs(user!).json({
      mobile_number: otherUserPhone?.mobileNumber,
    })

    response.assertBodyContains({
      success: false,
      message: 'Mobile number already registered under an existing user',
    })
  })

  test('6.3 updateUser - OTP Password Change Invalid', async ({}) => {
    console.log('TODO: To Be Working on test case 5.3 OTP')
  })

  test('7.1 gomamaVerify - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')

    const response = await client
      .post(`/api/v1/me/gomama-verify`)
      .loginAs(user!)
      .fields({
        first_name: 'NUR AISHA BINTE',
        last_name: 'FAIZAL VERIFY',
        birthday: '1999-12-23',
        children_birthdays: ['2019-12-25', '2021-08-29'],
        mobile_number: '82381784',
        email: user.emailAddress,
        gender: UserGender.female,
        fin_or_passport: 'P12A',
      })
      .file('user_selfie_file', fakeImg.contents, { filename: fakeImg.name })
      .file('user_selfie_file_copy', fakeImg.contents, { filename: fakeImg.name })

    response.assertBodyContains({
      success: true,
      message: 'Successfully submitted Gomama verification request.',
    })

    Drive.restore()
  })

  test('7.2 gomamaVerify - existing gomama verification request', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')

    const response = await client
      .post(`/api/v1/me/gomama-verify`)
      .loginAs(user!)
      .fields({
        first_name: 'Jensen',
        last_name: 'Jonathan',
        birthday: '1997-04-23',
        children_birthdays: [],
        mobile_number: user.mobileNumber,
        email: user.emailAddress,
        gender: UserGender.male,
        fin_or_passport: 'P12A',
      })
      .file('user_selfie_file', fakeImg.contents, { filename: fakeImg.name })
      .file('user_selfie_file_copy', fakeImg.contents, { filename: fakeImg.name })

    response.assertBodyContains({
      success: false,
      message:
        'Only one Gomama verification request allowed at a time, please wait admin to review the latest verification request.',
    })

    Drive.restore()
  })

  test('7.3 gomamaVerify - user has already been verified by gomama', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    Drive.fake()
    const fakeImg = await file.generatePng('1mb')

    const response = await client
      .post(`/api/v1/me/gomama-verify`)
      .loginAs(user!)
      .fields({
        first_name: 'NUR AISHA BINTE',
        last_name: 'FAIZAL VERIFY',
        birthday: '1999-12-23',
        children_birthdays: ['2019-12-25', '2021-08-29'],
        mobile_number: '82381784',
        email: user.emailAddress,
        gender: UserGender.female,
        fin_or_passport: 'P12A',
      })
      .file('user_selfie_file', fakeImg.contents, { filename: fakeImg.name })
      .file('user_selfie_file_copy', fakeImg.contents, { filename: fakeImg.name })

    response.assertBodyContains({
      success: false,
      message: 'The user has already been verified by Gomama.',
    })

    Drive.restore()
  })

  test('8.1 singpassVerify - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client.post(`/api/v1/me/singpass-verify`).loginAs(user!).json({
      full_name: 'NUR AISHA BINTE FAIZAL VERIFY',
      birthday: '1999-12-23',
      children_birthdays: [],
      mobile_number: '83727386',
      email: user.emailAddress,
      gender: UserGender.female,
      nric: '******E56E',
    })

    response.assertBodyContains({
      success: true,
      message: 'Successfully submitted Singpass verification request.',
    })
  })

  test('8.2 singpassVerify - existing singpass verification request', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client.post(`/api/v1/me/singpass-verify`).loginAs(user!).json({
      full_name: 'NUR AISHA BINTE FAIZAL VERIFY',
      birthday: '1999-12-23',
      children_birthdays: [],
      mobile_number: user.mobileNumber,
      email: user.emailAddress,
      gender: UserGender.female,
      nric: '******E56E',
    })

    response.assertBodyContains({
      success: false,
      message:
        'Only one Singpass verification request allowed at a time, please wait admin to review the latest verification request.',
    })
  })

  test('8.3 singpassVerify - user has already been verified by singpass', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client.post(`/api/v1/me/singpass-verify`).loginAs(user!).json({
      full_name: 'MOCK SINGPASS VERIFIED FAIZAL USER',
      birthday: '1999-12-23',
      children_birthdays: [],
      mobile_number: user.mobileNumber,
      email: user.emailAddress,
      gender: UserGender.female,
      nric: '******E7UE',
    })

    response.assertBodyContains({
      success: false,
      message: 'The user has already been verified by Singpass.',
    })
  })

  test('9.1 updateFavoriteListing - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const listing = await Listing.query().where('name', 'Demo Update Listing 1').firstOrFail()

    const response = await client.put(`/api/v1/me/favorite-listings`).loginAs(user!).json({
      listing_id: listing.id,
    })

    response.assertBodyContains({
      success: true,
      message: 'Updated user favorite listings successfully',
    })
  })

  test('9.2 updateFavoriteListing - remove a favorite listing', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const listing = await Listing.query().where('name', 'Go!mama Pod SKCC001').firstOrFail()

    const response = await client.put(`/api/v1/me/favorite-listings`).loginAs(user!).json({
      listing_id: listing.id,
    })

    const isExist = await UserFavoriteListing.query()
      .where('listing_id', listing.id)
      .andWhere('user_id', user.id)
      .first()

    assert.isNull(isExist)

    response.assertBodyContains({
      success: true,
      message: 'Updated user favorite listings successfully',
    })
  })

  test('10.1 deleteUser - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client.delete(`/api/v1/users`).loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully soft deleted an user.',
    })
  })

  test('10.2 deleteUser - user has already been soft deleted', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client.delete(`/api/v1/users`).loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'User has already been soft deleted.',
    })
  })
})
