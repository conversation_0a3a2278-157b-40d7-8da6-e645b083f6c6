import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import User from 'App/Models/User'

test.group('Auth Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 login - success', async ({ assert, client }) => {
    const response = await client
      .post('/api/v1/login')
      .json({ login_account: '<EMAIL>', password: 'abcd1234' })
    const data = response.body().data

    assert.properties(data, ['user', 'token'])

    assert.properties(data.user, [
      'birthday',
      'children_birthdays',
      'email_address',
      'full_name',
      'gender',
      'id',
      'is_admin_verified',
      'is_email_address_verified',
      'is_gomama_verified',
      'is_mobile_number_verified',
      'is_singpass_verified',
      'mobile_number',
      'photo_url',
      'username',
    ])

    assert.properties(data.token, ['type', 'token'])
  })

  test('1.2 login - invalid password', async ({ client }) => {
    const response = await client
      .post('/api/v1/login')
      .json({ login_account: '<EMAIL>', password: 'invalid_password_12345678' })

    response.assertBodyContains({
      success: false,
      message: 'Password not match',
    })
  })

  test('1.3 login - user not exist', async ({ client }) => {
    const response = await client
      .post('/api/v1/login')
      .json({ login_account: '<EMAIL>', password: 'invalid_password_12345678' })

    response.assertBodyContains({
      success: false,
      message: 'User not exist',
    })
  })

  test('1.4 login - existing user but hidden', async ({ client }) => {
    const response = await client
      .post('/api/v1/login')
      .json({ login_account: '<EMAIL>', password: 'abcd1234' })

    response.assertBodyContains({
      success: false,
      message: 'User not exist',
    })
  })

  test('1.5 login - existing user but soft deleted', async ({ client }) => {
    const response = await client
      .post('/api/v1/login')
      .json({ login_account: '<EMAIL>', password: 'abcd1234' })

    response.assertBodyContains({
      success: false,
      message: 'User not exist',
    })
  })

  test('2 logout - success', async ({ client }) => {
    const user = await User.findBy('email_address', '<EMAIL>')

    const response = await client.post('/api/v1/me/logout').guard('api').loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully logout',
    })
  })
})
