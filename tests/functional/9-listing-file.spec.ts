import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import ListingFile from 'App/Models/ListingFile'

test.group('Listing File Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 findListingFile - success', async ({ assert, client }) => {
    const listingFile = await ListingFile.query().where('is_hidden', false).limit(1).first()
    const response = await client.get(`/api/v1/listing-files/${listingFile?.id}`)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'listing_id',
      'uploaded_by',
      'image_url',
      'is_main',
      'is_approved',
      'is_hidden',
      'reviewed_by',
      'not_approved_reason',
      'created_at',
      'updated_at',
    ])
  })

  test('1.2 findListingFile - listing file not found', async ({ client }) => {
    const response = await client.get(`/api/v1/listing-files/${'not_exist_id'}`)

    response.assertBodyContains({
      success: false,
      message: 'Listing not found',
    })
  })
})
