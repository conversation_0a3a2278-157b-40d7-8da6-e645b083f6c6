import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import Listing from 'App/Models/Listing'
import Session from 'App/Models/Session'
import User from 'App/Models/User'
import { baseUrl } from 'App/utils'

test.group('Session Controller', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 createSession - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingChosen = await Listing.findByOrFail('name', 'Demo Session Listing With Lock Id')

    const response = await client
      .post(`${baseUrl}/sessions`)
      .json({ listing_id: listingChosen.id, lock_custom_pin: '123456abcd' })
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'listing_id',
      'user_id',
      'lock_bluetooth_guest_key',
      'lock_custom_pin',
      'lock_daily_pin',
      'lock_hourly_pin',
      'lock_one_time_pin',
      'started_at',
      'expected_ended_at',
      'id',
      'created_at',
      'updated_at',
      'expected_usage_duration',
    ])
  })

  test('1.2 createSession - user currently has an on-going session', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingChosen = await Listing.findByOrFail('name', 'Demo Session Listing With Lock Id')

    const response = await client
      .post(`${baseUrl}/sessions`)
      .json({ listing_id: listingChosen.id, lock_custom_pin: '123456abcd' })
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'You still have an on-going session',
      code: 'ERR_SESSION_ONGOING',
    })
  })

  test('1.3 createSession - listing without lock cannot create session', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingChosen = await Listing.findByOrFail('name', 'Listing With No Lock')

    const response = await client
      .post(`${baseUrl}/sessions`)
      .json({ listing_id: listingChosen.id, lock_custom_pin: '123456abcd' })
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: "You don't have to unlock this listing",
      code: 'ERR_SESSION_NO_LOCK',
    })
  })

  test('1.4 createSession - listing currently has an on-going session', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingChosen = await Listing.findByOrFail('name', 'Go!mama Pod 106')

    const response = await client
      .post(`${baseUrl}/sessions`)
      .json({ listing_id: listingChosen.id, lock_custom_pin: '123456abcd' })
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message:
        'There is an existing on-going session for the listing, a new session can only be created after its last session has ended',
      code: 'ERR_SESSION_EXISTING',
    })
  })

  test('1.5 createSession - not gomama type listing', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingChosen = await Listing.findByOrFail('name', 'Non-gomama Listing Type')

    const response = await client
      .post(`${baseUrl}/sessions`)
      .json({ listing_id: listingChosen.id, lock_custom_pin: '123456abcd' })
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Only GO!Mama pods can be accessed',
      code: 'ERR_ACCESS_RESTRICTED',
    })
  })

  test('1.6 createSession - some pin request failed', async ({}) => {
    console.log('Note: This test case cannot be replicated, it relies on third party API igloohome')
    console.log(
      'If some pin request fail, this object will be returned ' +
        JSON.stringify({
          success: false,
          message: 'Some pins request failed',
        })
    )
  })

  test('2 findSessions - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client.get(`${baseUrl}/sessions`).loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'listing_id',
        'user_id',
        'lock_bluetooth_guest_key',
        'lock_custom_pin',
        'lock_daily_pin',
        'lock_hourly_pin',
        'lock_one_time_pin',
        'started_at',
        'expected_ended_at',
        'id',
        'created_at',
        'updated_at',
        'expected_usage_duration',
      ])
    }
  })

  test('3.1 findSession - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const session = await Session.findByOrFail('user_id', user.id)

    const response = await client.get(`${baseUrl}/sessions/${session.id}`).loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'listing_id',
      'user_id',
      'lock_bluetooth_guest_key',
      'lock_custom_pin',
      'lock_daily_pin',
      'lock_hourly_pin',
      'lock_one_time_pin',
      'started_at',
      'expected_ended_at',
      'actual_ended_at',
      'number_of_usage_extensions',
      'is_hidden',
      'firestore_id',
      'created_at',
      'updated_at',
      'listing',
      'expected_usage_duration',
    ])
  })

  test('3.2 findSession - session not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client.get(`${baseUrl}/sessions/${'not_exist_id'}`).loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Session not found',
    })
  })

  test('4.1 findActiveSession - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client.get(`${baseUrl}/sessions/active`).loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'listing_id',
      'user_id',
      'lock_bluetooth_guest_key',
      'lock_custom_pin',
      'lock_daily_pin',
      'lock_hourly_pin',
      'lock_one_time_pin',
      'started_at',
      'expected_ended_at',
      'actual_ended_at',
      'number_of_usage_extensions',
      'is_hidden',
      'firestore_id',
      'created_at',
      'updated_at',
      'expected_usage_duration',
    ])
  })

  test('4.2 findActiveSession - no active session', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client.get(`${baseUrl}/sessions/active`).loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'No active session',
    })
  })

  test('5.1 extendSession - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client.post(`${baseUrl}/sessions/extend`).loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'listing_id',
      'user_id',
      'lock_bluetooth_guest_key',
      'lock_custom_pin',
      'lock_daily_pin',
      'lock_hourly_pin',
      'lock_one_time_pin',
      'started_at',
      'expected_ended_at',
      'actual_ended_at',
      'number_of_usage_extensions',
      'is_hidden',
      'firestore_id',
      'created_at',
      'updated_at',
      'listing',
      'expected_usage_duration',
    ])
  })

  test('5.2 extendSession - reach maximum number of session extension', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client.post(`${baseUrl}/sessions/extend`).loginAs(user!)

    response.assertBodyContains({ success: false, message: 'No active session found' })
  })

  test('5.3 extendSession - exceed maximum extend number', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.post(`${baseUrl}/sessions/extend`).loginAs(user!)
    const activeSession = await Session.query()
      .whereNull('actual_ended_at')
      .andWhere('user_id', user.id)
      .andWhere('is_hidden', false)
      .preload('listing', (query) => {
        query.preload('listingFiles', (query) => {
          query.where('is_hidden', false)
        })
      })
      .first()

    response.assertBodyContains({
      success: false,
      message: 'Session has reached maximum number of extension for the said listing.',
      listing_maximum_extension: activeSession?.listing.maxNumberOfUsageExtensions ?? 1,
    })
  })

  test('6.1 endSession - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.post(`${baseUrl}/sessions/end`).loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'listing_id',
      'user_id',
      'lock_bluetooth_guest_key',
      'lock_custom_pin',
      'lock_daily_pin',
      'lock_hourly_pin',
      'lock_one_time_pin',
      'started_at',
      'expected_ended_at',
      'actual_ended_at',
      'number_of_usage_extensions',
      'is_hidden',
      'firestore_id',
      'created_at',
      'updated_at',
      'actual_usage_duration',
      'expected_usage_duration',
    ])
  })

  test('6.2 endSession - no active session', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.post(`${baseUrl}/sessions/end`).loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'No active session found',
    })
  })
})
