#!/usr/bin/env node

/**
 * MQTT Integration Test Script
 * This script tests the MQTT functionality by simulating session events
 */

const mqtt = require('mqtt');

// MQTT Configuration
const MQTT_CONFIG = {
  host: 'localhost',
  port: 1883,
  clientId: 'gomama_test_client',
  username: 'test_client',
  password: 'test_pass'
};

// Test data
const TEST_SESSION_DATA = {
  id: 'test-session-123',
  userId: 'test-user-456',
  listingId: 'test-listing-789',
  startedAt: new Date().toISOString(),
  expectedEndedAt: new Date(Date.now() + 3600000).toISOString() // 1 hour from now
};

const TEST_LISTING_DATA = {
  listingId: 'test-listing-789',
  status: 'occupied'
};

function createMqttMessage(type, data) {
  return {
    timestamp: Date.now(),
    source: 'test_script',
    type,
    data
  };
}

async function testMqttIntegration() {
  console.log('🧪 Starting MQTT Integration Test...\n');

  const client = mqtt.connect(`mqtt://${MQTT_CONFIG.host}:${MQTT_CONFIG.port}`, {
    clientId: MQTT_CONFIG.clientId,
    username: MQTT_CONFIG.username,
    password: MQTT_CONFIG.password,
    keepalive: 60,
    connectTimeout: 30000,
    clean: true
  });

  return new Promise((resolve, reject) => {
    let testsPassed = 0;
    const totalTests = 6;

    client.on('connect', () => {
      console.log('✅ Connected to MQTT broker');
      
      // Test 1: Subscribe to session topics
      console.log('\n📡 Test 1: Subscribing to session topics...');
      client.subscribe('gomama/sessions/+/+', { qos: 1 }, (err) => {
        if (err) {
          console.error('❌ Failed to subscribe to session topics:', err);
          return reject(err);
        }
        console.log('✅ Subscribed to session topics');
        testsPassed++;
      });

      // Test 2: Subscribe to listing topics
      console.log('\n📡 Test 2: Subscribing to listing topics...');
      client.subscribe('gomama/listings/+/+', { qos: 1 }, (err) => {
        if (err) {
          console.error('❌ Failed to subscribe to listing topics:', err);
          return reject(err);
        }
        console.log('✅ Subscribed to listing topics');
        testsPassed++;
      });

      // Test 3: Subscribe to user topics
      console.log('\n📡 Test 3: Subscribing to user topics...');
      client.subscribe('gomama/users/+/+', { qos: 1 }, (err) => {
        if (err) {
          console.error('❌ Failed to subscribe to user topics:', err);
          return reject(err);
        }
        console.log('✅ Subscribed to user topics');
        testsPassed++;
      });

      // Wait a bit for subscriptions to be established
      setTimeout(() => {
        // Test 4: Publish session created event
        console.log('\n📤 Test 4: Publishing session created event...');
        const sessionCreatedTopic = `gomama/sessions/created/${TEST_SESSION_DATA.id}`;
        const sessionCreatedMessage = createMqttMessage('session_created', TEST_SESSION_DATA);
        
        client.publish(sessionCreatedTopic, JSON.stringify(sessionCreatedMessage), { qos: 1 }, (err) => {
          if (err) {
            console.error('❌ Failed to publish session created:', err);
            return reject(err);
          }
          console.log('✅ Published session created event');
          testsPassed++;
        });

        // Test 5: Publish listing status update
        console.log('\n📤 Test 5: Publishing listing status update...');
        const listingStatusTopic = `gomama/listings/status/${TEST_LISTING_DATA.listingId}`;
        const listingStatusMessage = createMqttMessage('status_update', TEST_LISTING_DATA);
        
        client.publish(listingStatusTopic, JSON.stringify(listingStatusMessage), { qos: 1, retain: true }, (err) => {
          if (err) {
            console.error('❌ Failed to publish listing status:', err);
            return reject(err);
          }
          console.log('✅ Published listing status update');
          testsPassed++;
        });

        // Test 6: Publish user notification
        console.log('\n📤 Test 6: Publishing user notification...');
        const userNotificationTopic = `gomama/users/notifications/${TEST_SESSION_DATA.userId}`;
        const userNotificationMessage = createMqttMessage('user_notification', {
          userId: TEST_SESSION_DATA.userId,
          title: 'Test Notification',
          message: 'This is a test notification'
        });
        
        client.publish(userNotificationTopic, JSON.stringify(userNotificationMessage), { qos: 1 }, (err) => {
          if (err) {
            console.error('❌ Failed to publish user notification:', err);
            return reject(err);
          }
          console.log('✅ Published user notification');
          testsPassed++;
        });

        // Wait for messages to be processed
        setTimeout(() => {
          if (testsPassed === totalTests) {
            console.log('\n🎉 All MQTT tests passed!');
            console.log(`✅ ${testsPassed}/${totalTests} tests successful`);
            client.end();
            resolve();
          } else {
            console.log(`\n⚠️  Some tests failed: ${testsPassed}/${totalTests} successful`);
            client.end();
            reject(new Error('Not all tests passed'));
          }
        }, 2000);
      }, 1000);
    });

    client.on('message', (topic, payload) => {
      try {
        const message = JSON.parse(payload.toString());
        console.log(`📨 Received message on topic: ${topic}`);
        console.log(`   Type: ${message.type}`);
        console.log(`   Source: ${message.source}`);
        console.log(`   Timestamp: ${new Date(message.timestamp).toISOString()}`);
        console.log(`   Data: ${JSON.stringify(message.data, null, 2)}`);
      } catch (error) {
        console.error('❌ Failed to parse received message:', error);
      }
    });

    client.on('error', (error) => {
      console.error('❌ MQTT connection error:', error);
      reject(error);
    });

    client.on('close', () => {
      console.log('🔌 MQTT connection closed');
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      console.log('\n⏰ Test timeout reached');
      client.end();
      reject(new Error('Test timeout'));
    }, 30000);
  });
}

// Run the test
if (require.main === module) {
  testMqttIntegration()
    .then(() => {
      console.log('\n✅ MQTT integration test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ MQTT integration test failed:', error);
      process.exit(1);
    });
}

module.exports = { testMqttIntegration };
