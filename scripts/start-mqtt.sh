#!/bin/bash

# GoMama MQTT Infrastructure Startup Script
# This script starts the MQTT infrastructure including EMQX broker, Redis, and related services

set -e

echo "🚀 Starting GoMama MQTT Infrastructure..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.mqtt.yml down --remove-orphans

# Pull latest images
echo "📥 Pulling latest Docker images..."
docker-compose -f docker-compose.mqtt.yml pull

# Start the MQTT infrastructure
echo "🔄 Starting MQTT infrastructure..."
docker-compose -f docker-compose.mqtt.yml up -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

# Check EMQX
if curl -f http://localhost:18083 > /dev/null 2>&1; then
    echo "✅ EMQX MQTT Broker is running (Dashboard: http://localhost:18083)"
    echo "   Username: admin"
    echo "   Password: gomama2024!"
else
    echo "⚠️  EMQX Dashboard may still be starting up..."
fi

# Check Redis
if docker-compose -f docker-compose.mqtt.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis is running"
else
    echo "❌ Redis is not responding"
fi

# Show running containers
echo ""
echo "📋 Running containers:"
docker-compose -f docker-compose.mqtt.yml ps

echo ""
echo "🎉 MQTT Infrastructure started successfully!"
echo ""
echo "📡 MQTT Broker Details:"
echo "   Host: localhost"
echo "   Port: 1883 (MQTT)"
echo "   WebSocket: 8083"
echo "   Dashboard: http://localhost:18083"
echo ""
echo "🔧 Next Steps:"
echo "1. Configure EMQX authentication at http://localhost:18083"
echo "2. Create users for AdonisJS and Flutter clients"
echo "3. Set up topic-based permissions"
echo "4. Test MQTT connection with: mqtt pub -h localhost -p 1883 -t 'test/topic' -m 'Hello MQTT!'"
echo ""
echo "📖 For more details, see MQTT_MIGRATION_GUIDE.md"
