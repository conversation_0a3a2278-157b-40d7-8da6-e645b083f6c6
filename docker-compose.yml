networks:
  gomama:
    external: true

volumes:
  gomama_data:
    name: gomama_data

services:
  # connect to mysql or postgres
  # via aws rds

  gomama_core:
    container_name: gomama_core
    image: gaincue/gomama-core:latest
    # build:
    #   context: .
    #   target: dependencies # dev env
    restart: always
    env_file:
      - .env.production.local
    ports:
      - 3333:3333
    networks:
      - gomama
    volumes:
      - gomama_data:/home/<USER>/app/data
    # extra_hosts:
    #   - "host.docker.internal:host-gateway"
    # volumes:
    #   - ./:/home/<USER>/app
    #   - /home/<USER>/app/node_modules
    # command: dumb-init node --inspect=0.0.0.0 ace serve --watch
    # for zero-downtime redeploy
    # deploy:
    #   replicas: 2  # Run two instances of your service
    #   update_config:
    #     parallelism: 1  # Update one instance at a time
    #     delay: 10s  # Delay between updates
    #     failure_action: rollback  # In case of failure, roll back

  gomama_migrate:
    container_name: gomama_migrate
    image: gaincue/gomama-core:latest
    # build:
    #   context: .
    #   target: dependencies # dev env
    profiles: ['database']
    env_file:
      - .env.production.local
    networks:
      - gomama
    # volumes:
    #   - ./:/home/<USER>/app
    #   - /home/<USER>/app/node_modules
    command: sh -c "node ace migration:run --force && node ace db:seed"
    depends_on:
      - gomama_core

  # gomama_seed:
  #   container_name: gomama_seed
  #   image: gaincue/gomama-core:latest
  #   # build:
  #   #   context: .
  #   #   target: dependencies # dev env
  #   profiles: [ "database" ]
  #   env_file:
  #     - .env.production.local
  #   networks:
  #     - gomama
  #   # volumes:
  #   #   - ./:/home/<USER>/app
  #   #   - /home/<USER>/app/node_modules
  #   command: sh -c "node ace db:seed"
  #   depends_on:
  #     - gomama_migrate
