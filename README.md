# `gomama_adonis` - Core Backend API

## Overview

This repository contains the core backend API for the Gomama 2.0 platform. It is built with **AdonisJS (TypeScript)** and serves as the central authority for business logic, user authentication, and database management.

## Key Responsibilities

- **Exposes a RESTful API:** Consumed by the `gomama_admin` panel and the `gomama_flutter_v2` mobile application.
- **Handles Core Business Logic:** Manages user data, orders, payments, and other essential platform functions.
- **Database Management:** Interacts with the primary database to store and retrieve application data.
- **Real-time Integration:** Publishes messages to Redis channels for events that need to be pushed to clients via `gomama_realtime`. It also subscribes to Redis channels to receive updates from other services.

## Tech Stack

- **Framework:** AdonisJS (TypeScript)
- **Database:** MariaDB (or as configured)
- **Real-time Messaging:** Redis (Pub/Sub)

## Authentication

- **Mobile App API (`/api/v1`):** Uses token-based authentication (`api` guard).
- **Admin Panel API (`/api/v1/admin`):** Uses session-based authentication (`web` guard).

## Getting Started

1.  **Install Dependencies:**
    ```bash
    pnpm install
    ```
2.  **Setup Environment Variables:**
    - Copy `.env.example` to `.env`.
    - Fill in the required environment variables, including database credentials and application keys.
3.  **Run Migrations:**
    ```bash
    node ace migration:run
    ```
4.  **Start the Development Server:**
    ```bash
    npm run dev
    ```

The API will be available at `http://localhost:3333` by default.
