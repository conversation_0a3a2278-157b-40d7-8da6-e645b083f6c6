version: '3.8'

networks:
  gomama:
    external: true

services:
  # connect to mysql or postgres
  # via aws rds

  gomama_core:
    container_name: gomama_core
    image: ghcr.io/gaincue/gomama-core:latest
    restart: always
    env_file:
      - .env.production.local
    ports:
      - 3333:3333
    networks:
      - gomama
    # for zero-downtime redeploy
    # deploy:
    #   replicas: 2  # Run two instances of your service
    #   update_config:
    #     parallelism: 1  # Update one instance at a time
    #     delay: 10s  # Delay between updates
    #     failure_action: rollback  # In case of failure, roll back

  gomama_migration:
    container_name: gomama_migration
    image: ghcr.io/gaincue/gomama-core:latest
    profiles: ['database']
    env_file:
      - .env.production.local
    networks:
      - gomama
    command: sh -c "node ace migration:run --force && node ace db:seed"
    depends_on:
      - gomama_core
