# AdonisJS MQTT Integration Guide

## Overview

This guide shows how to integrate MQTT publishing into your AdonisJS backend to replace Redis pub/sub for real-time communication.

## 📦 Installation

```bash
# Install MQTT client
npm install mqtt @types/mqtt

# Install additional dependencies if needed
npm install @adonisjs/redis @adonisjs/lucid
```

## 🔧 MQTT Service Implementation

### 1. Create MQTT Service

```typescript
// app/Services/MqttService.ts
import mqtt, { MqttClient, IClientOptions } from 'mqtt'
import Logger from '@ioc:Adonis/Core/Logger'
import Env from '@ioc:Adonis/Core/Env'

export interface MQTTMessage {
  timestamp: number
  source: 'adonisjs'
  type: string
  data: any
  messageId?: string
}

export class MqttService {
  private client: MqttClient | null = null
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 10

  constructor() {
    this.setupClient()
  }

  private setupClient() {
    const brokerUrl = Env.get('MQTT_BROKER_URL', 'mqtt://localhost:1883')
    const options: IClientOptions = {
      clientId: Env.get('MQTT_CLIENT_ID', `adonisjs_${Date.now()}`),
      username: Env.get('MQTT_USERNAME'),
      password: Env.get('MQTT_PASSWORD'),
      clean: false, // Persistent session
      keepalive: 60,
      reconnectPeriod: 5000,
      connectTimeout: 30000,
      will: {
        topic: 'gomama/system/health',
        payload: JSON.stringify({
          timestamp: Date.now(),
          source: 'adonisjs',
          type: 'disconnect',
          data: { reason: 'unexpected' }
        }),
        qos: 1,
        retain: false
      }
    }

    this.client = mqtt.connect(brokerUrl, options)
    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    if (!this.client) return

    this.client.on('connect', () => {
      Logger.info('✅ MQTT Client connected to broker')
      this.isConnected = true
      this.reconnectAttempts = 0
      
      // Publish connection status
      this.publishSystemHealth('connected')
    })

    this.client.on('disconnect', () => {
      Logger.warn('❌ MQTT Client disconnected from broker')
      this.isConnected = false
    })

    this.client.on('reconnect', () => {
      this.reconnectAttempts++
      Logger.info(`🔄 MQTT Client reconnecting... (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        Logger.error('❌ Max reconnection attempts reached. Stopping reconnection.')
        this.client?.end(true)
      }
    })

    this.client.on('error', (error) => {
      Logger.error('❌ MQTT Client error:', error)
    })
  }

  public async publish(topic: string, message: MQTTMessage, options: { qos?: 0 | 1 | 2, retain?: boolean } = {}): Promise<boolean> {
    if (!this.client || !this.isConnected) {
      Logger.error('❌ Cannot publish: MQTT client not connected')
      return false
    }

    const payload = JSON.stringify(message)
    const publishOptions = {
      qos: options.qos || 1,
      retain: options.retain || false
    }

    return new Promise<boolean>((resolve) => {
      this.client!.publish(topic, payload, publishOptions, (err) => {
        if (err) {
          Logger.error(`❌ Failed to publish to ${topic}:`, err)
          resolve(false)
        } else {
          Logger.info(`✅ Published to ${topic}`)
          resolve(true)
        }
      })
    })
  }

  // Convenience methods for common publishing patterns
  public async publishSessionCreated(sessionId: string, sessionData: any): Promise<boolean> {
    return this.publish(`gomama/sessions/created/${sessionId}`, {
      timestamp: Date.now(),
      source: 'adonisjs',
      type: 'session_created',
      data: sessionData
    }, { qos: 1, retain: false })
  }

  public async publishSessionUpdated(sessionId: string, sessionData: any): Promise<boolean> {
    return this.publish(`gomama/sessions/updated/${sessionId}`, {
      timestamp: Date.now(),
      source: 'adonisjs',
      type: 'session_updated',
      data: sessionData
    }, { qos: 1, retain: true })
  }

  public async publishSessionEnded(sessionId: string, sessionData: any): Promise<boolean> {
    return this.publish(`gomama/sessions/ended/${sessionId}`, {
      timestamp: Date.now(),
      source: 'adonisjs',
      type: 'session_ended',
      data: sessionData
    }, { qos: 1, retain: false })
  }

  public async publishListingStatus(listingId: string, status: string, additionalData: any = {}): Promise<boolean> {
    return this.publish(`gomama/listings/status/${listingId}`, {
      timestamp: Date.now(),
      source: 'adonisjs',
      type: 'listing_status_update',
      data: { listingId, status, ...additionalData }
    }, { qos: 1, retain: true })
  }

  public async publishUserNotification(userId: string, notification: any): Promise<boolean> {
    return this.publish(`gomama/users/notifications/${userId}`, {
      timestamp: Date.now(),
      source: 'adonisjs',
      type: 'user_notification',
      data: notification
    }, { qos: 1, retain: false })
  }

  private async publishSystemHealth(status: 'connected' | 'disconnected' | 'error', data?: any): Promise<boolean> {
    return this.publish('gomama/system/health', {
      timestamp: Date.now(),
      source: 'adonisjs',
      type: 'health_status',
      data: { status, clientId: Env.get('MQTT_CLIENT_ID'), ...data }
    }, { qos: 0, retain: false })
  }

  public isClientConnected(): boolean {
    return this.isConnected
  }

  public async disconnect(): Promise<void> {
    if (this.client) {
      await this.publishSystemHealth('disconnected', { reason: 'graceful_shutdown' })
      this.client.end(true)
      this.isConnected = false
    }
  }
}

// Export singleton instance
const mqttService = new MqttService()
export default mqttService
```

### 2. Register as IoC Container Service

```typescript
// providers/AppProvider.ts
import { ApplicationContract } from '@ioc:Adonis/Core/Application'
import { MqttService } from 'App/Services/MqttService'

export default class AppProvider {
  constructor(protected app: ApplicationContract) {}

  public register() {
    // Register MQTT service
    this.app.container.singleton('App/Services/MqttService', () => {
      return new MqttService()
    })
  }

  public async boot() {
    // Boot services
  }

  public async ready() {
    // App is ready
  }

  public async shutdown() {
    // Cleanup on shutdown
    const mqttService = this.app.container.use('App/Services/MqttService')
    await mqttService.disconnect()
  }
}
```

### 3. Environment Configuration

```env
# .env
MQTT_BROKER_URL=mqtt://localhost:1883
MQTT_CLIENT_ID=adonisjs_server
MQTT_USERNAME=adonisjs_client
MQTT_PASSWORD=your_jwt_token_here
```

## 🎯 Usage in Controllers

### 1. Session Controller

```typescript
// app/Controllers/Http/SessionsController.ts
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Session from 'App/Models/Session'
import MqttService from 'App/Services/MqttService'

export default class SessionsController {
  public async store({ request, response }: HttpContextContract) {
    const sessionData = request.only(['listingId', 'userId', 'startTime'])
    
    // Create session in database
    const session = await Session.create(sessionData)
    
    // Publish session created event via MQTT
    await MqttService.publishSessionCreated(session.id, {
      sessionId: session.id,
      listingId: session.listingId,
      userId: session.userId,
      startTime: session.startTime,
      status: 'created'
    })
    
    return response.created(session)
  }

  public async update({ params, request, response }: HttpContextContract) {
    const session = await Session.findOrFail(params.id)
    const updateData = request.only(['status', 'endTime'])
    
    // Update session in database
    session.merge(updateData)
    await session.save()
    
    // Publish session updated event via MQTT
    await MqttService.publishSessionUpdated(session.id, {
      sessionId: session.id,
      listingId: session.listingId,
      userId: session.userId,
      status: session.status,
      ...updateData
    })
    
    return response.ok(session)
  }

  public async destroy({ params, response }: HttpContextContract) {
    const session = await Session.findOrFail(params.id)
    
    // Publish session ended event before deletion
    await MqttService.publishSessionEnded(session.id, {
      sessionId: session.id,
      listingId: session.listingId,
      userId: session.userId,
      endTime: new Date(),
      reason: 'deleted'
    })
    
    await session.delete()
    
    return response.noContent()
  }
}
```

### 2. Listing Controller

```typescript
// app/Controllers/Http/ListingsController.ts
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Listing from 'App/Models/Listing'
import MqttService from 'App/Services/MqttService'

export default class ListingsController {
  public async updateStatus({ params, request, response }: HttpContextContract) {
    const listing = await Listing.findOrFail(params.id)
    const { status } = request.only(['status'])
    
    const previousStatus = listing.status
    listing.status = status
    await listing.save()
    
    // Publish status change via MQTT
    await MqttService.publishListingStatus(listing.id, status, {
      previousStatus,
      updatedAt: listing.updatedAt,
      updatedBy: 'admin' // or get from auth context
    })
    
    return response.ok(listing)
  }
}
```

### 3. Notification Controller

```typescript
// app/Controllers/Http/NotificationsController.ts
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import MqttService from 'App/Services/MqttService'

export default class NotificationsController {
  public async sendToUser({ request, response }: HttpContextContract) {
    const { userId, title, body, data } = request.all()
    
    // Send notification via MQTT
    const success = await MqttService.publishUserNotification(userId, {
      title,
      body,
      data,
      timestamp: Date.now()
    })
    
    if (success) {
      return response.ok({ message: 'Notification sent successfully' })
    } else {
      return response.badRequest({ message: 'Failed to send notification' })
    }
  }

  public async broadcast({ request, response }: HttpContextContract) {
    const { userIds, title, body, data } = request.all()
    
    // Send to multiple users
    const results = await Promise.allSettled(
      userIds.map(userId => 
        MqttService.publishUserNotification(userId, {
          title,
          body,
          data,
          timestamp: Date.now()
        })
      )
    )
    
    const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length
    
    return response.ok({
      message: `Notification sent to ${successCount}/${userIds.length} users`
    })
  }
}
```

## 🔄 Model Hooks

Use model hooks to automatically publish MQTT messages on data changes:

```typescript
// app/Models/Session.ts
import { BaseModel, column, beforeSave, afterCreate, afterUpdate, afterDelete } from '@ioc:Adonis/Lucid/Orm'
import MqttService from 'App/Services/MqttService'

export default class Session extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public listingId: number

  @column()
  public userId: number

  @column()
  public status: string

  @afterCreate()
  public static async publishCreated(session: Session) {
    await MqttService.publishSessionCreated(session.id.toString(), {
      sessionId: session.id,
      listingId: session.listingId,
      userId: session.userId,
      status: session.status,
      createdAt: session.createdAt
    })
  }

  @afterUpdate()
  public static async publishUpdated(session: Session) {
    await MqttService.publishSessionUpdated(session.id.toString(), {
      sessionId: session.id,
      listingId: session.listingId,
      userId: session.userId,
      status: session.status,
      updatedAt: session.updatedAt
    })
  }

  @afterDelete()
  public static async publishDeleted(session: Session) {
    await MqttService.publishSessionEnded(session.id.toString(), {
      sessionId: session.id,
      listingId: session.listingId,
      userId: session.userId,
      deletedAt: new Date(),
      reason: 'deleted'
    })
  }
}
```

## 🔐 MQTT Credentials Endpoint

Create an endpoint to provide MQTT credentials to Flutter clients:

```typescript
// app/Controllers/Http/MqttController.ts
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { generateFlutterCredentials } from 'path/to/mqtt-auth-service'

export default class MqttController {
  public async getCredentials({ request, response, auth }: HttpContextContract) {
    // Authenticate user
    const user = await auth.authenticate()
    
    const { deviceId } = request.only(['deviceId'])
    
    if (!deviceId) {
      return response.badRequest({ message: 'Device ID is required' })
    }
    
    // Generate MQTT credentials
    const credentials = generateFlutterCredentials(user.id.toString(), deviceId)
    
    return response.ok({
      username: credentials.username,
      password: credentials.password,
      token: credentials.token,
      brokerUrl: 'mqtt://your-broker.com:1883',
      brokerUrlSSL: 'mqtts://your-broker.com:8883'
    })
  }
}
```

## 📝 Routes

```typescript
// start/routes.ts
import Route from '@ioc:Adonis/Core/Route'

// MQTT credentials endpoint
Route.post('/api/mqtt/credentials', 'MqttController.getCredentials').middleware('auth')

// Session routes with MQTT publishing
Route.resource('sessions', 'SessionsController').apiOnly()

// Listing routes with MQTT publishing
Route.put('/listings/:id/status', 'ListingsController.updateStatus')

// Notification routes
Route.post('/notifications/user', 'NotificationsController.sendToUser')
Route.post('/notifications/broadcast', 'NotificationsController.broadcast')
```

## 🧪 Testing

Test your MQTT integration:

```bash
# Subscribe to all topics
mqtt sub -h localhost -p 1883 -t 'gomama/+/+/+'

# Test session creation
curl -X POST http://localhost:3333/sessions \
  -H "Content-Type: application/json" \
  -d '{"listingId": 1, "userId": 123, "startTime": "2024-01-01T10:00:00Z"}'

# Test listing status update
curl -X PUT http://localhost:3333/listings/1/status \
  -H "Content-Type: application/json" \
  -d '{"status": "available"}'
```

## 🚀 Production Considerations

1. **Connection Pooling**: Consider connection pooling for high-traffic applications
2. **Error Handling**: Implement comprehensive error handling and retry logic
3. **Monitoring**: Monitor MQTT connection health and message delivery
4. **Security**: Use SSL/TLS and proper authentication in production
5. **Performance**: Consider message batching for high-frequency updates
