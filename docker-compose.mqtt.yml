version: '3.8'

services:
  # EMQX MQTT Broker
  emqx:
    image: emqx/emqx:5.4.0
    container_name: gomama_emqx
    restart: always
    ports:
      - "1883:1883"     # MQTT port
      - "8083:8083"     # MQTT over WebSocket
      - "18083:18083"   # Dashboard
    environment:
      - EMQX_NAME=emqx
      - EMQX_HOST=127.0.0.1
      - EMQX_DASHBOARD__DEFAULT_USERNAME=admin
      - EMQX_DASHBOARD__DEFAULT_PASSWORD=gomama2024!
      - EMQX_AUTH__MNESIA__PASSWORD_HASH=sha256
    volumes:
      - emqx_data:/opt/emqx/data
      - emqx_log:/opt/emqx/log
    networks:
      - gomama_mqtt
    healthcheck:
      test: ["CMD", "/opt/emqx/bin/emqx", "ctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for data storage
  redis:
    image: redis:7.2-alpine
    container_name: gomama_redis_mqtt
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - gomama_mqtt
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # GoMama Realtime Service (updated for MQTT)
  gomama_realtime:
    image: gaincue/gomama-realtime:mqtt-latest
    container_name: gomama_realtime_mqtt
    restart: always
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - MQTT_BROKER_HOST=emqx
      - MQTT_BROKER_PORT=1883
      - MQTT_CLIENT_ID=gomama_realtime_server
      - MQTT_USERNAME=realtime_client
      - MQTT_PASSWORD=realtime_pass
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      emqx:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - gomama_mqtt
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # GoMama Core (AdonisJS Backend)
  gomama_core:
    image: gaincue/gomama-core:mqtt-latest
    container_name: gomama_core_mqtt
    restart: always
    ports:
      - "3333:3333"
    environment:
      - NODE_ENV=production
      - MQTT_BROKER_HOST=emqx
      - MQTT_BROKER_PORT=1883
      - MQTT_CLIENT_ID=gomama_adonisjs
      - MQTT_USERNAME=adonisjs_client
      - MQTT_PASSWORD=adonisjs_pass
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    env_file:
      - .env.production.local
    depends_on:
      emqx:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - gomama_mqtt
    volumes:
      - gomama_data:/home/<USER>/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  gomama_mqtt:
    driver: bridge

volumes:
  emqx_data:
    driver: local
  emqx_log:
    driver: local
  redis_data:
    driver: local
  gomama_data:
    driver: local
