/**
 * Config source: https://git.io/JOdi5
 *
 * Feel free to let us know via PR, if you find something broken in this config
 * file.
 */

import Env from '@ioc:Adonis/Core/Env'
import { AllyConfig } from '@ioc:Adonis/Addons/Ally'

/*
|--------------------------------------------------------------------------
| Ally Config
|--------------------------------------------------------------------------
|
| The `AllyConfig` relies on the `SocialProviders` interface which is
| defined inside `contracts/ally.ts` file.
|
*/
const allyConfig: AllyConfig = {
  /*
	|--------------------------------------------------------------------------
	| Google driver
	|--------------------------------------------------------------------------
	*/
  google: {
    driver: 'google',
    clientId: Env.get('GOOGLE_CLIENT_ID'),
    clientSecret: Env.get('GOOGLE_CLIENT_SECRET'),
    callbackUrl: Env.get('GOOGLE_CALLBACK_URL'),
  },
  /*
  |--------------------------------------------------------------------------
  | Facebook driver
  |--------------------------------------------------------------------------
  */
  facebook: {
    driver: 'facebook',
    clientId: Env.get('FACEBOOK_CLIENT_ID'),
    clientSecret: Env.get('FACEBOOK_CLIENT_SECRET'),
    callbackUrl: Env.get('FACEBOOK_CALLBACK_URL'),
  },
  /*
  |--------------------------------------------------------------------------
  | Apple driver
  |--------------------------------------------------------------------------
  */
  apple: {
    driver: 'apple',
    appId: Env.get('APPLE_APP_ID'),
    teamId: Env.get('APPLE_TEAM_ID'),
    clientId: Env.get('APPLE_CLIENT_ID'),
    clientSecret: Env.get('APPLE_CLIENT_SECRET'),
    callbackUrl: Env.get('APPLE_CALLBACK_URL'),
    scopes: ['email', 'fullName'],
  },
}

export default allyConfig
