<% require "dotenv"; Dotenv.load(".env") %>

# Name of your application. Used to uniquely configure containers.
service: gomama-core

# Name of the container image.
image: gaincue/gomama-core

# Deploy to these servers.
servers:
  web:
    - ***********
  # job:
  #   hosts:
  #     - ***********
  #   cmd: bin/jobs

# Enable SSL auto certification via Let's Encrypt (and allow for multiple apps on one server).
# If using something like Cloudflare, it is recommended to set encryption mode
# in Cloudflare's SSL/TLS setting to "Full" to enable end-to-end encryption.
proxy:
  ssl: true
  host: api.gomama.com.sg
  # kamal-proxy connects to your container over port 80, use `app_port` to specify a different port.
  app_port: 3333
  healthcheck:
    interval: 3
    path: /health
    timeout: 3

# Credentials for your image host.
registry:
  # Specify the registry server, if you're not using Docker Hub
  # server: registry.digitalocean.com / ghcr.io / ...
  username:
    - KAMAL_REGISTRY_USERNAME

  # Always use an access token rather than real password when possible.
  password:
    - KAM<PERSON>_REGISTRY_PASSWORD

# Configure builder setup.
builder:
  arch: amd64

# Inject ENV variables into containers (secrets come from .kamal/secrets).
env:
  clear:
    NODE_ENV: production
  secret:
    - PORT
    - HOST
    - APP_KEY
    - PUBLIC_DRIVE_DISK
    - PRIVATE_DRIVE_DISK
    - DB_CONNECTION
    - MYSQL_HOST
    - MYSQL_PORT
    - MYSQL_USER
    - MYSQL_PASSWORD
    - MYSQL_DB_NAME

    # misc
    - USER_VERIFY_IMAGE_URL
    - USER_PROFILE_IMAGE_URL
    - LISTING_IMAGE_URL
    - ACTIVITY_IMAGE_URL
    - AMENITY_IMAGE_URL
    - REGION_IMAGE_URL
    - LISTING_FLAG_IMAGE_URL
    - NOTIFICATION_MESSAGE_IMAGE_URL

    # igloohome
    - IGLOOHOME_API_KEY

    # auth
    - SESSION_DRIVER

    # singpass
    - CIPHER_SECRET
    - CIPHER_IV

    - SINGPASS_CLIENT_ID
    - SINGPASS_CLIENT_SECRET
    - SINGPASS_SCOPE
    - SINGPASS_GRANT_TYPE
    - SINGPASS_MYINFO_URL
    - SINGPASS_PURPOSE
    - SINGPASS_REDIRECT_URI
    - SINGPASS_APP_DESTINATION
    - SINGPASS_PKI_COMPANY_PRIVATE_KEY
    - SINGPASS_PKI_MYINFO_PUBLIC_CERT
    - CACHE_VIEWS

    # resend
    - RESEND_OFFICIAL_EMAIL
    - RESEND_API_KEY

    - BASE_URL
    - BASE_URL_ADMIN

    - LOCKABLE_ROOM_AMENITY_ID
    - BADWORD_TXT_FILE

    # s3
    - S3_KEY
    - S3_SECRET
    - S3_REGION
    - S3_BUCKET_PUBLIC
    - S3_BUCKET
    - S3_ENDPOINT
    - S3_PUBLIC_ENDPOINT
    - S3_PRIVATE_ENDPOINT

    # google login
    - GOOGLE_CLIENT_ID
    - GOOGLE_CLIENT_SECRET
    - GOOGLE_CALLBACK_URL

    # facebook login
    - FACEBOOK_CLIENT_ID
    - FACEBOOK_CLIENT_SECRET
    - FACEBOOK_CALLBACK_URL

    # apple login
    - APPLE_APP_ID
    - APPLE_TEAM_ID
    - APPLE_CLIENT_ID
    - APPLE_CLIENT_SECRET
    - APPLE_CALLBACK_URL

    # aws sdk v3 for sns
    - AWS_ACCESS_KEY_ID
    - AWS_SECRET_ACCESS_KEY

    # sns
    - SNS_REGION

    # redis
    - REDIS_CONNECTION
    - REDIS_HOST
    - REDIS_PORT
    - REDIS_PASSWORD

    # one way sms
    - ONEWAYSMS_USERNAME
    - ONEWAYSMS_PASSWORD
    - ONEWAYSMS_SENDER_ID

# Aliases are triggered with "bin/kamal <alias>". You can overwrite arguments on invocation:
# "bin/kamal logs -r job" will tail logs from the first server in the job section.
#
# aliases:
#   shell: app exec --interactive --reuse "bash"

# Use a different ssh user than root
ssh:
  # log_level: debug
  user: ubuntu
  keys: ['/Users/<USER>/.ssh/gomama.pem']
# Use a persistent storage volume.
#
# volumes:
#   - "app_storage:/app/storage"

# Bridge fingerprinted assets, like JS and CSS, between versions to avoid
# hitting 404 on in-flight requests. Combines all files from new and old
# version inside the asset_path.
#
# asset_path: /app/public/assets

# Configure rolling deploys by setting a wait time between batches of restarts.
#
# boot:
#   limit: 10 # Can also specify as a percentage of total hosts, such as "25%"
#   wait: 2

# Use accessory services (secrets come from .kamal/secrets).
#
# accessories:
#   db:
#     image: mysql:8.0
#     host: ***********
#     port: 3306
#     env:
#       clear:
#         MYSQL_ROOT_HOST: '%'
#       secret:
#         - MYSQL_ROOT_PASSWORD
#     files:
#       - config/mysql/production.cnf:/etc/mysql/my.cnf
#       - db/production.sql:/docker-entrypoint-initdb.d/setup.sql
#     directories:
#       - data:/var/lib/mysql
#   redis:
#     image: redis:7.0
#     host: ***********
#     port: 6379
#     directories:
#       - data:/data
