{"extends": "adonis-preset-ts/tsconfig.json", "include": ["**/*"], "exclude": ["node_modules", "build"], "compilerOptions": {"jsx": "react", "outDir": "build", "rootDir": "./", "sourceMap": true, "esModuleInterop": true, "paths": {"App/*": ["./app/*"], "Config/*": ["./config/*"], "Contracts/*": ["./contracts/*"], "Database/*": ["./database/*"]}, "types": ["@adonisjs/core", "@adonisjs/repl", "@japa/preset-adonis/build/adonis-typings", "@adonisjs/lucid", "@adonisjs/auth", "adonis-lucid-filter", "@adonisjs/lucid-slugify", "@adonisjs/session", "@adonisjs/view", "@adonisjs/drive-s3", "@adonisjs/ally", "@bitkidd/adonis-ally-apple", "@adonisjs/redis", "adonis5-scheduler"]}}