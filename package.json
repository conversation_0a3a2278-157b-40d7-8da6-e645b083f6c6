{"name": "gomama-core_adonis", "version": "1.0.0", "private": true, "scripts": {"dev": "node ace serve --watch", "build": "node ace build --production --ignore-ts-errors", "start": "node server.js", "lint": "eslint . --ext=.ts", "format": "npx prettier --write ."}, "eslintConfig": {"extends": ["plugin:adonis/typescriptApp", "prettier"], "plugins": ["prettier"], "rules": {"prettier/prettier": ["error"], "eqeqeq": "off"}}, "eslintIgnore": ["build"], "prettier": {"trailingComma": "es5", "semi": false, "singleQuote": true, "useTabs": false, "quoteProps": "consistent", "bracketSpacing": true, "arrowParens": "always", "printWidth": 100}, "devDependencies": {"@adonisjs/assembler": "^5.9.6", "@japa/preset-adonis": "^1.2.0", "@japa/runner": "^2.5.1", "@types/luxon": "^3.4.2", "@types/ngeohash": "^0.6.8", "@types/proxy-addr": "^2.0.3", "@types/source-map-support": "^0.5.10", "adonis-preset-ts": "^2.1.0", "pino-pretty": "^10.3.1", "typescript": "~4.6.4", "youch": "^3.3.3", "youch-terminal": "^2.2.3"}, "dependencies": {"@adonisjs/ally": "4.1.5", "@adonisjs/auth": "^8.2.3", "@adonisjs/core": "^5.8.0", "@adonisjs/drive-s3": "^1.3.3", "@adonisjs/lucid": "^18.4.2", "@adonisjs/lucid-slugify": "^2.2.1", "@adonisjs/redis": "7.3.4", "@adonisjs/repl": "^3.1.11", "@adonisjs/session": "6.4.0", "@adonisjs/view": "^6.2.0", "@aws-sdk/client-ses": "^3.668.0", "@aws-sdk/client-sns": "^3.650.0", "@aws-sdk/credential-providers": "^3.650.0", "@bitkidd/adonis-ally-apple": "^1.1.0", "@react-email/components": "0.0.22", "@types/google-libphonenumber": "^7.4.30", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/natural": "^6.0.1", "@types/nodemailer": "^6.4.17", "adonis-lucid-filter": "^4.1.1", "adonis5-scheduler": "^2.1.1", "axios": "^1.6.7", "bad-words": "^3.0.4", "bignumber.js": "^9.1.2", "bullmq": "^5.56.0", "csv-writer": "^1.6.0", "firebase-admin": "^13.1.0", "geolocation-utils": "^1.2.5", "google-libphonenumber": "^3.2.38", "i18n-iso-countries": "^7.12.0", "jose": "^5.2.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "luxon": "^3.4.4", "mqtt": "^5.13.2", "mysql2": "^3.9.1", "natural": "^8.1.0", "ngeohash": "^0.6.3", "node-jose": "^2.2.0", "nodemailer": "^7.0.3", "proxy-addr": "^2.0.7", "radash": "^12.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.1.14", "resend": "^3.5.0", "source-map-support": "^0.5.21", "twilio": "^5.3.7", "uuid": "^9.0.1"}}