PORT=3333
HOST=0.0.0.0
NODE_ENV=production
APP_KEY=OVKa07vbl8Lxt3-ZpN+nX4Uwu8q5Uo
PUBLIC_DRIVE_DISK=s3_public
PRIVATE_DRIVE_DISK=s3_private
DB_CONNECTION=mysql
MYSQL_HOST=gomama-mariadb.c5o64ke2c5xc.ap-southeast-1.rds.amazonaws.com
MYSQL_PORT=3306
MYSQL_USER=admin
MYSQL_PASSWORD=ZqsUYz2P*82U # DO NOT COMMIT
MYSQL_DB_NAME=gomama_core

# misc
USER_VERIFY_IMAGE_URL=user
USER_PROFILE_IMAGE_URL=user_profile
LISTING_IMAGE_URL=listing
ACTIVITY_IMAGE_URL=activity
AMENITY_IMAGE_URL=amenity
REGION_IMAGE_URL=region
LISTING_FLAG_IMAGE_URL=flags

# igloohome
IGLOOHOME_API_KEY=btrfVdLCnDiDRUxRBoai3p.3bDiOKmtHwcd8F5i9VjpKVDwawAj9aIa8zUEyvFl

# auth
SESSION_DRIVER=cookie

# singpass
CIPHER_SECRET=9affc7cf5792811ab4a955ff4448b83902a2a5fcac6e2a976e7e10df09bae89e
CIPHER_IV=cba66473aa20e6b61c63924c633f9045

SINGPASS_CLIENT_ID=PROD-202108084H-GOMAMA-REGN
SINGPASS_CLIENT_SECRET=Mm5KSU3itNGmraMiBZZ5zfqGraRTHGpe
SINGPASS_SCOPE=partialuinfin,name,sex,dob,mobileno,childrenbirthrecords.dob
SINGPASS_GRANT_TYPE=authorization_code
SINGPASS_MYINFO_URL=https://api.myinfo.gov.sg/com/v3
SINGPASS_PURPOSE="Accessing GO!MAMA's lactation pods in the public places."
SINGPASS_REDIRECT_URI=https://api.gomama.com.sg/api/v1/verification/callback
SINGPASS_APP_DESTINATION=gomama://com.gomama.app.prod
SINGPASS_PKI_COMPANY_PRIVATE_KEY="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
SINGPASS_PKI_MYINFO_PUBLIC_CERT="-----BEGIN CERTIFICATE-----
MIIG2zCCBMOgAwIBAgIRALOE6ptl31xMwundcOg0eFgwDQYJKoZIhvcNAQELBQAw
 aDELMAkGA1UEBhMCU0cxGDAWBgNVBAoTD05ldHJ1c3QgUHRlIEx0ZDEmMCQGA1UE
 CxMdTmV0cnVzdCBDZXJ0aWZpY2F0ZSBBdXRob3JpdHkxFzAVBgNVBAMTDk5ldHJ1
 c3QgQ0EgMi0xMB4XDTI0MDQwMjA5MDY1OFoXDTI5MTEwNjE2MDAwMFowgcgxCzAJ
 BgNVBAYTAlNHMRgwFgYDVQQKEw9OZXRydXN0IFB0ZSBMdGQxJjAkBgNVBAsTHU5l
 dHJ1c3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5MSAwHgYDVQQLExdOZXRydXN0IENB
 IDItMSAoU2VydmVyKTEPMA0GA1UECxMGTXlJbmZvMSQwIgYDVQQLExtHb3Zlbm1l
 bnQgVGVjaG5vbG9neSBBZ2VuY3kxHjAcBgNVBAMTFWNvbnNlbnQubXlpbmZvLmdv
 di5zZzCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJGTR9PsG3hFd7Wm
 7gnhfTuYVkohooZ6NR4OSTMT8CH6ZLKRwtNYbCYdfoZNsushD6b+oUXqnI1mgPBu
 XUsOZ2vwBxrFCQCbwBRkKECjB7AqyxyEgtuIT6evpY0ONW2ZQiiBKjaAIKj5M9Ho
 sjwkKyCHOBT6P2IHaiL+ygDdiBm3zFNsghzXPEhNn9AAFX8g470uj7Wkd7KVt+OY
 Bpj4zW244Vdz9pRdSZtbFXknCyX5A70NNvvQ+HFvuFbO7zZM/zcorellm52Vyhkj
 U0YfI0pcn/1PxrriWtsJ+V4osmUZ/21jSbHnUPpjI9A4z8uSPcEM/sDFJiX+WX/p
 DBVZ1PUCAwEAAaOCAh0wggIZMAsGA1UdDwQEAwIFoDAdBgNVHSUEFjAUBggrBgEF
 BQcDAQYIKwYBBQUHAwIwSAYDVR0gBEEwPzA9BggqhT4Ah2oGATAxMC8GCCsGAQUF
 BwIBFiNodHRwOi8vd3d3Lm5ldHJ1c3QubmV0L291cnByYWN0aWNlczBoBggrBgEF
 BQcBAQRcMFowIwYIKwYBBQUHMAGGF2h0dHA6Ly9vY3NwLm5ldHJ1c3QubmV0MDMG
 CCsGAQUFBzAChidodHRwOi8vYWlhLm5ldHJ1c3QubmV0L25ldHJ1c3RjYTItMS5j
 ZXIwgb4GA1UdHwSBtjCBszAtoCugKYYnaHR0cDovL2NybC5uZXRydXN0Lm5ldC9u
 ZXRydXN0Y2EyLTEuY3JsMIGBoH+gfaR7MHkxCzAJBgNVBAYTAlNHMRgwFgYDVQQK
 Ew9OZXRydXN0IFB0ZSBMdGQxJjAkBgNVBAsTHU5ldHJ1c3QgQ2VydGlmaWNhdGUg
 QXV0aG9yaXR5MRcwFQYDVQQDEw5OZXRydXN0IENBIDItMTEPMA0GA1UEAxMGQ1JM
 NDE3MCsGA1UdEAQkMCKADzIwMjQwNDAyMDkwNjU4WoEPMjAyOTExMDYxNjAwMDBa
 MB8GA1UdIwQYMBaAFBdLJkuUeQpf35rxCDfDSDeDu9f6MB0GA1UdDgQWBBQDRzN+
 kjgpXgPiL1wFL08ldjZ7WjAJBgNVHRMEAjAAMA0GCSqGSIb3DQEBCwUAA4ICAQCl
 Qq/D6wwdpRz4FM4hWrS5Of89lI4o1W2GKqi/pkIFO9CgABPJOD5eiNQiL+ffqr3b
 5itE2/FdYlvLlL2Z+MUp3dNuJ0dcnMfBSgr/gz0dbBQoLwidBlbNm0Slwj6fS/4v
 pLVpLUeW6iBv07hbSflIJ2VdKI6+URSB/V+PuJHBnWteDeB8Tes2g+KtIS6soDf9
 +qlrHlgMZV76eBxikTD7Dyhy2xZIYvJBl4ig2hkRMkIeKZ2eKwrWHoYS1EEfD8Nt
 zsKsyZDMP+3Aao/pao8U1yIeGBaOFQqSrKP/W0OKNYjsqtD6e47jD+thnNwIMKVT
 ecoCDTYYIZ8/kQ75erZgJ2nqI1VxvVCTMnUcWUjK38P1P7ZpKVD11yaUwkyKCXjc
 NgJz4wFgMewMajI/LEBQ9P1+Rokd6ss1Vs8+zmwtcaNz713fy/TV2jcGVMntrkca
 gFQibwZxLh7EAkbvMXPdtfASrkrtFGyNTWi0MCFUdnpmqysAhnyiR7T9a38bOoiy
 ZfykZOBlEINiBBRzhUSFXhkwwHTvqHl/xJTze+YRNvuX6qLWah070fjjUKMXm2Ar
 k40+ktSN+hWj9vpkQi9xcN/oaZmlU1AL2I8LGVCMsG9gnziz0KXLZIzbpuLVlKhB
 Al1699SwJoTekAr1caJlOz5VRBtC+BtgCY+sOLPG8w==
-----END CERTIFICATE-----"
CACHE_VIEWS=false

# resend
RESEND_OFFICIAL_EMAIL=<EMAIL>
RESEND_API_KEY=re_gVmF6rfS_PoduS7FpCesVGhSZrZ7EPDPK # <EMAIL>

BASE_URL=/api/v1
BASE_URL_ADMIN=/api/v1/admin

LOCKABLE_ROOM_AMENITY_ID=32902e77-d7fd-496f-9bb2-b648d2075a17
BADWORD_TXT_FILE=badwords.txt

# firebase
FIRESTORE_ADMIN_SERVICE_ACCOUNT_JSON=serviceAccountKey.json

# s3
S3_KEY=********************
S3_SECRET=pB4gmLNRUl0U1evuUiZdrKCvSwnPpIOgzP4uKfVS
S3_REGION=ap-southeast-1
S3_BUCKET_PUBLIC=gomama
S3_BUCKET=gomama.private
S3_ENDPOINT=https://s3.ap-southeast-1.amazonaws.com
S3_PUBLIC_ENDPOINT=https://gomama.s3.ap-southeast-1.amazonaws.com # public
S3_PRIVATE_ENDPOINT=https://gomama.private.s3.ap-southeast-1.amazonaws.com # private

# google login
GOOGLE_CLIENT_ID=************-2p8mq3vit7vi9s7n6v66g8ogupei6u37.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_CALLBACK_URL=https://api.gomama.com.sg/api/v1/oauth/google/callback

# facebook login
FACEBOOK_CLIENT_ID=****************
FACEBOOK_CLIENT_SECRET=********************************
FACEBOOK_CALLBACK_URL=https://api.gomama.com.sg/api/v1/oauth/facebook/callback

# apple login
APPLE_APP_ID=com.gomama.app.prod
APPLE_TEAM_ID=5FMDKW6S3D
APPLE_CLIENT_ID=P7XV23MLMX
APPLE_CLIENT_SECRET="*****************************************************************************************************************************************************************************************************************************************************************"
APPLE_CALLBACK_URL=https://api.gomama.com.sg/api/v1/oauth/apple/callback

# aws sdk v3 for sns
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=flXJuQVuE51aw5MYWYrbW+dXBzwMSensk9b88hOr

# sns
SNS_REGION=ap-southeast-1

# ses
SES_SENDER=<EMAIL>

# redis
REDIS_CONNECTION=local
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
