import { BaseCommand } from '@adonisjs/core/build/standalone'
import Session from 'App/Models/Session'
import { createObjectCsvWriter } from 'csv-writer'
import path from 'path'
import natural from 'natural'

interface ThemeSummary {
  listingName: string
  totalReviews: number
  positiveThemes: string[]
  negativeThemes: string[]
  improvementAreas: string[]
  emergingTrends: string[]
}

export default class AnalyzeReviewThemes extends BaseCommand {
  public static commandName = 'analyze:review-themes'
  public static description = 'Analyze and summarize review themes for listing improvement'

  public static settings = {
    loadApp: true,
  }

  private tokenizer = new natural.WordTokenizer()
  private stemmer = natural.PorterStemmer
  private analyzer = new natural.SentimentAnalyzer('English', natural.PorterStemmer, 'afinn')

  private readonly THEME_CATEGORIES = {
    positive: [
      'clean',
      'comfortable',
      'convenient',
      'friendly',
      'helpful',
      'nice',
      'quiet',
      'spacious',
      'well-maintained',
      'good',
    ],
    negative: [
      'dirty',
      'uncomfortable',
      'inconvenient',
      'noisy',
      'crowded',
      'broken',
      'poor',
      'bad',
      'difficult',
      'problem',
    ],
    improvement: [
      'improve',
      'better',
      'need',
      'should',
      'could',
      'suggestion',
      'recommend',
      'wish',
      'hope',
      'expect',
    ],
  }

  private extractThemes(text: string, category: 'positive' | 'negative' | 'improvement'): string[] {
    if (!text) return []

    const tokens = this.tokenizer.tokenize(text.toLowerCase())
    const stemmedTokens = tokens.map((token) => this.stemmer.stem(token))

    const themes = new Set<string>()
    this.THEME_CATEGORIES[category].forEach((theme) => {
      const stemmedTheme = this.stemmer.stem(theme)
      if (stemmedTokens.includes(stemmedTheme)) {
        themes.add(theme)
      }
    })

    return Array.from(themes)
  }

  private identifyEmergingTrends(reviews: string[]): string[] {
    if (reviews.length < 3) return []

    const recentReviews = reviews.slice(0, Math.floor(reviews.length / 3))
    const olderReviews = reviews.slice(Math.floor(reviews.length / 3))

    const recentThemes = new Set<string>()
    const olderThemes = new Set<string>()

    recentReviews.forEach((review) => {
      const tokens = this.tokenizer.tokenize(review.toLowerCase())
      tokens.forEach((token) => {
        if (token.length > 4) recentThemes.add(token)
      })
    })

    olderReviews.forEach((review) => {
      const tokens = this.tokenizer.tokenize(review.toLowerCase())
      tokens.forEach((token) => {
        if (token.length > 4) olderThemes.add(token)
      })
    })

    // Find themes that appear more frequently in recent reviews
    const emergingTrends = Array.from(recentThemes).filter((theme) => {
      const recentCount = recentReviews.filter((review) =>
        review.toLowerCase().includes(theme)
      ).length
      const olderCount = olderReviews.filter((review) =>
        review.toLowerCase().includes(theme)
      ).length
      return recentCount > olderCount * 2 // Theme appears twice as often in recent reviews
    })

    return emergingTrends.slice(0, 5) // Return top 5 emerging trends
  }

  public async run() {
    this.logger.info('Starting review theme analysis...')

    // Get all sessions with ratings
    const sessions = await Session.query()
      .whereNotNull('actual_ended_at')
      .preload('listing')
      .preload('listingRating')
      .orderBy('created_at', 'desc')

    // Group sessions by listing
    const listingReviews = new Map<string, { name: string; reviews: string[] }>()

    sessions.forEach((session) => {
      if (session.listing && session.listingRating?.review) {
        const listingId = session.listing.id
        if (!listingReviews.has(listingId)) {
          listingReviews.set(listingId, {
            name: session.listing.name,
            reviews: [],
          })
        }
        listingReviews.get(listingId)!.reviews.push(session.listingRating.review)
      }
    })

    const themeSummaries: ThemeSummary[] = []

    // Analyze each listing's reviews
    for (const [_, data] of listingReviews) {
      const { name, reviews } = data

      const positiveThemes = new Set<string>()
      const negativeThemes = new Set<string>()
      const improvementAreas = new Set<string>()

      reviews.forEach((review) => {
        // Extract themes based on sentiment
        const sentiment = this.analyzer.getSentiment(this.tokenizer.tokenize(review))

        if (sentiment > 0.2) {
          this.extractThemes(review, 'positive').forEach((theme) => positiveThemes.add(theme))
        } else if (sentiment < -0.2) {
          this.extractThemes(review, 'negative').forEach((theme) => negativeThemes.add(theme))
        }

        this.extractThemes(review, 'improvement').forEach((theme) => improvementAreas.add(theme))
      })

      themeSummaries.push({
        listingName: name,
        totalReviews: reviews.length,
        positiveThemes: Array.from(positiveThemes),
        negativeThemes: Array.from(negativeThemes),
        improvementAreas: Array.from(improvementAreas),
        emergingTrends: this.identifyEmergingTrends(reviews),
      })
    }

    // Create CSV writer
    const csvWriter = createObjectCsvWriter({
      path: path.join(process.cwd(), 'review_themes_analysis.csv'),
      header: [
        { id: 'listingName', title: 'Listing Name' },
        { id: 'totalReviews', title: 'Total Reviews' },
        { id: 'positiveThemes', title: 'Positive Themes' },
        { id: 'negativeThemes', title: 'Negative Themes' },
        { id: 'improvementAreas', title: 'Areas for Improvement' },
        { id: 'emergingTrends', title: 'Emerging Trends' },
      ],
    })

    // Write summaries to CSV
    await csvWriter.writeRecords(
      themeSummaries.map((summary) => ({
        ...summary,
        positiveThemes: summary.positiveThemes.join(', '),
        negativeThemes: summary.negativeThemes.join(', '),
        improvementAreas: summary.improvementAreas.join(', '),
        emergingTrends: summary.emergingTrends.join(', '),
      }))
    )

    this.logger.success('Review theme analysis completed successfully!')
    this.logger.info(`Analysis saved to: ${path.join(process.cwd(), 'review_themes_analysis.csv')}`)
    this.logger.info(`Analyzed ${themeSummaries.length} listings`)
  }
}
