import { BaseCommand } from '@adonisjs/core/build/standalone'
import Session from 'App/Models/Session'
import { DateTime } from 'luxon'
import nodemailer from 'nodemailer'
import Env from '@ioc:Adonis/Core/Env'

export default class MonitorSessionIssues extends BaseCommand {
  public static commandName = 'monitor:session-issues'
  public static description = 'Monitor and alert on critical session issues'

  public static settings = {
    loadApp: true,
  }

  private criticalKeywords = ['crash', 'bug', 'error', 'unable to use', 'not working', 'broken']

  private async sendAlertEmail(issues: any[]) {
    const transporter = nodemailer.createTransport({
      host: Env.get('SMTP_HOST'),
      port: Env.get('SMTP_PORT'),
      secure: Env.get('SMTP_SECURE', false),
      auth: {
        user: Env.get('SMTP_USERNAME'),
        pass: Env.get('SMTP_PASSWORD'),
      },
    })

    const emailContent = issues
      .map(
        (issue) => `
      Session ID: ${issue.sessionId}
      User: ${issue.username} (${issue.emailAddress})
      Listing: ${issue.listingName}
      App Rating: ${issue.appRating}
      Experience Rating: ${issue.experienceRating}
      Review: ${issue.review}
      Started At: ${issue.startedAt}
      Ended At: ${issue.endedAt}
      Issue Type: ${issue.issueType}
      ------------------------
    `
      )
      .join('\n')

    await transporter.sendMail({
      from: Env.get('MAIL_FROM'),
      to: Env.get('ALERT_EMAIL_RECIPIENTS'),
      subject: 'Critical Session Issues Alert',
      text: `The following critical issues were detected:\n\n${emailContent}`,
    })
  }

  private isCriticalIssue(session: Session): boolean {
    if (!session.listingRating) return false

    const { appRating, experienceRating, review } = session.listingRating

    // Check for low ratings
    if (appRating <= 1 || experienceRating <= 1) return true

    // Check for critical keywords in review
    if (review) {
      const lowerReview = review.toLowerCase()
      return this.criticalKeywords.some((keyword) => lowerReview.includes(keyword))
    }

    return false
  }

  public async run() {
    this.logger.info('Starting session monitoring...')

    // Get sessions from the last 24 hours
    const yesterday = DateTime.now().minus({ hours: 24 })

    const sessions = await Session.query()
      .where('created_at', '>=', yesterday.toSQL())
      .preload('listing')
      .preload('user')
      .preload('listingRating')

    const criticalIssues = sessions
      .filter((session) => this.isCriticalIssue(session))
      .map((session) => ({
        sessionId: session.id,
        username: session.user?.username || 'N/A',
        emailAddress: session.user?.emailAddress || 'N/A',
        listingName: session.listing?.name || 'N/A',
        appRating: session.listingRating?.appRating || 'N/A',
        experienceRating: session.listingRating?.experienceRating || 'N/A',
        review: session.listingRating?.review || 'N/A',
        startedAt: session.startedAt?.toFormat('yyyy-MM-dd HH:mm:ss') || 'N/A',
        endedAt: session.actualEndedAt?.toFormat('yyyy-MM-dd HH:mm:ss') || 'N/A',
        issueType:
          session.listingRating?.appRating <= 1 || session.listingRating?.experienceRating <= 1
            ? 'Low Rating'
            : 'Critical Keywords in Review',
      }))

    if (criticalIssues.length > 0) {
      this.logger.info(`Found ${criticalIssues.length} critical issues`)
      await this.sendAlertEmail(criticalIssues)
      this.logger.success('Alert email sent successfully')
    } else {
      this.logger.success('No critical issues found')
    }
  }
}
