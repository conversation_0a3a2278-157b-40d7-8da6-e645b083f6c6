import { BaseCommand } from '@adonisjs/core/build/standalone'
import Session from 'App/Models/Session'
import natural from 'natural'
import { createObjectCsvWriter } from 'csv-writer'
import path from 'path'

export default class AnalyzeSessionRatings extends BaseCommand {
  public static commandName = 'analyze:session-ratings'
  public static description = 'Analyze session ratings and review sentiment'

  public static settings = {
    loadApp: true,
  }

  private tokenizer = new natural.WordTokenizer()
  private analyzer = new natural.SentimentAnalyzer('English', natural.PorterStemmer, 'afinn')

  private analyzeSentiment(text: string): 'Positive' | 'Negative' | 'Neutral' {
    if (!text) return 'Neutral'

    const tokens = this.tokenizer.tokenize(text)
    const score = this.analyzer.getSentiment(tokens)

    if (score > 0.2) return 'Positive'
    if (score < -0.2) return 'Negative'
    return 'Neutral'
  }

  private extractKeywords(text: string, count: number = 5): string[] {
    if (!text) return []

    const tokens = this.tokenizer.tokenize(text.toLowerCase())
    const wordFreq: { [key: string]: number } = {}

    tokens.forEach((token) => {
      if (token.length > 3) {
        // Ignore very short words
        wordFreq[token] = (wordFreq[token] || 0) + 1
      }
    })

    return Object.entries(wordFreq)
      .sort(([, a], [, b]) => b - a)
      .slice(0, count)
      .map(([word]) => word)
  }

  public async run() {
    this.logger.info('Starting session ratings analysis...')

    // Query sessions with related data
    const sessions = await Session.query()
      .preload('listing')
      .preload('listingRating')
      .orderBy('started_at', 'desc')

    // Group sessions by listing
    const listingStats = new Map()

    sessions.forEach((session) => {
      if (!session.listing) return

      const listingId = session.listing.id
      if (!listingStats.has(listingId)) {
        listingStats.set(listingId, {
          name: session.listing.name,
          ratings: [],
          reviews: [],
          totalSessions: 0,
        })
      }

      const stats = listingStats.get(listingId)
      stats.totalSessions++

      if (session.listingRating) {
        stats.ratings.push({
          appRating: session.listingRating.appRating,
          experienceRating: session.listingRating.experienceRating,
          listingRating: session.listingRating.listingRating,
        })

        if (session.listingRating.review) {
          stats.reviews.push({
            text: session.listingRating.review,
            sentiment: this.analyzeSentiment(session.listingRating.review),
          })
        }
      }
    })

    // Prepare analysis data
    const analysisData = Array.from(listingStats.entries()).map(([_, stats]) => {
      const ratings = stats.ratings
      const reviews = stats.reviews

      const avgAppRating = ratings.reduce((sum, r) => sum + r.appRating, 0) / ratings.length || 0
      const avgExperienceRating =
        ratings.reduce((sum, r) => sum + r.experienceRating, 0) / ratings.length || 0

      const positiveReviews = reviews.filter((r) => r.sentiment === 'Positive')
      const negativeReviews = reviews.filter((r) => r.sentiment === 'Negative')

      const positiveKeywords = this.extractKeywords(positiveReviews.map((r) => r.text).join(' '))

      const negativeKeywords = this.extractKeywords(negativeReviews.map((r) => r.text).join(' '))

      return {
        'Listing Name': stats.name,
        'Total Sessions': stats.totalSessions,
        'Average App Rating': avgAppRating.toFixed(2),
        'Average Experience Rating': avgExperienceRating.toFixed(2),
        'Total Reviews': reviews.length,
        'Positive Reviews': positiveReviews.length,
        'Negative Reviews': negativeReviews.length,
        'Neutral Reviews': reviews.length - positiveReviews.length - negativeReviews.length,
        'Top Positive Keywords': positiveKeywords.join(', '),
        'Top Negative Keywords': negativeKeywords.join(', '),
        'Low Rating Alert': avgExperienceRating < 2.5 ? 'Yes' : 'No',
      }
    })

    // Create CSV writer
    const csvWriter = createObjectCsvWriter({
      path: path.join(process.cwd(), 'session_ratings_analysis.csv'),
      header: [
        { id: 'Listing Name', title: 'Listing Name' },
        { id: 'Total Sessions', title: 'Total Sessions' },
        { id: 'Average App Rating', title: 'Average App Rating' },
        { id: 'Average Experience Rating', title: 'Average Experience Rating' },
        { id: 'Total Reviews', title: 'Total Reviews' },
        { id: 'Positive Reviews', title: 'Positive Reviews' },
        { id: 'Negative Reviews', title: 'Negative Reviews' },
        { id: 'Neutral Reviews', title: 'Neutral Reviews' },
        { id: 'Top Positive Keywords', title: 'Top Positive Keywords' },
        { id: 'Top Negative Keywords', title: 'Top Negative Keywords' },
        { id: 'Low Rating Alert', title: 'Low Rating Alert' },
      ],
    })

    // Write to CSV
    await csvWriter.writeRecords(analysisData)

    this.logger.success('Session ratings analysis completed successfully!')
    this.logger.info(
      `Analysis saved to: ${path.join(process.cwd(), 'session_ratings_analysis.csv')}`
    )
  }
}
