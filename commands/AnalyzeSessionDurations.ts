import { BaseCommand } from '@adonisjs/core/build/standalone'
import Session from 'App/Models/Session'
import { createObjectCsvWriter } from 'csv-writer'
import path from 'path'

interface DurationStats {
  average: number
  standardDeviation: number
  min: number
  max: number
  totalSessions: number
}

interface AnomalyReport {
  listingName: string
  sessionId: string
  username: string
  startedAt: string
  endedAt: string
  duration: number
  averageDuration: number
  standardDeviation: number
  anomalyType: 'Short' | 'Long'
  rating: number | null
  review: string | null
}

export default class AnalyzeSessionDurations extends BaseCommand {
  public static commandName = 'analyze:session-durations'
  public static description = 'Analyze session duration patterns and anomalies'

  public static settings = {
    loadApp: true,
  }

  private calculateStats(durations: number[]): DurationStats {
    if (durations.length === 0) {
      return {
        average: 0,
        standardDeviation: 0,
        min: 0,
        max: 0,
        totalSessions: 0,
      }
    }

    const sum = durations.reduce((a, b) => a + b, 0)
    const average = sum / durations.length
    const squareDiffs = durations.map((duration) => Math.pow(duration - average, 2))
    const standardDeviation = Math.sqrt(squareDiffs.reduce((a, b) => a + b, 0) / durations.length)

    return {
      average,
      standardDeviation,
      min: Math.min(...durations),
      max: Math.max(...durations),
      totalSessions: durations.length,
    }
  }

  private isAnomaly(duration: number, stats: DurationStats): boolean {
    const threshold = 2 // Number of standard deviations
    return Math.abs(duration - stats.average) > threshold * stats.standardDeviation
  }

  public async run() {
    this.logger.info('Starting session duration analysis...')

    // Get all sessions with completed durations
    const sessions = await Session.query()
      .whereNotNull('actual_ended_at')
      .preload('listing')
      .preload('user')
      .preload('listingRating')
      .orderBy('started_at', 'desc')

    // Group sessions by listing
    const listingSessions = new Map<string, Session[]>()
    sessions.forEach((session) => {
      if (session.listing) {
        const listingId = session.listing.id
        if (!listingSessions.has(listingId)) {
          listingSessions.set(listingId, [])
        }
        listingSessions.get(listingId)!.push(session)
      }
    })

    const anomalies: AnomalyReport[] = []
    const listingStats: { [key: string]: DurationStats } = {}

    // Analyze each listing
    for (const [listingId, sessions] of listingSessions) {
      const durations = sessions.map(
        (session) => session.actualEndedAt!.diff(session.startedAt, 'minutes').minutes
      )

      const stats = this.calculateStats(durations)
      listingStats[listingId] = stats

      // Find anomalies
      sessions.forEach((session) => {
        const duration = session.actualEndedAt!.diff(session.startedAt, 'minutes').minutes

        if (this.isAnomaly(duration, stats)) {
          anomalies.push({
            listingName: session.listing?.name || 'N/A',
            sessionId: session.id,
            username: session.user?.username || 'N/A',
            startedAt: session.startedAt.toFormat('yyyy-MM-dd HH:mm:ss'),
            endedAt: session.actualEndedAt!.toFormat('yyyy-MM-dd HH:mm:ss'),
            duration: Math.round(duration),
            averageDuration: Math.round(stats.average),
            standardDeviation: Math.round(stats.standardDeviation),
            anomalyType: duration < stats.average ? 'Short' : 'Long',
            rating: session.listingRating?.experienceRating || null,
            review: session.listingRating?.review || null,
          })
        }
      })
    }

    // Create CSV writer for anomalies
    const csvWriter = createObjectCsvWriter({
      path: path.join(process.cwd(), 'session_duration_anomalies.csv'),
      header: [
        { id: 'listingName', title: 'Listing Name' },
        { id: 'sessionId', title: 'Session ID' },
        { id: 'username', title: 'Username' },
        { id: 'startedAt', title: 'Started At' },
        { id: 'endedAt', title: 'Ended At' },
        { id: 'duration', title: 'Duration (minutes)' },
        { id: 'averageDuration', title: 'Average Duration (minutes)' },
        { id: 'standardDeviation', title: 'Standard Deviation' },
        { id: 'anomalyType', title: 'Anomaly Type' },
        { id: 'rating', title: 'Rating' },
        { id: 'review', title: 'Review' },
      ],
    })

    // Write anomalies to CSV
    await csvWriter.writeRecords(anomalies)

    // Create CSV writer for listing stats
    const statsWriter = createObjectCsvWriter({
      path: path.join(process.cwd(), 'listing_duration_stats.csv'),
      header: [
        { id: 'listingName', title: 'Listing Name' },
        { id: 'totalSessions', title: 'Total Sessions' },
        { id: 'averageDuration', title: 'Average Duration (minutes)' },
        { id: 'standardDeviation', title: 'Standard Deviation' },
        { id: 'minDuration', title: 'Min Duration (minutes)' },
        { id: 'maxDuration', title: 'Max Duration (minutes)' },
      ],
    })

    // Prepare listing stats data
    const listingStatsData = Array.from(listingSessions.entries()).map(([listingId, sessions]) => ({
      listingName: sessions[0].listing?.name || 'N/A',
      totalSessions: listingStats[listingId].totalSessions,
      averageDuration: Math.round(listingStats[listingId].average),
      standardDeviation: Math.round(listingStats[listingId].standardDeviation),
      minDuration: Math.round(listingStats[listingId].min),
      maxDuration: Math.round(listingStats[listingId].max),
    }))

    // Write listing stats to CSV
    await statsWriter.writeRecords(listingStatsData)

    this.logger.success('Session duration analysis completed successfully!')
    this.logger.info(
      `Anomalies saved to: ${path.join(process.cwd(), 'session_duration_anomalies.csv')}`
    )
    this.logger.info(
      `Listing stats saved to: ${path.join(process.cwd(), 'listing_duration_stats.csv')}`
    )
    this.logger.info(`Found ${anomalies.length} anomalous sessions`)
  }
}
