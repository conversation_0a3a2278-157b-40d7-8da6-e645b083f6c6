import { BaseCommand } from '@adonisjs/core/build/standalone'
import Session from 'App/Models/Session'
import { createObjectCsvWriter } from 'csv-writer'
import path from 'path'

export default class GenerateSessionReport extends BaseCommand {
  public static commandName = 'generate:session-report'
  public static description = 'Generate a comprehensive CSV report of session information'

  public static settings = {
    loadApp: true,
  }

  public async run() {
    this.logger.info('Starting to generate session report...')

    // Query sessions with related data
    const sessions = await Session.query()
      .preload('listing')
      .preload('user')
      .preload('listingRating')
      .orderBy('started_at', 'desc')

    // Prepare CSV data
    const csvData = sessions.map((session) => ({
      'Listing Name': session.listing?.name || 'N/A',
      'Username': session.user?.username || 'N/A',
      'Email Address': session.user?.emailAddress || 'N/A',
      'App Rating': session.listingRating?.appRating || 'N/A',
      'Experience Rating': session.listingRating?.experienceRating || 'N/A',
      'Listing Rating': session.listingRating?.listingRating || 'N/A',
      'Review': session.listingRating?.review || 'N/A',
      'Started At': session.startedAt?.toFormat('yyyy-MM-dd HH:mm:ss') || 'N/A',
      'Actual Ended At': session.actualEndedAt?.toFormat('yyyy-MM-dd HH:mm:ss') || 'N/A',
      'Usage Duration (minutes)': session.actualEndedAt
        ? Math.round(session.actualEndedAt.diff(session.startedAt, 'minutes').minutes)
        : 'N/A',
    }))

    // Create CSV writer
    const csvWriter = createObjectCsvWriter({
      path: path.join(process.cwd(), 'session_report.csv'),
      header: [
        { id: 'Listing Name', title: 'Listing Name' },
        { id: 'Username', title: 'Username' },
        { id: 'Email Address', title: 'Email Address' },
        { id: 'App Rating', title: 'App Rating' },
        { id: 'Experience Rating', title: 'Experience Rating' },
        { id: 'Listing Rating', title: 'Listing Rating' },
        { id: 'Review', title: 'Review' },
        { id: 'Started At', title: 'Started At' },
        { id: 'Actual Ended At', title: 'Actual Ended At' },
        { id: 'Usage Duration (minutes)', title: 'Usage Duration (minutes)' },
      ],
    })

    // Write to CSV
    await csvWriter.writeRecords(csvData)

    this.logger.success('Session report generated successfully!')
    this.logger.info(`Report saved to: ${path.join(process.cwd(), 'session_report.csv')}`)
  }
}
