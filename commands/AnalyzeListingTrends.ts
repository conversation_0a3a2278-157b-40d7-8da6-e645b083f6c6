import { BaseCommand } from '@adonisjs/core/build/standalone'
import Session from 'App/Models/Session'
import { DateTime } from 'luxon'
import { createObjectCsvWriter } from 'csv-writer'
import path from 'path'

interface UsageStats {
  listingName: string
  totalSessions: number
  totalDuration: number
  averageDuration: number
  peakHours: string[]
  peakDays: string[]
  trend: 'up' | 'down' | 'stable'
  trendPercentage: number
}

export default class AnalyzeListingTrends extends BaseCommand {
  public static commandName = 'analyze:listing-trends'
  public static description = 'Analyze listing popularity and usage trends'

  public static settings = {
    loadApp: true,
  }

  private readonly TREND_THRESHOLD = 0.2 // 20% change to consider a trend
  private readonly PEAK_HOURS_THRESHOLD = 0.1 // 10% above average to consider peak
  private readonly PEAK_DAYS_THRESHOLD = 0.1 // 10% above average to consider peak

  private calculateTrend(
    recentSessions: number,
    olderSessions: number
  ): { trend: 'up' | 'down' | 'stable'; percentage: number } {
    if (olderSessions === 0) return { trend: 'stable', percentage: 0 }

    const percentage = (recentSessions - olderSessions) / olderSessions

    if (percentage > this.TREND_THRESHOLD) return { trend: 'up', percentage }
    if (percentage < -this.TREND_THRESHOLD) return { trend: 'down', percentage }
    return { trend: 'stable', percentage }
  }

  private findPeakHours(sessions: Session[]): string[] {
    const hourlyCounts = new Array(24).fill(0)

    sessions.forEach((session) => {
      const hour = session.startedAt.hour
      hourlyCounts[hour]++
    })

    const average = hourlyCounts.reduce((a, b) => a + b, 0) / 24
    const threshold = average * (1 + this.PEAK_HOURS_THRESHOLD)

    return hourlyCounts
      .map((count, hour) => ({ hour, count }))
      .filter(({ count }) => count > threshold)
      .map(({ hour }) => `${hour.toString().padStart(2, '0')}:00`)
  }

  private findPeakDays(sessions: Session[]): string[] {
    const dayCounts = new Array(7).fill(0)
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

    sessions.forEach((session) => {
      const day = session.startedAt.weekday % 7
      dayCounts[day]++
    })

    const average = dayCounts.reduce((a, b) => a + b, 0) / 7
    const threshold = average * (1 + this.PEAK_DAYS_THRESHOLD)

    return dayCounts
      .map((count, day) => ({ day, count }))
      .filter(({ count }) => count > threshold)
      .map(({ day }) => days[day])
  }

  public async run() {
    this.logger.info('Starting listing trend analysis...')

    // Get all sessions
    const sessions = await Session.query()
      .whereNotNull('actual_ended_at')
      .preload('listing')
      .orderBy('started_at', 'desc')

    // Group sessions by listing
    const listingSessions = new Map<string, Session[]>()
    sessions.forEach((session) => {
      if (session.listing) {
        const listingId = session.listing.id
        if (!listingSessions.has(listingId)) {
          listingSessions.set(listingId, [])
        }
        listingSessions.get(listingId)!.push(session)
      }
    })

    const usageStats: UsageStats[] = []

    // Analyze each listing
    for (const [_, sessions] of listingSessions) {
      const totalSessions = sessions.length
      const totalDuration = sessions.reduce((sum, session) => {
        return sum + session.actualEndedAt!.diff(session.startedAt, 'minutes').minutes
      }, 0)
      const averageDuration = totalDuration / totalSessions

      // Split sessions into recent and older periods
      const now = DateTime.now()
      const recentSessions = sessions.filter((s) => s.startedAt >= now.minus({ months: 1 }))
      const olderSessions = sessions.filter(
        (s) => s.startedAt >= now.minus({ months: 2 }) && s.startedAt < now.minus({ months: 1 })
      )

      const { trend, percentage } = this.calculateTrend(recentSessions.length, olderSessions.length)

      usageStats.push({
        listingName: sessions[0].listing!.name,
        totalSessions,
        totalDuration: Math.round(totalDuration),
        averageDuration: Math.round(averageDuration),
        peakHours: this.findPeakHours(sessions),
        peakDays: this.findPeakDays(sessions),
        trend,
        trendPercentage: Math.round(percentage * 100),
      })
    }

    // Create CSV writer
    const csvWriter = createObjectCsvWriter({
      path: path.join(process.cwd(), 'listing_trends_analysis.csv'),
      header: [
        { id: 'listingName', title: 'Listing Name' },
        { id: 'totalSessions', title: 'Total Sessions' },
        { id: 'totalDuration', title: 'Total Duration (minutes)' },
        { id: 'averageDuration', title: 'Average Duration (minutes)' },
        { id: 'peakHours', title: 'Peak Hours' },
        { id: 'peakDays', title: 'Peak Days' },
        { id: 'trend', title: 'Usage Trend' },
        { id: 'trendPercentage', title: 'Trend Percentage' },
      ],
    })

    // Write stats to CSV
    await csvWriter.writeRecords(
      usageStats.map((stats) => ({
        ...stats,
        peakHours: stats.peakHours.join(', '),
        peakDays: stats.peakDays.join(', '),
      }))
    )

    this.logger.success('Listing trend analysis completed successfully!')
    this.logger.info(
      `Analysis saved to: ${path.join(process.cwd(), 'listing_trends_analysis.csv')}`
    )
    this.logger.info(`Analyzed ${usageStats.length} listings`)

    // Log significant trends
    const significantTrends = usageStats.filter(
      (stats) => Math.abs(stats.trendPercentage) >= this.TREND_THRESHOLD * 100
    )

    if (significantTrends.length > 0) {
      this.logger.info('\nSignificant trends detected:')
      significantTrends.forEach((stats) => {
        this.logger.info(`${stats.listingName}: ${stats.trend} (${stats.trendPercentage}%)`)
      })
    }
  }
}
