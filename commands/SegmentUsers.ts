import { BaseCommand } from '@adonisjs/core/build/standalone'
import Session from 'App/Models/Session'
import { DateTime } from 'luxon'
import { createObjectCsvWriter } from 'csv-writer'
import path from 'path'
import User from 'App/Models/User'

interface UserSegment {
  userId: string
  username: string
  emailAddress: string
  totalSessions: number
  totalDuration?: number
  recentSessions?: number
  lowRatings?: number
  detailedReviews?: number
  lastActivity: string
  segment: string
}

interface Segments {
  powerUsers: UserSegment[]
  atRiskUsers: UserSegment[]
  inactiveUsers: UserSegment[]
}

export default class SegmentUsers extends BaseCommand {
  public static commandName = 'segment:users'
  public static description = 'Segment users based on activity and feedback'

  public static settings = {
    loadApp: true,
  }

  private readonly INACTIVE_DAYS = 30
  private readonly ACTIVITY_DROP_THRESHOLD = 0.5 // 50% drop in activity

  private async getUserStats(userId: string) {
    const sessions = await Session.query()
      .where('user_id', userId)
      .preload('listingRating')
      .orderBy('created_at', 'desc')

    const totalSessions = sessions.length
    const totalDuration = sessions.reduce((sum, session) => {
      if (session.actualEndedAt) {
        return sum + session.actualEndedAt.diff(session.startedAt, 'minutes').minutes
      }
      return sum
    }, 0)

    const recentSessions = sessions.filter(
      (session) => session.createdAt >= DateTime.now().minus({ days: this.INACTIVE_DAYS })
    )

    const recentDuration = recentSessions.reduce((sum, session) => {
      if (session.actualEndedAt) {
        return sum + session.actualEndedAt.diff(session.startedAt, 'minutes').minutes
      }
      return sum
    }, 0)

    const lowRatings = sessions.filter(
      (session) =>
        session.listingRating &&
        (session.listingRating.appRating <= 2 || session.listingRating.experienceRating <= 2)
    ).length

    const detailedReviews = sessions.filter(
      (session) =>
        session.listingRating &&
        session.listingRating.review &&
        session.listingRating.review.length > 50
    ).length

    return {
      totalSessions,
      totalDuration,
      recentSessions: recentSessions.length,
      recentDuration,
      lowRatings,
      detailedReviews,
      lastActivity: sessions[0]?.createdAt || null,
    }
  }

  public async run() {
    this.logger.info('Starting user segmentation...')

    // Get all unique users from sessions
    const sessions = await Session.query().preload('user').orderBy('created_at', 'desc')

    const uniqueUsers = new Map<string, User>()
    sessions.forEach((session) => {
      if (session.user && !uniqueUsers.has(session.user.id)) {
        uniqueUsers.set(session.user.id, session.user)
      }
    })

    const segments: Segments = {
      powerUsers: [],
      atRiskUsers: [],
      inactiveUsers: [],
    }

    // Analyze each user
    for (const [userId, user] of uniqueUsers) {
      const stats = await this.getUserStats(userId)

      // Power Users
      if (
        stats.totalSessions >= 10 &&
        stats.totalDuration >= 300 && // 5 hours total
        stats.detailedReviews >= 3
      ) {
        segments.powerUsers.push({
          userId: user.id,
          username: user.username,
          emailAddress: user.emailAddress,
          totalSessions: stats.totalSessions,
          totalDuration: Math.round(stats.totalDuration),
          detailedReviews: stats.detailedReviews,
          lastActivity: stats.lastActivity?.toFormat('yyyy-MM-dd HH:mm:ss') || 'N/A',
          segment: 'Power User',
        })
      }

      // At-Risk Users
      if (
        stats.recentSessions > 0 &&
        stats.recentSessions < stats.totalSessions * this.ACTIVITY_DROP_THRESHOLD &&
        stats.lowRatings >= 2
      ) {
        segments.atRiskUsers.push({
          userId: user.id,
          username: user.username,
          emailAddress: user.emailAddress,
          totalSessions: stats.totalSessions,
          recentSessions: stats.recentSessions,
          lowRatings: stats.lowRatings,
          lastActivity: stats.lastActivity?.toFormat('yyyy-MM-dd HH:mm:ss') || 'N/A',
          segment: 'At-Risk User',
        })
      }

      // Inactive Users
      if (
        !stats.lastActivity ||
        stats.lastActivity < DateTime.now().minus({ days: this.INACTIVE_DAYS })
      ) {
        segments.inactiveUsers.push({
          userId: user.id,
          username: user.username,
          emailAddress: user.emailAddress,
          totalSessions: stats.totalSessions,
          lastActivity: stats.lastActivity?.toFormat('yyyy-MM-dd HH:mm:ss') || 'N/A',
          segment: 'Inactive User',
        })
      }
    }

    // Combine all segments
    const allSegments = [...segments.powerUsers, ...segments.atRiskUsers, ...segments.inactiveUsers]

    // Create CSV writer
    const csvWriter = createObjectCsvWriter({
      path: path.join(process.cwd(), 'user_segments.csv'),
      header: [
        { id: 'userId', title: 'User ID' },
        { id: 'username', title: 'Username' },
        { id: 'emailAddress', title: 'Email Address' },
        { id: 'segment', title: 'Segment' },
        { id: 'totalSessions', title: 'Total Sessions' },
        { id: 'totalDuration', title: 'Total Duration (minutes)' },
        { id: 'recentSessions', title: 'Recent Sessions' },
        { id: 'lowRatings', title: 'Low Ratings' },
        { id: 'detailedReviews', title: 'Detailed Reviews' },
        { id: 'lastActivity', title: 'Last Activity' },
      ],
    })

    // Write to CSV
    await csvWriter.writeRecords(allSegments)

    this.logger.success('User segmentation completed successfully!')
    this.logger.info(`Segmentation saved to: ${path.join(process.cwd(), 'user_segments.csv')}`)
    this.logger.info(`Found ${segments.powerUsers.length} power users`)
    this.logger.info(`Found ${segments.atRiskUsers.length} at-risk users`)
    this.logger.info(`Found ${segments.inactiveUsers.length} inactive users`)
  }
}
