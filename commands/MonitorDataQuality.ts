import { BaseCommand } from '@adonisjs/core/build/standalone'
import Session from 'App/Models/Session'
import { DateTime } from 'luxon'
import { createObjectCsvWriter } from 'csv-writer'
import path from 'path'

interface DataQualityIssue {
  sessionId: string
  listingName: string
  username: string
  issueType: string
  details: string
  createdAt: string
}

export default class MonitorDataQuality extends BaseCommand {
  public static commandName = 'monitor:data-quality'
  public static description = 'Monitor data quality and completeness of session records'

  public static settings = {
    loadApp: true,
  }

  private validateTimestamp(timestamp: DateTime | null): boolean {
    if (!timestamp) return false
    return timestamp.isValid
  }

  private validateDuration(startedAt: DateTime, endedAt: DateTime | null): boolean {
    if (!endedAt) return false
    if (!this.validateTimestamp(startedAt) || !this.validateTimestamp(endedAt)) return false
    return endedAt > startedAt
  }

  public async run() {
    this.logger.info('Starting data quality monitoring...')

    // Get all sessions
    const sessions = await Session.query()
      .preload('listing')
      .preload('user')
      .preload('listingRating')
      .orderBy('created_at', 'desc')

    const issues: DataQualityIssue[] = []

    // Check each session for data quality issues
    sessions.forEach((session) => {
      // Check for missing critical data
      if (!session.listing) {
        issues.push({
          sessionId: session.id,
          listingName: 'N/A',
          username: session.user?.username || 'N/A',
          issueType: 'Missing Listing',
          details: 'Session has no associated listing',
          createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
        })
      }

      if (!session.user) {
        issues.push({
          sessionId: session.id,
          listingName: session.listing?.name || 'N/A',
          username: 'N/A',
          issueType: 'Missing User',
          details: 'Session has no associated user',
          createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
        })
      }

      // Validate timestamps
      if (!this.validateTimestamp(session.startedAt)) {
        issues.push({
          sessionId: session.id,
          listingName: session.listing?.name || 'N/A',
          username: session.user?.username || 'N/A',
          issueType: 'Invalid Start Time',
          details: 'Session start time is invalid',
          createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
        })
      }

      if (session.actualEndedAt && !this.validateTimestamp(session.actualEndedAt)) {
        issues.push({
          sessionId: session.id,
          listingName: session.listing?.name || 'N/A',
          username: session.user?.username || 'N/A',
          issueType: 'Invalid End Time',
          details: 'Session end time is invalid',
          createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
        })
      }

      // Validate duration
      if (
        session.actualEndedAt &&
        !this.validateDuration(session.startedAt, session.actualEndedAt)
      ) {
        issues.push({
          sessionId: session.id,
          listingName: session.listing?.name || 'N/A',
          username: session.user?.username || 'N/A',
          issueType: 'Invalid Duration',
          details: 'Session end time is before start time',
          createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
        })
      }

      // Check for missing ratings where expected
      if (session.actualEndedAt && !session.listingRating) {
        issues.push({
          sessionId: session.id,
          listingName: session.listing?.name || 'N/A',
          username: session.user?.username || 'N/A',
          issueType: 'Missing Rating',
          details: 'Completed session has no rating',
          createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
        })
      }

      // Check for incomplete user identifiers
      if (session.user && !session.user.username && !session.user.emailAddress) {
        issues.push({
          sessionId: session.id,
          listingName: session.listing?.name || 'N/A',
          username: 'N/A',
          issueType: 'Incomplete User Data',
          details: 'User has neither username nor email address',
          createdAt: session.createdAt.toFormat('yyyy-MM-dd HH:mm:ss'),
        })
      }
    })

    // Create CSV writer
    const csvWriter = createObjectCsvWriter({
      path: path.join(process.cwd(), 'data_quality_issues.csv'),
      header: [
        { id: 'sessionId', title: 'Session ID' },
        { id: 'listingName', title: 'Listing Name' },
        { id: 'username', title: 'Username' },
        { id: 'issueType', title: 'Issue Type' },
        { id: 'details', title: 'Details' },
        { id: 'createdAt', title: 'Created At' },
      ],
    })

    // Write issues to CSV
    await csvWriter.writeRecords(issues)

    // Generate summary
    const issueTypes = new Map<string, number>()
    issues.forEach((issue) => {
      issueTypes.set(issue.issueType, (issueTypes.get(issue.issueType) || 0) + 1)
    })

    this.logger.success('Data quality monitoring completed successfully!')
    this.logger.info(`Issues saved to: ${path.join(process.cwd(), 'data_quality_issues.csv')}`)
    this.logger.info(`Found ${issues.length} data quality issues:`)
    issueTypes.forEach((count, type) => {
      this.logger.info(`- ${type}: ${count} issues`)
    })
  }
}
