import { BaseCommand } from '@adonisjs/core/build/standalone'
import MqttService from 'App/Services/MqttService'
import Logger from '@ioc:Adonis/Core/Logger'

export default class TestMqtt extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'test:mqtt'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'Test MQTT integration functionality'

  public static settings = {
    /**
     * Set the following value to true, if you want to load the application
     * before running the command. Don't forget to call `node ace generate:manifest` 
     * afterwards.
     */
    loadApp: true,

    /**
     * Set the following value to true, if you want this command to keep running until
     * you manually decide to exit the process. Don't forget to call 
     * `node ace generate:manifest` afterwards.
     */
    stayAlive: false,
  }

  public async run() {
    this.logger.info('🧪 Starting MQTT Integration Test...')

    try {
      // Create MQTT service instance
      const mqttService = new MqttService()

      // Wait for connection
      await this.waitForConnection(mqttService)

      // Test session publishing
      await this.testSessionPublishing(mqttService)

      // Test listing status publishing
      await this.testListingStatusPublishing(mqttService)

      // Test user notification publishing
      await this.testUserNotificationPublishing(mqttService)

      // Test system health publishing
      await this.testSystemHealthPublishing(mqttService)

      this.logger.info('✅ All MQTT tests completed successfully!')

      // Disconnect
      await mqttService.disconnect()

    } catch (error) {
      this.logger.error('❌ MQTT test failed:', error)
      this.exitCode = 1
    }
  }

  private async waitForConnection(mqttService: MqttService, maxWait: number = 10000): Promise<void> {
    const startTime = Date.now()
    
    while (!mqttService.isClientConnected() && (Date.now() - startTime) < maxWait) {
      this.logger.info('⏳ Waiting for MQTT connection...')
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    if (!mqttService.isClientConnected()) {
      throw new Error('Failed to connect to MQTT broker within timeout')
    }

    this.logger.info('✅ Connected to MQTT broker')
  }

  private async testSessionPublishing(mqttService: MqttService): Promise<void> {
    this.logger.info('📤 Testing session publishing...')

    const testSessionData = {
      id: 'test-session-' + Date.now(),
      userId: 'test-user-123',
      listingId: 'test-listing-456',
      startedAt: new Date().toISOString(),
      expectedEndedAt: new Date(Date.now() + 3600000).toISOString()
    }

    // Test session created
    await mqttService.publishSessionCreated(testSessionData.id, testSessionData)
    this.logger.info('✅ Published session created')

    // Test session updated
    await mqttService.publishSessionUpdated(testSessionData.id, {
      ...testSessionData,
      expectedEndedAt: new Date(Date.now() + 7200000).toISOString() // Extended by 1 hour
    })
    this.logger.info('✅ Published session updated')

    // Test session ended
    await mqttService.publishSessionEnded(testSessionData.id, {
      ...testSessionData,
      actualEndedAt: new Date().toISOString()
    })
    this.logger.info('✅ Published session ended')

    // Test session cleanup
    await mqttService.publishSessionCleanup(testSessionData.id, testSessionData)
    this.logger.info('✅ Published session cleanup')
  }

  private async testListingStatusPublishing(mqttService: MqttService): Promise<void> {
    this.logger.info('📤 Testing listing status publishing...')

    const testListingId = 'test-listing-' + Date.now()

    // Test different status updates
    const statuses = ['idle', 'occupied', 'disinfecting']
    
    for (const status of statuses) {
      await mqttService.publishListingStatus(testListingId, status, {
        previousStatus: 'idle',
        updatedBy: 'test-system'
      })
      this.logger.info(`✅ Published listing status: ${status}`)
    }

    // Test listing availability
    await mqttService.publishListingAvailability(testListingId, {
      available: true,
      nextAvailableTime: new Date(Date.now() + 1800000).toISOString() // 30 minutes
    })
    this.logger.info('✅ Published listing availability')
  }

  private async testUserNotificationPublishing(mqttService: MqttService): Promise<void> {
    this.logger.info('📤 Testing user notification publishing...')

    const testUserId = 'test-user-' + Date.now()

    await mqttService.publishUserNotification(testUserId, {
      userId: testUserId,
      title: 'Test Notification',
      message: 'This is a test notification from MQTT integration test',
      type: 'info',
      timestamp: new Date().toISOString()
    })
    this.logger.info('✅ Published user notification')

    // Test user session update
    await mqttService.publishUserSession(testUserId, {
      id: 'test-session-' + Date.now(),
      userId: testUserId,
      listingId: 'test-listing-123',
      startedAt: new Date().toISOString(),
      expectedEndedAt: new Date(Date.now() + 3600000).toISOString()
    })
    this.logger.info('✅ Published user session')
  }

  private async testSystemHealthPublishing(mqttService: MqttService): Promise<void> {
    this.logger.info('📤 Testing system health publishing...')

    await mqttService.publishSystemHealth({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'healthy',
        redis: 'healthy',
        mqtt: 'healthy'
      },
      uptime: process.uptime()
    })
    this.logger.info('✅ Published system health')

    await mqttService.publishSystemMetrics({
      timestamp: new Date().toISOString(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      activeConnections: 42,
      messagesProcessed: 1337
    })
    this.logger.info('✅ Published system metrics')
  }
}
